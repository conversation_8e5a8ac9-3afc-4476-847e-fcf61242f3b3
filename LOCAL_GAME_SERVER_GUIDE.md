# GSXDB 本地游戏服务器环境搭建指南

## 📋 概览

本指南将帮助您在本地环境中搭建和运行GSXDB游戏服务器，支持Windows、Linux和macOS系统。

## 🎯 功能特性

- ✅ **一键启动**: 自动检查环境、设置服务器并启动
- ✅ **智能诊断**: 实时监控日志并自动诊断问题
- ✅ **自动修复**: 支持自动修复常见启动问题
- ✅ **跨平台**: 支持Windows、Linux、macOS
- ✅ **完整日志**: 详细的启动和运行日志
- ✅ **状态监控**: 实时查看服务器状态

## 🚀 快速开始

### 方法一：一键启动（推荐）

```bash
# 给脚本执行权限
chmod +x start_local_game_server.sh

# 一键启动（包含环境检查、设置、启动、监控）
./start_local_game_server.sh
```

### 方法二：分步执行

```bash
# 1. 检查环境
python3 scripts/tools/check_game_environment.py

# 2. 设置本地环境
./scripts/game/local_game_server.sh setup

# 3. 启动游戏服务器
./scripts/game/local_game_server.sh start

# 4. 监控日志
python3 scripts/tools/game_log_monitor.py monitor --auto-fix
```

## 📋 系统要求

### 最低要求
- **操作系统**: Windows 10+, Ubuntu 18.04+, macOS 10.14+
- **内存**: 2GB RAM
- **磁盘空间**: 5GB 可用空间
- **Java**: Java 8 或更高版本
- **Python**: Python 3.6 或更高版本

### 推荐配置
- **内存**: 4GB+ RAM
- **磁盘空间**: 10GB+ 可用空间
- **Java**: Java 11 或更高版本
- **网络**: 稳定的网络连接

## 🔧 环境准备

### 1. 安装Java

#### Windows
1. 下载Java JDK: https://adoptopenjdk.net/
2. 安装并配置JAVA_HOME环境变量
3. 将Java bin目录添加到PATH

#### Linux (Ubuntu/Debian)
```bash
sudo apt-get update
sudo apt-get install openjdk-8-jdk
```

#### Linux (CentOS/RHEL)
```bash
sudo yum install java-1.8.0-openjdk-devel
```

#### macOS
```bash
brew install openjdk@8
```

### 2. 安装Python

#### Windows
1. 下载Python: https://www.python.org/downloads/
2. 安装时勾选"Add Python to PATH"

#### Linux
```bash
# Ubuntu/Debian
sudo apt-get install python3 python3-pip

# CentOS/RHEL
sudo yum install python3 python3-pip
```

#### macOS
```bash
brew install python3
```

### 3. 安装网络工具

#### Linux
```bash
# Ubuntu/Debian
sudo apt-get install net-tools lsof

# CentOS/RHEL
sudo yum install net-tools lsof
```

#### Windows/macOS
通常已包含所需工具

## 📁 项目结构

```
gsxdb_mt3_mly/
├── centos7.6_game_server/          # 游戏服务器源文件
│   ├── bin/qd                      # 原始qd管理脚本
│   └── game/                       # 游戏文件
│       ├── common/                 # 公共服务
│       └── server1/                # 大区1文件
├── scripts/                        # 新建的脚本管理系统
│   ├── game/
│   │   └── local_game_server.sh    # 本地服务器管理脚本
│   └── tools/
│       ├── check_game_environment.py  # 环境检查脚本
│       └── game_log_monitor.py     # 日志监控脚本
├── local_game_env/                 # 本地运行环境（自动创建）
│   ├── common/                     # 公共服务
│   ├── server1/                    # 大区1
│   ├── logs/                       # 日志文件
│   └── qd.sh                       # 适配的qd脚本
└── start_local_game_server.sh      # 一键启动脚本
```

## 🎮 使用说明

### 启动选项

```bash
# 完整启动（推荐）
./start_local_game_server.sh

# 仅检查环境
./start_local_game_server.sh --check-only

# 仅设置环境
./start_local_game_server.sh --setup-only

# 启动但不监控日志
./start_local_game_server.sh --no-monitor
```

### 服务器管理

```bash
# 查看服务状态
./scripts/game/local_game_server.sh status

# 启动服务器
./scripts/game/local_game_server.sh start

# 停止服务器
./scripts/game/local_game_server.sh stop

# 查看日志
./scripts/game/local_game_server.sh logs

# 清理环境
./scripts/game/local_game_server.sh clean
```

### 日志监控

```bash
# 实时监控（自动修复）
python3 scripts/tools/game_log_monitor.py monitor --auto-fix

# 实时监控（详细输出）
python3 scripts/tools/game_log_monitor.py monitor --verbose

# 分析现有日志
python3 scripts/tools/game_log_monitor.py analyze
```

## 🔍 端口说明

| 服务 | 端口 | 说明 |
|------|------|------|
| 名称服务 | 22200 | 名称服务器端口 |
| SDK服务 | 29200 | SDK服务器端口 |
| 大区1 GM | 41001 | 大区1 GM管理端口 |
| 网关服务 | 42001 | 大区1网关端口 |
| 游戏服务 | 43001 | 大区1游戏端口 |
| 代理服务 | 44001 | 大区1代理端口 |
| 数据库服务 | 45001 | 大区1数据库端口 |

## 🐛 常见问题解决

### 1. Java相关问题

**问题**: `java: command not found`
**解决**: 
```bash
# 检查Java是否安装
java -version

# 如果未安装，请按照上述环境准备步骤安装Java
```

**问题**: `OutOfMemoryError: Java heap space`
**解决**: 
- 自动修复：启动时使用 `--auto-fix` 参数
- 手动修复：增加JVM内存参数 `-Xmx2048m`

### 2. 端口冲突问题

**问题**: `Address already in use`
**解决**:
```bash
# 查看端口占用
netstat -tlnp | grep :端口号

# 终止占用进程
kill -9 PID

# 或使用自动修复功能
```

### 3. 权限问题

**问题**: `Permission denied`
**解决**:
```bash
# Linux/macOS
chmod -R 755 local_game_env/
find local_game_env/ -name "*.sh" -exec chmod +x {} \;

# 或使用自动修复功能
```

### 4. 文件缺失问题

**问题**: 游戏文件不存在
**解决**:
1. 确保 `centos7.6_game_server` 目录存在
2. 检查必要文件是否完整
3. 重新运行环境检查

## 📊 日志文件位置

| 日志类型 | 位置 | 说明 |
|----------|------|------|
| 启动日志 | `local_game_env/logs/startup.log` | 脚本启动日志 |
| QD脚本日志 | `local_game_env/logs/qd.log` | QD脚本执行日志 |
| 游戏服务器 | `local_game_env/server1/game_server/logs/game.log` | 游戏服务器日志 |
| SDK服务 | `local_game_env/common/logs/sdk.log` | SDK服务日志 |
| 名称服务 | `local_game_env/common/logs/ns.log` | 名称服务日志 |

## 🔧 高级配置

### 自定义JVM参数

编辑 `local_game_env/qd.sh` 文件，修改JVM参数：

```bash
# 示例：增加内存和GC优化
JAVA_OPTS="-Xms1024m -Xmx2048m -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

### 自定义端口配置

如果需要修改端口，请编辑相应的配置文件：
- 游戏服务器: `local_game_env/server1/game_server/gs.xio.xml`
- 网关服务: `local_game_env/server1/gate_server/gate.conf`
- 代理服务: `local_game_env/server1/proxy_server/proxy.conf`

## 🚨 故障排除

### 1. 服务启动失败

1. 检查日志文件中的错误信息
2. 运行环境检查: `python3 scripts/tools/check_game_environment.py`
3. 分析日志: `python3 scripts/tools/game_log_monitor.py analyze`
4. 检查端口占用: `netstat -tlnp`

### 2. 连接问题

1. 确认所有服务都已启动
2. 检查防火墙设置
3. 验证端口是否正确监听
4. 查看网络配置

### 3. 性能问题

1. 监控系统资源使用情况
2. 调整JVM内存参数
3. 检查磁盘空间
4. 优化数据库配置

## 📞 技术支持

如果遇到问题，请：

1. **查看日志**: 使用日志监控工具分析问题
2. **运行诊断**: 使用环境检查工具
3. **查看文档**: 参考本指南的故障排除部分
4. **收集信息**: 准备错误日志和系统信息

## 📝 更新日志

### v1.0.0 (2025-07-30)
- 初始版本发布
- 支持一键启动本地游戏服务器
- 集成环境检查和日志监控
- 支持自动问题诊断和修复
- 完整的跨平台支持

---

**🎮 祝您游戏开发愉快！**
