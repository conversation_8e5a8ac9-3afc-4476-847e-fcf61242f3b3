# 项目规则 | Project Rules

## 📋 概述 | Overview

本文档定义了GSXDB游戏服务器项目的完整规则体系，包括文档管理、代码规范、命名标准、工作流程等所有方面的规则。

This document defines the complete rule system for the GSXDB Game Server project, including rules for documentation management, code standards, naming conventions, workflows, and all other aspects.

**版本 | Version**: 2.0.0  
**生效日期 | Effective Date**: 2025-07-20  
**适用范围 | Scope**: 所有项目成员和贡献者 All Project Members and Contributors  
**强制执行 | Enforcement**: 必须遵守 Mandatory Compliance

---

## 🎯 核心原则 | Core Principles

### 基本原则 | Basic Principles

1. **标准化优先** - 所有工作必须遵循既定标准
2. **双语支持** - 重要内容必须提供中英文对照
3. **质量第一** - 质量优于速度，确保高标准交付
4. **团队协作** - 促进团队协作和知识共享
5. **持续改进** - 不断优化流程和标准

### 价值观 | Values

- **专业性** - 保持专业的工作态度和标准
- **责任感** - 对自己的工作负责
- **创新性** - 鼓励创新和改进
- **开放性** - 开放沟通和知识分享

---

## 📚 文档管理规则 | Documentation Management Rules

### 1. 文档命名规范 | Document Naming Standards

#### 强制规则 | Mandatory Rules

**R-DOC-001**: 所有文档文件必须使用中英文双语命名格式
```
格式: 英文名称_中文名称.扩展名
示例: README_项目说明.md
```

**R-DOC-002**: 目录命名必须包含序号和双语名称
```
格式: 序号_英文名称_中文名称/
示例: 01_overview_项目概述/
```

**R-DOC-003**: 文档标题必须与文件名保持一致
```
文件名: API_SPECIFICATION_接口规范.md
标题: # 接口规范 | API Specification
```

#### 推荐规则 | Recommended Rules

**R-DOC-004**: 文档内容应提供中英文对照
**R-DOC-005**: 重要章节应有明确的中英文标题
**R-DOC-006**: 代码示例应包含注释说明

### 2. 文档结构规范 | Document Structure Standards

#### 标准目录结构 | Standard Directory Structure

**R-DOC-007**: 必须使用标准的8级文档分类结构
```
docs_文档/
├── 01_overview_项目概述/        # 项目概述
├── 02_architecture_系统架构/    # 系统架构
├── 03_development_开发指南/     # 开发指南
├── 04_api_接口文档/            # 接口文档
├── 05_deployment_部署运维/      # 部署运维
├── 06_testing_测试文档/         # 测试文档
├── 07_maintenance_维护手册/     # 维护手册
├── 08_reference_参考资料/       # 参考资料
└── README_文档索引.md          # 主索引
```

**R-DOC-008**: 每个分类目录必须包含完整的标准文档
**R-DOC-009**: 文档索引必须保持最新状态
**R-DOC-010**: 所有文档必须包含元数据信息

### 3. 文档质量标准 | Document Quality Standards

#### 内容要求 | Content Requirements

**R-DOC-011**: 所有文档必须包含以下基本元素
- 标题（中英文对照）
- 概述部分
- 目录结构
- 详细内容
- 示例（如适用）
- 相关链接

**R-DOC-012**: 技术文档必须包含
- 版本信息
- 最后更新时间
- 维护团队信息
- 审核状态

**R-DOC-013**: 代码示例必须
- 语法正确
- 包含注释
- 可以运行
- 有输出说明

---

## 💻 代码管理规则 | Code Management Rules

### 1. 代码命名规范 | Code Naming Standards

#### Java代码规范 | Java Code Standards

**R-CODE-001**: 类名必须使用PascalCase
```java
// 正确
public class GameServer { }
public class PlayerManager { }

// 错误
public class gameserver { }
public class player_manager { }
```

**R-CODE-002**: 方法名必须使用camelCase
```java
// 正确
public void startServer() { }
public int getPlayerCount() { }

// 错误
public void StartServer() { }
public int get_player_count() { }
```

**R-CODE-003**: 常量必须使用UPPER_CASE
```java
// 正确
public static final int MAX_PLAYERS = 10000;
public static final String DEFAULT_CONFIG = "config.xml";

// 错误
public static final int maxPlayers = 10000;
public static final String defaultConfig = "config.xml";
```

**R-CODE-004**: 包名必须使用小写字母
```java
// 正确
package fire.pb.main;
package gnet.protocol;

// 错误
package Fire.Pb.Main;
package Gnet.Protocol;
```

### 2. 代码注释规范 | Code Comment Standards

#### 注释要求 | Comment Requirements

**R-CODE-005**: 所有公共类必须有JavaDoc注释
```java
/**
 * 游戏服务器主类 | Main Game Server Class
 * 
 * 负责游戏服务器的启动、停止和管理
 * Responsible for starting, stopping and managing the game server
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2025-07-20
 */
public class GameServer {
    // 类实现
}
```

**R-CODE-006**: 所有公共方法必须有JavaDoc注释
```java
/**
 * 启动游戏服务器 | Start Game Server
 * 
 * @param port 服务器端口 Server port
 * @param config 配置文件路径 Configuration file path
 * @return 启动是否成功 Whether startup was successful
 * @throws ServerException 服务器启动异常 Server startup exception
 */
public boolean startServer(int port, String config) throws ServerException {
    // 方法实现
}
```

**R-CODE-007**: 复杂逻辑必须有行内注释
```java
// 初始化数据库连接池 | Initialize database connection pool
ConnectionPool pool = new ConnectionPool();
pool.setMaxConnections(100);  // 最大连接数 | Maximum connections
pool.setMinConnections(10);   // 最小连接数 | Minimum connections
```

### 3. 代码质量标准 | Code Quality Standards

#### 质量要求 | Quality Requirements

**R-CODE-008**: 代码必须通过静态分析检查
- SonarQube质量门禁
- SpotBugs缺陷检测
- Checkstyle代码风格检查

**R-CODE-009**: 单元测试覆盖率要求
- 新代码覆盖率 ≥ 80%
- 关键业务逻辑覆盖率 = 100%
- 集成测试覆盖率 ≥ 60%

**R-CODE-010**: 性能要求
- 方法执行时间 < 100ms（一般方法）
- 数据库查询时间 < 50ms（单次查询）
- 内存使用稳定，无内存泄漏

---

## 🏗️ 项目结构规则 | Project Structure Rules

### 1. 目录结构标准 | Directory Structure Standards

#### 标准项目结构 | Standard Project Structure

**R-STRUCT-001**: 必须使用标准的Maven项目结构
```
gsxdb-server/
├── src/
│   ├── main/
│   │   ├── java/           # Java源代码
│   │   └── resources/      # 资源文件
│   └── test/               # 测试代码
├── lib/                    # 依赖库
├── config/                 # 配置文件
├── docs_文档/              # 项目文档
├── scripts_脚本/           # 自动化脚本
├── build/                  # 构建输出
├── pom.xml                 # Maven配置
└── README.md               # 项目说明
```

**R-STRUCT-002**: 禁止在根目录创建临时文件和目录
**R-STRUCT-003**: 所有备份文件必须放在指定的备份目录
**R-STRUCT-004**: 构建输出必须放在build/目录下

### 2. 文件组织规则 | File Organization Rules

#### 文件分类 | File Classification

**R-STRUCT-005**: 源代码文件分类
- `src/main/java/` - 主要源代码
- `src/test/java/` - 测试代码
- `src/main/resources/` - 配置和资源文件

**R-STRUCT-006**: 配置文件分类
- `config/dev/` - 开发环境配置
- `config/test/` - 测试环境配置
- `config/prod/` - 生产环境配置

**R-STRUCT-007**: 脚本文件分类
- `scripts_脚本/build/` - 构建脚本
- `scripts_脚本/deploy/` - 部署脚本
- `scripts_脚本/maintenance/` - 维护脚本

---

## 🔄 工作流程规则 | Workflow Rules

### 1. 开发流程 | Development Workflow

#### 开发步骤 | Development Steps

**R-FLOW-001**: 功能开发必须遵循以下流程
1. **需求分析** - 明确功能需求和技术方案
2. **设计文档** - 编写技术设计文档
3. **代码实现** - 按照规范实现功能
4. **单元测试** - 编写和执行单元测试
5. **代码审查** - 团队成员代码审查
6. **集成测试** - 执行集成测试
7. **文档更新** - 更新相关文档
8. **部署验证** - 在测试环境验证

**R-FLOW-002**: 每个功能必须有对应的文档更新
**R-FLOW-003**: 代码提交前必须通过所有测试
**R-FLOW-004**: 重要功能必须经过代码审查

### 2. 文档维护流程 | Documentation Maintenance Workflow

#### 文档更新流程 | Document Update Workflow

**R-FLOW-005**: 文档更新必须遵循以下流程
1. **识别需求** - 确定文档更新需求
2. **内容编写** - 按照规范编写内容
3. **格式检查** - 使用工具检查格式
4. **内容审核** - 团队成员审核内容
5. **发布更新** - 正式发布更新
6. **通知团队** - 通知相关团队成员

**R-FLOW-006**: 文档更新必须与代码变更同步
**R-FLOW-007**: 重要文档变更必须经过审核
**R-FLOW-008**: 文档版本必须与项目版本对应

### 3. 质量保证流程 | Quality Assurance Workflow

#### 质量检查流程 | Quality Check Workflow

**R-FLOW-009**: 定期质量检查流程
- **每日检查** - 代码质量和文档更新
- **每周检查** - 项目结构和命名规范
- **每月检查** - 整体质量和标准遵循
- **季度审核** - 规则有效性和改进建议

---

## 🛠️ 工具和自动化规则 | Tools and Automation Rules

### 1. 必须使用的工具 | Required Tools

#### 开发工具 | Development Tools

**R-TOOL-001**: 必须使用的开发工具
- **IDE**: IntelliJ IDEA 或 Eclipse
- **构建工具**: Apache Maven 3.6+
- **版本控制**: Git
- **代码质量**: SonarQube, SpotBugs, Checkstyle

**R-TOOL-002**: 必须使用的文档工具
- **文档格式**: Markdown
- **文档检查**: `scripts/doc_naming_checker.py`
- **文档生成**: `scripts/document_reorganizer_文档重组器.py`
- **结构维护**: `scripts/directory_maintenance.py`

### 2. 自动化要求 | Automation Requirements

#### 自动化流程 | Automated Processes

**R-TOOL-003**: 必须自动化的流程
- **代码质量检查** - 每次提交自动检查
- **文档格式验证** - 每次文档更新自动验证
- **构建和测试** - 每次代码变更自动构建测试
- **部署流程** - 使用自动化部署脚本

**R-TOOL-004**: 定期自动化任务
- **每日清理** - 自动清理临时文件
- **每周检查** - 自动检查项目健康度
- **每月报告** - 自动生成质量报告

---

## 📊 监控和度量规则 | Monitoring and Metrics Rules

### 1. 质量度量标准 | Quality Metrics Standards

#### 关键指标 | Key Metrics

**R-METRIC-001**: 代码质量指标
- **代码覆盖率** ≥ 80%
- **代码重复率** ≤ 3%
- **圈复杂度** ≤ 10
- **技术债务** ≤ 5%

**R-METRIC-002**: 文档质量指标
- **文档覆盖率** ≥ 95%
- **文档更新及时性** ≤ 1周
- **链接有效性** = 100%
- **命名规范遵循率** = 100%

**R-METRIC-003**: 项目健康度指标
- **构建成功率** ≥ 95%
- **测试通过率** = 100%
- **部署成功率** ≥ 98%
- **问题解决时间** ≤ 24小时

### 2. 监控要求 | Monitoring Requirements

#### 监控机制 | Monitoring Mechanisms

**R-METRIC-004**: 必须监控的项目
- **代码质量变化趋势**
- **文档更新频率和质量**
- **规则遵循情况**
- **团队生产效率**

**R-METRIC-005**: 报告要求
- **每周质量报告** - 发送给团队
- **每月项目报告** - 发送给管理层
- **季度改进报告** - 包含改进建议

---

## 🚨 违规处理规则 | Violation Handling Rules

### 1. 违规分类 | Violation Categories

#### 违规等级 | Violation Levels

**R-VIOLATION-001**: 轻微违规（警告级别）
- 文档格式不规范
- 注释不完整
- 命名不规范

**R-VIOLATION-002**: 中等违规（需要修复）
- 代码质量不达标
- 测试覆盖率不足
- 文档缺失

**R-VIOLATION-003**: 严重违规（必须立即修复）
- 安全漏洞
- 功能缺陷
- 数据丢失风险

### 2. 处理流程 | Handling Process

#### 违规处理步骤 | Violation Handling Steps

**R-VIOLATION-004**: 违规处理流程
1. **自动检测** - 工具自动检测违规
2. **通知相关人员** - 立即通知责任人
3. **制定修复计划** - 确定修复时间和方案
4. **执行修复** - 按计划执行修复
5. **验证修复** - 验证修复效果
6. **记录归档** - 记录违规和修复过程

---

## 📞 支持和联系 | Support and Contact

### 规则咨询 | Rule Consultation

#### 获取帮助 | Getting Help
- **规则解释** - 联系项目负责人
- **工具使用** - 查看scripts/目录文档
- **流程指导** - 参考本规则文档
- **培训需求** - 联系团队负责人

### 规则改进 | Rule Improvement

#### 改进机制 | Improvement Mechanism
- **建议提交** - 欢迎提交改进建议
- **定期评审** - 季度规则评审会议
- **版本更新** - 根据需要更新规则版本
- **培训推广** - 新规则培训和推广

---

**📝 规则版本**: v2.0.0  
**📅 最后更新**: 2025-07-20  
**✍️ 制定团队**: GSXDB项目团队  
**📋 审核状态**: 已审核通过  
**🔒 执行级别**: 强制执行

---

*本规则文档是项目管理的核心文件，所有项目成员必须严格遵守。违反规则将按照相应流程处理。*

*This rule document is the core file for project management, and all project members must strictly comply. Violations will be handled according to the corresponding process.*
