{"timestamp": "2025-07-26T19:01:05.440668", "production_files": [{"path": "mkdb\\Bean.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Bean.class", "size": 370, "modified": "2025-07-26T18:56:51.164407"}, {"path": "mkdb\\Module.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Module.class", "size": 738, "modified": "2025-07-26T18:56:51.165409"}, {"path": "mkdb\\Procedure$1.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$1.class", "size": 665, "modified": "2025-07-26T17:41:34.384180"}, {"path": "mkdb\\Procedure$2.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$2.class", "size": 794, "modified": "2025-07-26T17:41:34.384180"}, {"path": "mkdb\\Procedure$BeanComparator.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$BeanComparator.class", "size": 812, "modified": "2025-07-26T17:41:34.385180"}, {"path": "mkdb\\Procedure$Broadcast.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$Broadcast.class", "size": 676, "modified": "2025-07-26T17:41:34.386179"}, {"path": "mkdb\\Procedure$Done.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$Done.class", "size": 284, "modified": "2025-07-26T17:41:34.387180"}, {"path": "mkdb\\Procedure$ExecuteProcedure.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$ExecuteProcedure.class", "size": 457, "modified": "2025-07-26T17:41:34.386179"}, {"path": "mkdb\\Procedure$ExecuteRunnable.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$ExecuteRunnable.class", "size": 544, "modified": "2025-07-26T17:41:34.387180"}, {"path": "mkdb\\Procedure$IOnlines.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$IOnlines.class", "size": 446, "modified": "2025-07-26T17:41:34.387180"}, {"path": "mkdb\\Procedure$SendResponse.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$SendResponse.class", "size": 649, "modified": "2025-07-26T17:41:34.386179"}, {"path": "mkdb\\Procedure$SendToRole.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$SendToRole.class", "size": 690, "modified": "2025-07-26T17:41:34.385180"}, {"path": "mkdb\\Procedure$SendToRoles.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$SendToRoles.class", "size": 1118, "modified": "2025-07-26T17:41:34.385180"}, {"path": "mkdb\\Procedure$SendToXio.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$SendToXio.class", "size": 535, "modified": "2025-07-26T17:41:34.386179"}, {"path": "mkdb\\Procedure$Task.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$Task.class", "size": 789, "modified": "2025-07-26T17:41:34.385180"}, {"path": "mkdb\\Procedure.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure.class", "size": 8785, "modified": "2025-07-26T17:41:34.389180"}, {"path": "fire\\script\\AbstractJSEngine.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\AbstractJSEngine.class", "size": 4019, "modified": "2025-07-26T17:41:34.294972"}, {"path": "fire\\script\\FightJSEngine.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\FightJSEngine.class", "size": 6860, "modified": "2025-07-26T19:00:54.067130"}, {"path": "fire\\script\\IJavaScriptEngine.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\IJavaScriptEngine.class", "size": 545, "modified": "2025-07-26T17:41:34.296972"}, {"path": "fire\\script\\JavaScriptEngineFix.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\JavaScriptEngineFix.class", "size": 5433, "modified": "2025-07-26T18:56:50.639192"}, {"path": "fire\\script\\JsFunManager.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\JsFunManager.class", "size": 129598, "modified": "2025-07-26T19:00:37.711644"}, {"path": "fire\\pb\\battle\\BuffAgent.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\battle\\BuffAgent.class", "size": 209, "modified": "2025-07-26T18:56:51.164407"}, {"path": "fire\\pb\\battle\\Fighter$1.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\battle\\Fighter$1.class", "size": 1734, "modified": "2025-07-26T18:56:51.161410"}, {"path": "fire\\pb\\battle\\Fighter$2.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\battle\\Fighter$2.class", "size": 687, "modified": "2025-07-26T18:56:51.161410"}, {"path": "fire\\pb\\battle\\Fighter$Battle.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\battle\\Fighter$Battle.class", "size": 447, "modified": "2025-07-26T18:56:51.162409"}, {"path": "fire\\pb\\battle\\Fighter$FighterBean.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\battle\\Fighter$FighterBean.class", "size": 994, "modified": "2025-07-26T18:56:51.162409"}, {"path": "fire\\pb\\battle\\Fighter.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\battle\\Fighter.class", "size": 2661, "modified": "2025-07-26T18:56:51.163408"}, {"path": "fire\\pb\\role\\EffectRole.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\role\\EffectRole.class", "size": 390, "modified": "2025-07-26T18:56:51.164407"}], "test_files": [{"path": "fire\\log\\Logger$LogWhileProcedureEnd.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\log\\Logger$LogWhileProcedureEnd.class", "size": 1354, "modified": "2025-07-26T17:41:34.343972"}, {"path": "fire\\log\\Logger.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\log\\Logger.class", "size": 4177, "modified": "2025-07-26T17:41:34.344974"}, {"path": "fire\\pb\\PropConf$Battle.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$Battle.class", "size": 3263, "modified": "2025-07-26T17:41:34.251976"}, {"path": "fire\\pb\\PropConf$Cross.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$Cross.class", "size": 517, "modified": "2025-07-26T17:41:34.251976"}, {"path": "fire\\pb\\PropConf$FuShi.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$FuShi.class", "size": 947, "modified": "2025-07-26T17:41:34.248974"}, {"path": "fire\\pb\\PropConf$Mission.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$Mission.class", "size": 1029, "modified": "2025-07-26T17:41:34.249973"}, {"path": "fire\\pb\\PropConf$ServerId.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$ServerId.class", "size": 520, "modified": "2025-07-26T17:41:34.252973"}, {"path": "fire\\pb\\PropConf$Specialquest.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$Specialquest.class", "size": 741, "modified": "2025-07-26T17:41:34.249973"}, {"path": "fire\\pb\\PropConf$Sys.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$Sys.class", "size": 1044, "modified": "2025-07-26T17:41:34.248974"}, {"path": "fire\\pb\\PropConf.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf.class", "size": 1178, "modified": "2025-07-26T17:41:34.252973"}, {"path": "fire\\script\\FightJSEngine$SimpleModule.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\FightJSEngine$SimpleModule.class", "size": 1124, "modified": "2025-07-26T19:00:54.066131"}, {"path": "fire\\script\\JavaScriptCompatibilityAdapter.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\JavaScriptCompatibilityAdapter.class", "size": 6608, "modified": "2025-07-26T18:56:51.552602"}, {"path": "fire\\script\\JsFunManagerUpgraded.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\JsFunManagerUpgraded.class", "size": 9474, "modified": "2025-07-26T18:56:51.153407"}, {"path": "fire\\pb\\battle\\BattleManager.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\battle\\BattleManager.class", "size": 2523, "modified": "2025-07-26T17:38:23.881120"}, {"path": "fire\\pb\\effect\\IEffect.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\effect\\IEffect.class", "size": 193, "modified": "2025-07-26T17:41:34.384180"}, {"path": "fire\\pb\\effect\\Module.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\effect\\Module.class", "size": 8024, "modified": "2025-07-26T17:41:34.383181"}, {"path": "fire\\pb\\main\\ConfigManager.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\main\\ConfigManager.class", "size": 12491, "modified": "2025-07-26T17:41:34.370178"}, {"path": "fire\\pb\\main\\ModuleInterface.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\main\\ModuleInterface.class", "size": 267, "modified": "2025-07-26T17:41:34.371179"}, {"path": "fire\\pb\\main\\ReloadResult.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\main\\ReloadResult.class", "size": 1201, "modified": "2025-07-26T17:41:34.372180"}, {"path": "fire\\pb\\main\\Unmarshaller.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\main\\Unmarshaller.class", "size": 182, "modified": "2025-07-26T17:41:34.371179"}, {"path": "fire\\pb\\util\\FireProp.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\util\\FireProp.class", "size": 1926, "modified": "2025-07-26T17:41:34.374180"}], "moved_files": [{"file": "fire\\log\\Logger$LogWhileProcedureEnd.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\log\\Logger$LogWhileProcedureEnd.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\log\\Logger$LogWhileProcedureEnd.class", "size": 1354}, {"file": "fire\\log\\Logger.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\log\\Logger.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\log\\Logger.class", "size": 4177}, {"file": "fire\\pb\\PropConf$Battle.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$Battle.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\pb\\PropConf$Battle.class", "size": 3263}, {"file": "fire\\pb\\PropConf$Cross.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$Cross.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\pb\\PropConf$Cross.class", "size": 517}, {"file": "fire\\pb\\PropConf$FuShi.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$FuShi.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\pb\\PropConf$FuShi.class", "size": 947}, {"file": "fire\\pb\\PropConf$Mission.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$Mission.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\pb\\PropConf$Mission.class", "size": 1029}, {"file": "fire\\pb\\PropConf$ServerId.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$ServerId.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\pb\\PropConf$ServerId.class", "size": 520}, {"file": "fire\\pb\\PropConf$Specialquest.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$Specialquest.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\pb\\PropConf$Specialquest.class", "size": 741}, {"file": "fire\\pb\\PropConf$Sys.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$Sys.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\pb\\PropConf$Sys.class", "size": 1044}, {"file": "fire\\pb\\PropConf.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\pb\\PropConf.class", "size": 1178}, {"file": "fire\\script\\FightJSEngine$SimpleModule.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\FightJSEngine$SimpleModule.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\script\\FightJSEngine$SimpleModule.class", "size": 1124}, {"file": "fire\\script\\JavaScriptCompatibilityAdapter.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\JavaScriptCompatibilityAdapter.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\script\\JavaScriptCompatibilityAdapter.class", "size": 6608}, {"file": "fire\\script\\JsFunManagerUpgraded.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\JsFunManagerUpgraded.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\script\\JsFunManagerUpgraded.class", "size": 9474}, {"file": "fire\\pb\\battle\\BattleManager.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\battle\\BattleManager.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\pb\\battle\\BattleManager.class", "size": 2523}, {"file": "fire\\pb\\effect\\IEffect.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\effect\\IEffect.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\pb\\effect\\IEffect.class", "size": 193}, {"file": "fire\\pb\\effect\\Module.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\effect\\Module.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\pb\\effect\\Module.class", "size": 8024}, {"file": "fire\\pb\\main\\ConfigManager.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\main\\ConfigManager.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\pb\\main\\ConfigManager.class", "size": 12491}, {"file": "fire\\pb\\main\\ModuleInterface.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\main\\ModuleInterface.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\pb\\main\\ModuleInterface.class", "size": 267}, {"file": "fire\\pb\\main\\ReloadResult.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\main\\ReloadResult.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\pb\\main\\ReloadResult.class", "size": 1201}, {"file": "fire\\pb\\main\\Unmarshaller.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\main\\Unmarshaller.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\pb\\main\\Unmarshaller.class", "size": 182}, {"file": "fire\\pb\\util\\FireProp.class", "from": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\util\\FireProp.class", "to": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb\\fire\\pb\\util\\FireProp.class", "size": 1926}], "preserved_files": [], "statistics": {"original_total": 49, "current_total": 49, "production_files": 28, "test_files": 21, "files_moved": 21, "organization_integrity": true}, "organization_success": false, "all_files": [{"path": "mkdb\\Bean.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Bean.class", "size": 370, "modified": "2025-07-26T18:56:51.164407"}, {"path": "mkdb\\Module.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Module.class", "size": 738, "modified": "2025-07-26T18:56:51.165409"}, {"path": "mkdb\\Procedure$1.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$1.class", "size": 665, "modified": "2025-07-26T17:41:34.384180"}, {"path": "mkdb\\Procedure$2.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$2.class", "size": 794, "modified": "2025-07-26T17:41:34.384180"}, {"path": "mkdb\\Procedure$BeanComparator.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$BeanComparator.class", "size": 812, "modified": "2025-07-26T17:41:34.385180"}, {"path": "mkdb\\Procedure$Broadcast.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$Broadcast.class", "size": 676, "modified": "2025-07-26T17:41:34.386179"}, {"path": "mkdb\\Procedure$Done.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$Done.class", "size": 284, "modified": "2025-07-26T17:41:34.387180"}, {"path": "mkdb\\Procedure$ExecuteProcedure.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$ExecuteProcedure.class", "size": 457, "modified": "2025-07-26T17:41:34.386179"}, {"path": "mkdb\\Procedure$ExecuteRunnable.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$ExecuteRunnable.class", "size": 544, "modified": "2025-07-26T17:41:34.387180"}, {"path": "mkdb\\Procedure$IOnlines.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$IOnlines.class", "size": 446, "modified": "2025-07-26T17:41:34.387180"}, {"path": "mkdb\\Procedure$SendResponse.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$SendResponse.class", "size": 649, "modified": "2025-07-26T17:41:34.386179"}, {"path": "mkdb\\Procedure$SendToRole.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$SendToRole.class", "size": 690, "modified": "2025-07-26T17:41:34.385180"}, {"path": "mkdb\\Procedure$SendToRoles.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$SendToRoles.class", "size": 1118, "modified": "2025-07-26T17:41:34.385180"}, {"path": "mkdb\\Procedure$SendToXio.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$SendToXio.class", "size": 535, "modified": "2025-07-26T17:41:34.386179"}, {"path": "mkdb\\Procedure$Task.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure$Task.class", "size": 789, "modified": "2025-07-26T17:41:34.385180"}, {"path": "mkdb\\Procedure.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\mkdb\\Procedure.class", "size": 8785, "modified": "2025-07-26T17:41:34.389180"}, {"path": "fire\\log\\Logger$LogWhileProcedureEnd.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\log\\Logger$LogWhileProcedureEnd.class", "size": 1354, "modified": "2025-07-26T17:41:34.343972"}, {"path": "fire\\log\\Logger.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\log\\Logger.class", "size": 4177, "modified": "2025-07-26T17:41:34.344974"}, {"path": "fire\\pb\\PropConf$Battle.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$Battle.class", "size": 3263, "modified": "2025-07-26T17:41:34.251976"}, {"path": "fire\\pb\\PropConf$Cross.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$Cross.class", "size": 517, "modified": "2025-07-26T17:41:34.251976"}, {"path": "fire\\pb\\PropConf$FuShi.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$FuShi.class", "size": 947, "modified": "2025-07-26T17:41:34.248974"}, {"path": "fire\\pb\\PropConf$Mission.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$Mission.class", "size": 1029, "modified": "2025-07-26T17:41:34.249973"}, {"path": "fire\\pb\\PropConf$ServerId.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$ServerId.class", "size": 520, "modified": "2025-07-26T17:41:34.252973"}, {"path": "fire\\pb\\PropConf$Specialquest.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$Specialquest.class", "size": 741, "modified": "2025-07-26T17:41:34.249973"}, {"path": "fire\\pb\\PropConf$Sys.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf$Sys.class", "size": 1044, "modified": "2025-07-26T17:41:34.248974"}, {"path": "fire\\pb\\PropConf.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\PropConf.class", "size": 1178, "modified": "2025-07-26T17:41:34.252973"}, {"path": "fire\\script\\AbstractJSEngine.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\AbstractJSEngine.class", "size": 4019, "modified": "2025-07-26T17:41:34.294972"}, {"path": "fire\\script\\FightJSEngine$SimpleModule.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\FightJSEngine$SimpleModule.class", "size": 1124, "modified": "2025-07-26T19:00:54.066131"}, {"path": "fire\\script\\FightJSEngine.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\FightJSEngine.class", "size": 6860, "modified": "2025-07-26T19:00:54.067130"}, {"path": "fire\\script\\IJavaScriptEngine.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\IJavaScriptEngine.class", "size": 545, "modified": "2025-07-26T17:41:34.296972"}, {"path": "fire\\script\\JavaScriptCompatibilityAdapter.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\JavaScriptCompatibilityAdapter.class", "size": 6608, "modified": "2025-07-26T18:56:51.552602"}, {"path": "fire\\script\\JavaScriptEngineFix.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\JavaScriptEngineFix.class", "size": 5433, "modified": "2025-07-26T18:56:50.639192"}, {"path": "fire\\script\\JsFunManager.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\JsFunManager.class", "size": 129598, "modified": "2025-07-26T19:00:37.711644"}, {"path": "fire\\script\\JsFunManagerUpgraded.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\script\\JsFunManagerUpgraded.class", "size": 9474, "modified": "2025-07-26T18:56:51.153407"}, {"path": "fire\\pb\\battle\\BattleManager.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\battle\\BattleManager.class", "size": 2523, "modified": "2025-07-26T17:38:23.881120"}, {"path": "fire\\pb\\battle\\BuffAgent.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\battle\\BuffAgent.class", "size": 209, "modified": "2025-07-26T18:56:51.164407"}, {"path": "fire\\pb\\battle\\Fighter$1.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\battle\\Fighter$1.class", "size": 1734, "modified": "2025-07-26T18:56:51.161410"}, {"path": "fire\\pb\\battle\\Fighter$2.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\battle\\Fighter$2.class", "size": 687, "modified": "2025-07-26T18:56:51.161410"}, {"path": "fire\\pb\\battle\\Fighter$Battle.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\battle\\Fighter$Battle.class", "size": 447, "modified": "2025-07-26T18:56:51.162409"}, {"path": "fire\\pb\\battle\\Fighter$FighterBean.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\battle\\Fighter$FighterBean.class", "size": 994, "modified": "2025-07-26T18:56:51.162409"}, {"path": "fire\\pb\\battle\\Fighter.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\battle\\Fighter.class", "size": 2661, "modified": "2025-07-26T18:56:51.163408"}, {"path": "fire\\pb\\effect\\IEffect.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\effect\\IEffect.class", "size": 193, "modified": "2025-07-26T17:41:34.384180"}, {"path": "fire\\pb\\effect\\Module.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\effect\\Module.class", "size": 8024, "modified": "2025-07-26T17:41:34.383181"}, {"path": "fire\\pb\\main\\ConfigManager.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\main\\ConfigManager.class", "size": 12491, "modified": "2025-07-26T17:41:34.370178"}, {"path": "fire\\pb\\main\\ModuleInterface.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\main\\ModuleInterface.class", "size": 267, "modified": "2025-07-26T17:41:34.371179"}, {"path": "fire\\pb\\main\\ReloadResult.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\main\\ReloadResult.class", "size": 1201, "modified": "2025-07-26T17:41:34.372180"}, {"path": "fire\\pb\\main\\Unmarshaller.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\main\\Unmarshaller.class", "size": 182, "modified": "2025-07-26T17:41:34.371179"}, {"path": "fire\\pb\\role\\EffectRole.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\role\\EffectRole.class", "size": 390, "modified": "2025-07-26T18:56:51.164407"}, {"path": "fire\\pb\\util\\FireProp.class", "full_path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb\\fire\\pb\\util\\FireProp.class", "size": 1926, "modified": "2025-07-26T17:41:34.374180"}], "organization_summary": {"total_files_processed": 49, "production_files_count": 28, "test_files_count": 21, "files_moved_count": 21, "organization_date": "2025-07-26T19:01:05.453668", "production_directory": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\production\\gsxdb", "test_directory": "E:\\MT3_Projects\\gsxdb_mt3_mly\\out\\test\\gsxdb"}}