// Java项目翻牌功能完整文档
public class FlipCardSystemDocumentation {
    
    public static void main(String[] args) {
        System.out.println("=== Java项目翻牌功能完整文档 ===\n");
        
        System.out.println("1. 功能概述");
        System.out.println("翻牌系统是一个基于概率的奖励发放机制，玩家可以从多张卡牌中");
        System.out.println("选择一张来获得奖励。系统支持多种奖励类型，包括物品、经验、金钱等。");
        
        System.out.println("\n2. 核心组件架构");
        
        System.out.println("\n2.1 主要类结构");
        System.out.println("* RollCardManager - 翻牌管理器（单例模式）");
        System.out.println("* CardItem - 卡牌物品类");
        System.out.println("* RollCardInfo - 翻牌信息数据结构");
        System.out.println("* WheelItem - 转盘物品类");
        System.out.println("* PReqCardProc - 请求翻牌处理器");
        System.out.println("* PPlayCardItemProc - 翻牌奖励发放处理器");
        
        System.out.println("\n2.2 配置类");
        System.out.println("* SRollCardConfig - 翻牌配置类");
        System.out.println("* SSpotCardConfig - 点卡配置类");
        
        System.out.println("\n3. 数据结构详解");
        
        System.out.println("\n3.1 CardItem类属性");
        System.out.println("- type: 物品类型（1=物品, 2=经验, 3=金钱, 5=物品类型库）");
        System.out.println("- itemId: 物品ID");
        System.out.println("- number: 数量");
        System.out.println("- times: 倍数");
        System.out.println("- probability: 概率权重");
        System.out.println("- realindex: 真实索引（1=必中项）");
        System.out.println("- bind: 绑定状态");
        System.out.println("- msgId: 消息ID");
        System.out.println("- obs: 备注信息");
        
        System.out.println("\n3.2 RollCardInfo数据结构");
        System.out.println("- serviceid: 服务ID");
        System.out.println("- takeflag: 领取标志（0=未领取, 1=已领取）");
        System.out.println("- basemoney: 基础金钱");
        System.out.println("- basesmoney: 基础符石");
        System.out.println("- baseexp: 基础经验");
        System.out.println("- index: 中奖卡牌索引");
        System.out.println("- wheelitems: 卡牌列表（最多4张）");
        
        System.out.println("\n3.3 WheelItem属性");
        System.out.println("- itemtype: 物品类型（1=物品, 2=经验, 3=金钱）");
        System.out.println("- itemid: 物品ID（金钱和经验为0）");
        System.out.println("- num: 数量");
        System.out.println("- times: 倍数（实际倍数的10倍）");
        System.out.println("- bind: 绑定状态（仅对物品有效）");
        System.out.println("- limit: 最大上限（仅对物品有效）");
        System.out.println("- msgid: 公告消息ID（仅对物品有效）");
        
        System.out.println("\n4. 核心流程分析");
        
        System.out.println("\n4.1 翻牌请求流程（PReqCardProc）");
        System.out.println("1. 接收参数：roleId（角色ID）, realIndex（配置索引）, cardCount（卡牌数量）");
        System.out.println("2. 从RollCardManager获取卡牌配置");
        System.out.println("3. 调用choseCard()方法选择卡牌");
        System.out.println("4. 调用getSelectIndex()确定中奖索引");
        System.out.println("5. 处理物品类型库转换（type=5转为type=1）");
        System.out.println("6. 保存翻牌信息到数据库");
        System.out.println("7. 发送SReqFortuneWheel消息给客户端");
        
        System.out.println("\n4.2 卡牌选择算法（choseCard）");
        System.out.println("1. 优先选择必中项（realIndex=1）");
        System.out.println("2. 如果必中项超过需要数量，随机移除多余项");
        System.out.println("3. 从剩余卡牌中按概率权重随机选择");
        System.out.println("4. 使用Misc.getProbability()进行概率计算");
        System.out.println("5. 返回最终选中的卡牌列表");
        
        System.out.println("\n4.3 中奖索引确定（getSelectIndex）");
        System.out.println("1. 构建所有卡牌的概率权重数组");
        System.out.println("2. 使用概率算法确定最终中奖索引");
        System.out.println("3. 返回中奖卡牌在列表中的位置");
        
        System.out.println("\n4.4 奖励发放流程（PPlayCardItemProc）");
        System.out.println("1. 验证翻牌信息有效性");
        System.out.println("2. 检查是否已经领取过奖励");
        System.out.println("3. 根据中奖索引获取对应奖励");
        System.out.println("4. 按奖励类型分别处理：");
        System.out.println("   - type=1: 调用giveItem()发放物品");
        System.out.println("   - type=2: 调用giveExp()发放经验");
        System.out.println("   - type=3: 调用giveMoney()发放金钱");
        System.out.println("5. 设置takeflag=1标记已领取");
        
        System.out.println("\n5. 配置管理");
        
        System.out.println("\n5.1 配置加载");
        System.out.println("- 通过ConfigManager加载SRollCardConfig配置");
        System.out.println("- 支持热重载（reload()方法）");
        System.out.println("- 配置格式：id -> List<CardItem>");
        
        System.out.println("\n5.2 配置解析");
        System.out.println("- 解析配置字符串为CardItem对象");
        System.out.println("- 支持多种奖励类型配置");
        System.out.println("- 自动处理概率权重分配");
        
        System.out.println("\n6. 数据库设计");
        
        System.out.println("\n6.1 表结构");
        System.out.println("- rolerollcard表：存储角色翻牌信息");
        System.out.println("- 主键：roleId（角色ID）");
        System.out.println("- 外键：关联properties表");
        System.out.println("- 锁机制：使用rolelock保证并发安全");
        
        System.out.println("\n6.2 缓存策略");
        System.out.println("- 缓存容量：1024");
        System.out.println("- 缓存策略：LRU");
        System.out.println("- 数据一致性：通过事务保证");
        
        System.out.println("\n7. 客户端通信");
        
        System.out.println("\n7.1 消息协议");
        System.out.println("- SReqFortuneWheel：翻牌界面消息");
        System.out.println("  * flag: 状态标志");
        System.out.println("  * index: 中奖索引");
        System.out.println("  * itemids: 卡牌物品列表");
        
        System.out.println("\n7.2 ForturneWheelType结构");
        System.out.println("- id: 物品ID");
        System.out.println("- itemtype: 物品类型");
        System.out.println("- num: 数量");
        System.out.println("- times: 倍数");
        
        System.out.println("\n8. 特殊功能");
        
        System.out.println("\n8.1 物品类型库支持");
        System.out.println("- type=5表示物品类型库");
        System.out.println("- 自动转换为具体物品（type=1）");
        System.out.println("- 支持动态物品生成");
        
        System.out.println("\n8.2 必中机制");
        System.out.println("- realIndex=1的卡牌必定出现");
        System.out.println("- 保证重要奖励的获得概率");
        System.out.println("- 支持多个必中项配置");
        
        System.out.println("\n8.3 概率控制");
        System.out.println("- 基于权重的概率分配");
        System.out.println("- 使用高质量随机数生成器");
        System.out.println("- 支持精确的概率控制");
        
        System.out.println("\n9. 错误处理");
        
        System.out.println("\n9.1 异常情况");
        System.out.println("- 配置不存在：返回空列表");
        System.out.println("- 索引越界：记录错误日志");
        System.out.println("- 重复领取：直接返回失败");
        System.out.println("- 数据异常：回滚事务");
        
        System.out.println("\n9.2 日志记录");
        System.out.println("- 使用AWARD日志记录器");
        System.out.println("- 记录关键操作和异常");
        System.out.println("- 便于问题追踪和调试");
        
        System.out.println("\n10. 性能优化");
        
        System.out.println("\n10.1 单例模式");
        System.out.println("- RollCardManager使用单例模式");
        System.out.println("- 减少对象创建开销");
        System.out.println("- 保证配置的一致性");
        
        System.out.println("\n10.2 缓存机制");
        System.out.println("- 配置数据内存缓存");
        System.out.println("- 数据库查询结果缓存");
        System.out.println("- 减少重复计算和IO操作");
        
        System.out.println("\n11. 技术实现细节");

        System.out.println("\n11.1 关键代码片段");
        System.out.println("// 卡牌选择核心算法");
        System.out.println("public List<CardItem> choseCard(int keyId, int choseNum) {");
        System.out.println("    List<CardItem> items = rollCardMap.get(keyId);");
        System.out.println("    List<CardItem> choseItem = new ArrayList<>();");
        System.out.println("    // 1. 优先选择必中项");
        System.out.println("    for(CardItem item : items) {");
        System.out.println("        if (item.getRealIndex() == 1) {");
        System.out.println("            choseItem.add(item);");
        System.out.println("        }");
        System.out.println("    }");
        System.out.println("    // 2. 按概率选择剩余项");
        System.out.println("    int[] proArray = new int[allItem.size()];");
        System.out.println("    int index = Misc.getProbability(proArray, hRandom);");
        System.out.println("}");

        System.out.println("\n11.2 数据转换");
        System.out.println("// CardItem转WheelItem");
        System.out.println("public List<WheelItem> toWheelItemList(List<CardItem> items) {");
        System.out.println("    for(CardItem item : items) {");
        System.out.println("        WheelItem wheelItem = Pod.newWheelItem();");
        System.out.println("        wheelItem.setItemid(item.getItemId());");
        System.out.println("        wheelItem.setItemtype(item.getType());");
        System.out.println("        wheelItem.setNum(item.getNumber());");
        System.out.println("    }");
        System.out.println("}");

        System.out.println("\n11.3 物品类型库处理");
        System.out.println("// 动态物品生成");
        System.out.println("for (CardItem item : items) {");
        System.out.println("    if (item.getType() == 5) { // 物品类型库");
        System.out.println("        int itemid = getItemId(item.getItemId());");
        System.out.println("        if (itemid > 0) {");
        System.out.println("            item.setItemId(itemid);");
        System.out.println("        }");
        System.out.println("        item.setType(1); // 转为普通物品");
        System.out.println("    }");
        System.out.println("}");

        System.out.println("\n12. 扩展功能");

        System.out.println("\n12.1 冰封战场集成");
        System.out.println("- BingFengBattleHandler中的fanPai()方法");
        System.out.println("- 根据战斗轮数确定翻牌等级");
        System.out.println("- 自动触发翻牌奖励");

        System.out.println("\n12.2 点卡交易系统");
        System.out.println("- SpotCheckManage管理点卡交易");
        System.out.println("- 支持点卡买卖功能");
        System.out.println("- 仅限点卡服务器使用");

        System.out.println("\n13. 安全机制");

        System.out.println("\n13.1 并发控制");
        System.out.println("- 使用rolelock防止并发修改");
        System.out.println("- 事务保证数据一致性");
        System.out.println("- 重复领取检查");

        System.out.println("\n13.2 数据验证");
        System.out.println("- 索引边界检查");
        System.out.println("- 配置有效性验证");
        System.out.println("- 奖励合法性校验");

        System.out.println("\n14. 监控和调试");

        System.out.println("\n14.1 日志输出");
        System.out.println("- 翻牌请求日志");
        System.out.println("- 奖励发放日志");
        System.out.println("- 异常情况记录");

        System.out.println("\n14.2 统计信息");
        System.out.println("- 翻牌次数统计");
        System.out.println("- 奖励分布统计");
        System.out.println("- 概率验证数据");

        System.out.println("\n15. 部署和维护");

        System.out.println("\n15.1 配置更新");
        System.out.println("- 支持热更新配置");
        System.out.println("- 无需重启服务器");
        System.out.println("- 实时生效新配置");

        System.out.println("\n15.2 数据备份");
        System.out.println("- 定期备份翻牌数据");
        System.out.println("- 支持数据恢复");
        System.out.println("- 防止数据丢失");

        System.out.println("\n=== Java项目翻牌功能完整文档 ===");
        System.out.println("文档版本: 1.0");
        System.out.println("最后更新: 2024年");
        System.out.println("适用范围: GSXDB游戏服务器项目");
    }
}
