# 生产服务器结构分析报告

## 📁 服务器目录结构

根据提供的截图，当前生产服务器的目录结构如下：

```
/home/<USER>/
├── server1/                    # 大区1
│   ├── game_server/           # 游戏服务器
│   ├── gate_server/           # 网关服务器  
│   ├── proxy_server/          # 代理服务器
│   └── qd.sh                  # 大区启动脚本 (12.2 KB)
├── server2/                   # 大区2 (如果存在)
├── common/                    # 公共服务
├── robot/                     # 机器人相关
└── 其他系统目录...
```

## 🔍 结构分析

### ✅ **标准结构特点**

1. **符合标准部署**: 使用 `/home/<USER>/` 作为游戏根目录
2. **大区分离**: 每个大区独立目录 (server1, server2, ...)
3. **服务分离**: 每个大区包含独立的游戏、网关、代理服务
4. **脚本管理**: 包含大区级别的启动脚本 `qd.sh`

### 📋 **目录功能说明**

| 目录 | 功能 | 说明 |
|------|------|------|
| `game_server/` | 游戏核心服务 | 包含gsxdb.jar等核心文件 |
| `gate_server/` | 网关服务 | 处理客户端连接 |
| `proxy_server/` | 代理服务 | 负载均衡和转发 |
| `common/` | 公共服务 | 名称服务器、SDK服务器等 |
| `robot/` | 机器人测试 | 自动化测试工具 |

## 🛠️ QD脚本路径配置

基于这个结构，qd脚本应该配置如下路径：

### 当前配置应该是：
```bash
readonly GAME_HOME="/home/<USER>"
```

### 验证命令：
```bash
# 检查当前配置
grep "GAME_HOME" /path/to/qd

# 应该显示：
readonly GAME_HOME="/home/<USER>"
```

## 🔧 修复"大区不存在"问题

### 问题诊断：

1. **路径配置检查**：
```bash
# 检查qd脚本中的路径配置
cd /home/<USER>/server1
cat qd.sh | head -20  # 查看脚本头部配置
```

2. **目录权限检查**：
```bash
# 检查目录权限
ls -la /home/<USER>/
ls -la /home/<USER>/server1/
```

3. **脚本位置检查**：
```bash
# 查找qd脚本位置
find /home/<USER>"qd" -type f
find /home/<USER>"qd.sh" -type f
```

### 可能的问题和解决方案：

#### 问题1: 主qd脚本路径配置错误
```bash
# 如果主qd脚本在 /home/<USER>/bin/qd
# 修改GAME_HOME配置
sed -i 's|readonly GAME_HOME=.*|readonly GAME_HOME="/home/<USER>"|g' /home/<USER>/bin/qd
```

#### 问题2: 脚本执行权限问题
```bash
# 设置正确权限
chmod +x /home/<USER>/bin/qd
chmod +x /home/<USER>/server1/qd.sh
chmod +x /home/<USER>/server1/game_server/start.sh
```

#### 问题3: 相对路径问题
```bash
# 确保从正确目录执行
cd /home/<USER>
./bin/qd 1
```

## 🚀 推荐的启动流程

### 方法1: 使用主qd脚本 (推荐)
```bash
# 切换到游戏根目录
cd /home/<USER>

# 启动大区1
./bin/qd 1

# 检查状态
./bin/qd dk
```

### 方法2: 使用大区qd.sh脚本
```bash
# 切换到大区目录
cd /home/<USER>/server1

# 启动所有服务
./qd.sh start

# 检查状态
./qd.sh status
```

### 方法3: 直接启动游戏服务器
```bash
# 切换到游戏服务器目录
cd /home/<USER>/server1/game_server

# 启动游戏服务器
./start.sh daemon

# 检查状态
./start.sh status
```

## 📋 快速诊断脚本

创建以下诊断脚本来快速检查问题：

```bash
#!/bin/bash
# 快速诊断脚本

echo "=== 生产服务器结构诊断 ==="
echo "当前用户: $(whoami)"
echo "当前目录: $(pwd)"
echo

echo "=== 检查游戏目录 ==="
if [ -d "/home/<USER>" ]; then
    echo "✓ /home/<USER>"
    ls -la /home/<USER>/ | head -10
else
    echo "✗ /home/<USER>"
fi

echo
echo "=== 检查大区目录 ==="
for i in {1..5}; do
    if [ -d "/home/<USER>/server$i" ]; then
        echo "✓ server$i 存在"
        ls -la /home/<USER>/server$i/ | grep -E "(game_server|gate_server|proxy_server|qd.sh)"
    else
        echo "✗ server$i 不存在"
    fi
done

echo
echo "=== 检查qd脚本 ==="
find /home/<USER>"qd" -o -name "qd.sh" 2>/dev/null

echo
echo "=== 检查Java环境 ==="
java -version 2>&1 | head -1

echo
echo "=== 检查端口占用 ==="
netstat -tlnp | grep -E ":(41001|42001|43001|44001|15000|16000)" | head -5
```

## 🎯 建议的修复步骤

### 步骤1: 确认目录结构
```bash
cd /home/<USER>
ls -la
```

### 步骤2: 检查qd脚本配置
```bash
# 查找主qd脚本
find /home/<USER>"qd" -type f

# 检查配置
grep -n "GAME_HOME\|server.*dir" /home/<USER>/bin/qd
```

### 步骤3: 修复路径配置
```bash
# 如果路径不正确，修复它
sed -i 's|readonly GAME_HOME=.*|readonly GAME_HOME="/home/<USER>"|g' /home/<USER>/bin/qd
```

### 步骤4: 设置权限
```bash
chmod +x /home/<USER>/bin/qd
chmod +x /home/<USER>/server1/qd.sh
```

### 步骤5: 测试启动
```bash
cd /home/<USER>
./bin/qd help
./bin/qd dk
./bin/qd 1
```

## 📞 技术支持

如果问题仍然存在，请提供以下信息：

1. **目录结构**:
```bash
ls -la /home/<USER>/
ls -la /home/<USER>/server1/
```

2. **qd脚本配置**:
```bash
grep -A5 -B5 "GAME_HOME" /home/<USER>/bin/qd
```

3. **错误信息**:
```bash
cd /home/<USER>
./bin/qd 1 2>&1
```

## ✅ 总结

根据您的服务器结构，这是一个标准的游戏服务器部署。主要需要确保：

1. ✅ qd脚本中的GAME_HOME指向 `/home/<USER>
2. ✅ 脚本具有执行权限
3. ✅ 从正确的目录执行命令
4. ✅ 大区目录结构完整

**推荐操作**: 从 `/home/<USER>/bin/qd 1` 来启动大区1。
