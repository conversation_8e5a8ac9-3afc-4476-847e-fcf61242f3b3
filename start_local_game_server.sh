#!/bin/bash
#==============================================================================
# 一键启动本地游戏服务器脚本
# 自动检查环境、设置环境、启动服务器并监控日志
#==============================================================================

set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly PURPLE='\033[0;35m'
readonly NC='\033[0m'

# 路径配置
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly PROJECT_ROOT="$SCRIPT_DIR"

# 显示标题
show_title() {
    clear
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║                    GSXDB 本地游戏服务器                      ║${NC}"
    echo -e "${PURPLE}║                      一键启动工具                            ║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 进度条
progress_bar() {
    local duration=$1
    local message=$2
    local width=50
    
    echo -ne "${CYAN}$message${NC} ["
    
    for ((i=0; i<=width; i++)); do
        sleep $(echo "scale=3; $duration/$width" | bc 2>/dev/null || echo "0.1")
        echo -ne "█"
    done
    
    echo -e "] ${GREEN}完成${NC}"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查Python
    if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
        missing_deps+=("python3")
    fi
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        missing_deps+=("java")
    fi
    
    # 检查bc (用于计算)
    if ! command -v bc &> /dev/null; then
        log_warning "bc工具未安装，将使用简化计算"
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_error "缺少必要依赖: ${missing_deps[*]}"
        echo
        echo -e "${YELLOW}请安装缺少的依赖:${NC}"
        for dep in "${missing_deps[@]}"; do
            case $dep in
                "python3")
                    echo "  - Python 3: https://www.python.org/downloads/"
                    ;;
                "java")
                    echo "  - Java 8+: https://adoptopenjdk.net/"
                    ;;
            esac
        done
        return 1
    fi
    
    log_success "依赖检查通过"
    return 0
}

# 运行环境检查
run_environment_check() {
    log_info "运行环境检查..."
    
    local python_cmd="python3"
    if ! command -v python3 &> /dev/null; then
        python_cmd="python"
    fi
    
    if [ -f "$PROJECT_ROOT/scripts/tools/check_game_environment.py" ]; then
        if $python_cmd "$PROJECT_ROOT/scripts/tools/check_game_environment.py"; then
            log_success "环境检查通过"
            return 0
        else
            log_error "环境检查失败"
            echo
            echo -e "${YELLOW}请根据上述提示解决环境问题后重试${NC}"
            return 1
        fi
    else
        log_warning "环境检查脚本不存在，跳过详细检查"
        return 0
    fi
}

# 设置本地环境
setup_local_environment() {
    log_info "设置本地游戏环境..."
    
    if [ -f "$PROJECT_ROOT/scripts/game/local_game_server.sh" ]; then
        if bash "$PROJECT_ROOT/scripts/game/local_game_server.sh" setup; then
            log_success "本地环境设置完成"
            return 0
        else
            log_error "本地环境设置失败"
            return 1
        fi
    else
        log_error "本地游戏服务器脚本不存在"
        return 1
    fi
}

# 启动游戏服务器
start_game_server() {
    log_info "启动游戏服务器..."
    
    if [ -f "$PROJECT_ROOT/scripts/game/local_game_server.sh" ]; then
        if bash "$PROJECT_ROOT/scripts/game/local_game_server.sh" start; then
            log_success "游戏服务器启动成功"
            return 0
        else
            log_error "游戏服务器启动失败"
            return 1
        fi
    else
        log_error "本地游戏服务器脚本不存在"
        return 1
    fi
}

# 启动日志监控
start_log_monitoring() {
    log_info "启动日志监控..."
    
    local python_cmd="python3"
    if ! command -v python3 &> /dev/null; then
        python_cmd="python"
    fi
    
    if [ -f "$PROJECT_ROOT/scripts/tools/game_log_monitor.py" ]; then
        echo
        echo -e "${CYAN}选择监控模式:${NC}"
        echo -e "${YELLOW}1.${NC} 实时监控 (推荐)"
        echo -e "${YELLOW}2.${NC} 分析现有日志"
        echo -e "${YELLOW}3.${NC} 跳过监控"
        echo
        echo -ne "${CYAN}请选择 [1-3]: ${NC}"
        read -r choice
        
        case $choice in
            1)
                echo
                log_info "启动实时日志监控..."
                echo -e "${YELLOW}提示: 按 Ctrl+C 可停止监控${NC}"
                echo
                sleep 2
                $python_cmd "$PROJECT_ROOT/scripts/tools/game_log_monitor.py" monitor --auto-fix
                ;;
            2)
                echo
                log_info "分析现有日志..."
                $python_cmd "$PROJECT_ROOT/scripts/tools/game_log_monitor.py" analyze
                ;;
            3)
                log_info "跳过日志监控"
                ;;
            *)
                log_warning "无效选择，跳过监控"
                ;;
        esac
    else
        log_warning "日志监控脚本不存在，跳过监控"
    fi
}

# 显示服务状态
show_service_status() {
    echo
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                        服务状态                              ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    
    if [ -f "$PROJECT_ROOT/scripts/game/local_game_server.sh" ]; then
        bash "$PROJECT_ROOT/scripts/game/local_game_server.sh" status
    fi
    
    echo
    echo -e "${GREEN}🎮 游戏服务器管理命令:${NC}"
    echo -e "${YELLOW}  查看状态:${NC} ./scripts/game/local_game_server.sh status"
    echo -e "${YELLOW}  停止服务:${NC} ./scripts/game/local_game_server.sh stop"
    echo -e "${YELLOW}  查看日志:${NC} ./scripts/game/local_game_server.sh logs"
    echo -e "${YELLOW}  清理环境:${NC} ./scripts/game/local_game_server.sh clean"
    echo
    echo -e "${GREEN}📊 日志监控命令:${NC}"
    echo -e "${YELLOW}  实时监控:${NC} python3 scripts/tools/game_log_monitor.py monitor"
    echo -e "${YELLOW}  分析日志:${NC} python3 scripts/tools/game_log_monitor.py analyze"
    echo
}

# 显示帮助信息
show_help() {
    echo -e "${BLUE}GSXDB 本地游戏服务器一键启动工具${NC}"
    echo
    echo -e "${YELLOW}用法:${NC}"
    echo "  $0 [选项]"
    echo
    echo -e "${YELLOW}选项:${NC}"
    echo "  --check-only    仅运行环境检查"
    echo "  --setup-only    仅设置本地环境"
    echo "  --no-monitor    启动服务器但不监控日志"
    echo "  --help          显示此帮助信息"
    echo
    echo -e "${YELLOW}功能:${NC}"
    echo "  1. 自动检查系统环境和依赖"
    echo "  2. 设置本地游戏服务器环境"
    echo "  3. 启动游戏服务器和相关服务"
    echo "  4. 实时监控日志并自动诊断问题"
    echo
    echo -e "${YELLOW}支持的游戏端口:${NC}"
    echo "  - 名称服务: 22200"
    echo "  - SDK服务: 29200"
    echo "  - 大区1 GM端口: 41001"
    echo "  - 网关端口: 42001"
    echo "  - 游戏端口: 43001"
    echo
}

# 主函数
main() {
    show_title
    
    # 解析命令行参数
    local check_only=false
    local setup_only=false
    local no_monitor=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --check-only)
                check_only=true
                shift
                ;;
            --setup-only)
                setup_only=true
                shift
                ;;
            --no-monitor)
                no_monitor=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 步骤1: 检查依赖
    if ! check_dependencies; then
        exit 1
    fi
    
    progress_bar 1 "检查系统依赖"
    
    # 步骤2: 运行环境检查
    if ! run_environment_check; then
        if [ "$check_only" = true ]; then
            exit 1
        fi
        
        echo
        echo -ne "${YELLOW}环境检查失败，是否继续? (y/N): ${NC}"
        read -r continue_anyway
        if [[ ! "$continue_anyway" =~ ^[Yy]$ ]]; then
            log_info "用户取消操作"
            exit 1
        fi
    fi
    
    if [ "$check_only" = true ]; then
        log_success "环境检查完成"
        exit 0
    fi
    
    progress_bar 2 "环境检查"
    
    # 步骤3: 设置本地环境
    if ! setup_local_environment; then
        log_error "环境设置失败"
        exit 1
    fi
    
    if [ "$setup_only" = true ]; then
        log_success "环境设置完成"
        exit 0
    fi
    
    progress_bar 2 "设置本地环境"
    
    # 步骤4: 启动游戏服务器
    if ! start_game_server; then
        log_error "游戏服务器启动失败"
        echo
        echo -e "${YELLOW}建议操作:${NC}"
        echo "1. 检查日志: python3 scripts/tools/game_log_monitor.py analyze"
        echo "2. 查看状态: ./scripts/game/local_game_server.sh status"
        echo "3. 重新运行环境检查: python3 scripts/tools/check_game_environment.py"
        exit 1
    fi
    
    progress_bar 3 "启动游戏服务器"
    
    # 显示服务状态
    show_service_status
    
    # 步骤5: 启动日志监控
    if [ "$no_monitor" = false ]; then
        start_log_monitoring
    else
        log_info "跳过日志监控"
    fi
    
    echo
    log_success "🎉 本地游戏服务器启动完成！"
    echo
    echo -e "${GREEN}🎮 现在您可以:${NC}"
    echo -e "${YELLOW}  1.${NC} 连接到游戏服务器进行测试"
    echo -e "${YELLOW}  2.${NC} 使用GM工具管理游戏"
    echo -e "${YELLOW}  3.${NC} 查看实时日志监控"
    echo
}

# 执行主函数
main "$@"
