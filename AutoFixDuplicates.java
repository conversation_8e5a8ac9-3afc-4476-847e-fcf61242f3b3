import java.io.*;
import java.util.*;
import java.util.regex.*;

/**
 * 自动修复 JsFunManager.java 中所有重复的 Map key 问题
 * Automatically fixes all duplicate Map keys in JsFunManager.java
 */
public class AutoFixDuplicates {
    
    private static class KeyValuePair {
        String key;
        int value;
        int lineNumber;
        String fullLine;
        
        KeyValuePair(String key, int value, int lineNumber, String fullLine) {
            this.key = key;
            this.value = value;
            this.lineNumber = lineNumber;
            this.fullLine = fullLine;
        }
    }
    
    public static void main(String[] args) {
        String filePath = "src/fire/script/JsFunManager.java";
        autoFixAllDuplicates(filePath);
    }
    
    public static void autoFixAllDuplicates(String filePath) {
        System.out.println("Starting automatic duplicate key fixing...");
        System.out.println("============================================================");
        
        try {
            // Step 1: Read file and analyze duplicates
            List<String> lines = readFileLines(filePath);
            Map<String, List<KeyValuePair>> keyMap = analyzeDuplicates(lines);
            
            // Step 2: Identify duplicates to remove
            Set<Integer> linesToRemove = identifyLinesToRemove(keyMap);
            
            // Step 3: Create fixed content
            List<String> fixedLines = createFixedContent(lines, linesToRemove);
            
            // Step 4: Write back to file
            writeFileLines(filePath, fixedLines);
            
            // Step 5: Report results
            reportResults(keyMap, linesToRemove);
            
        } catch (IOException e) {
            System.err.println("Error processing file: " + e.getMessage());
        }
    }
    
    private static List<String> readFileLines(String filePath) throws IOException {
        List<String> lines = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                lines.add(line);
            }
        }
        return lines;
    }
    
    private static Map<String, List<KeyValuePair>> analyzeDuplicates(List<String> lines) {
        Map<String, List<KeyValuePair>> keyMap = new HashMap<>();
        Pattern pattern = Pattern.compile("funMap\\.put\\(\"([^\"]+)\",\\s*(\\d+)\\);");
        
        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i);
            Matcher matcher = pattern.matcher(line);
            
            if (matcher.find()) {
                String key = matcher.group(1);
                int value = Integer.parseInt(matcher.group(2));
                
                KeyValuePair pair = new KeyValuePair(key, value, i + 1, line);
                keyMap.computeIfAbsent(key, k -> new ArrayList<>()).add(pair);
            }
        }
        
        return keyMap;
    }
    
    private static Set<Integer> identifyLinesToRemove(Map<String, List<KeyValuePair>> keyMap) {
        Set<Integer> linesToRemove = new HashSet<>();
        int duplicateKeys = 0;
        int conflictKeys = 0;
        
        System.out.println("Analyzing duplicates...");
        
        for (Map.Entry<String, List<KeyValuePair>> entry : keyMap.entrySet()) {
            List<KeyValuePair> pairs = entry.getValue();
            
            if (pairs.size() > 1) {
                duplicateKeys++;
                
                // Check for value conflicts
                Set<Integer> values = new HashSet<>();
                for (KeyValuePair pair : pairs) {
                    values.add(pair.value);
                }
                
                if (values.size() > 1) {
                    conflictKeys++;
                    System.out.println("WARNING: Value conflict for key: \"" + entry.getKey() + "\"");
                    for (KeyValuePair pair : pairs) {
                        System.out.println("  Line " + pair.lineNumber + ": " + pair.value);
                    }
                    System.out.println("  Keeping first occurrence, removing others");
                }
                
                // Remove all duplicates except the first one
                for (int i = 1; i < pairs.size(); i++) {
                    linesToRemove.add(pairs.get(i).lineNumber - 1); // Convert to 0-based index
                }
            }
        }
        
        System.out.println("Found " + duplicateKeys + " duplicate keys");
        System.out.println("Found " + conflictKeys + " value conflicts");
        System.out.println("Will remove " + linesToRemove.size() + " duplicate lines");
        System.out.println();
        
        return linesToRemove;
    }
    
    private static List<String> createFixedContent(List<String> lines, Set<Integer> linesToRemove) {
        List<String> fixedLines = new ArrayList<>();
        
        for (int i = 0; i < lines.size(); i++) {
            if (!linesToRemove.contains(i)) {
                fixedLines.add(lines.get(i));
            }
        }
        
        return fixedLines;
    }
    
    private static void writeFileLines(String filePath, List<String> lines) throws IOException {
        try (PrintWriter writer = new PrintWriter(new FileWriter(filePath))) {
            for (String line : lines) {
                writer.println(line);
            }
        }
    }
    
    private static void reportResults(Map<String, List<KeyValuePair>> keyMap, Set<Integer> linesToRemove) {
        int totalEntries = keyMap.values().stream().mapToInt(List::size).sum();
        int uniqueKeys = keyMap.size();
        int duplicateKeys = (int) keyMap.entrySet().stream()
            .filter(entry -> entry.getValue().size() > 1)
            .count();
        
        System.out.println("RESULTS:");
        System.out.println("========");
        System.out.println("Original entries: " + totalEntries);
        System.out.println("Unique keys: " + uniqueKeys);
        System.out.println("Duplicate keys fixed: " + duplicateKeys);
        System.out.println("Lines removed: " + linesToRemove.size());
        System.out.println("Final entries: " + (totalEntries - linesToRemove.size()));
        System.out.println();
        System.out.println("SUCCESS: All duplicate keys have been automatically fixed!");
        System.out.println("SUCCESS: File has been updated successfully!");
    }
}
