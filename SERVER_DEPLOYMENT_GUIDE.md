
# GSXDB游戏服务器部署指南

## 📋 验证结果总结

**验证时间**: 2025-07-30 18:44:02
**总体得分**: 98.0%
**部署状态**: ✅ 完全就绪

## 🚀 服务器部署步骤

### 1. 上传文件到服务器
```bash
# 上传整个centos7.6_game_server目录
scp -r centos7.6_game_server/ user@server:/home/<USER>/

# 或使用rsync同步
rsync -avz centos7.6_game_server/ user@server:/home/<USER>/
```

### 2. 设置文件权限
```bash
# 登录服务器
ssh user@server

# 设置目录权限
find /home/<USER>
find /home/<USER>

# 设置脚本执行权限
find /home/<USER>"*.sh" -exec chmod 755 {} \;
chmod 755 /home/<USER>/bin/qd /home/<USER>/bin/qd_fixed

# 设置游戏程序权限
chmod 755 /home/<USER>/game/server*/gate_server/gateserver 2>/dev/null || true
chmod 755 /home/<USER>/game/server*/proxy_server/proxyserver 2>/dev/null || true
```

### 3. 验证脚本语法
```bash
# 验证QD脚本语法
bash -n /home/<USER>/bin/qd
bash -n /home/<USER>/bin/qd_fixed

# 测试帮助功能
/home/<USER>/bin/qd help
/home/<USER>/bin/qd_fixed help
```

### 4. 启动游戏服务器
```bash
# 方案1: 使用原始QD脚本
/home/<USER>/bin/qd 0        # 启动公共服务
/home/<USER>/bin/qd 1        # 启动大区1

# 方案2: 使用修复版QD脚本 (推荐)
/home/<USER>/bin/qd_fixed start-all    # 一键启动所有服务
```

### 5. 验证服务状态
```bash
# 检查服务状态
/home/<USER>/bin/qd status
/home/<USER>/bin/qd_fixed status

# 检查端口监听
netstat -tlnp | grep -E ":(22200|29200|41001|42001|43001)"

# 检查进程
ps aux | grep -E "(java|gateserver|proxyserver)" | grep -v grep
```

### 6. 测试机器人功能
```bash
# 启动机器人 (原始QD脚本)
/home/<USER>/bin/qd robot

# 启动机器人 (修复版QD脚本)
/home/<USER>/bin/qd_fixed robot 1 10 1    # 大区1启动10个机器人

# 查看机器人状态
/home/<USER>/bin/qd_fixed robotstatus

# 查看机器人日志
/home/<USER>/bin/qd_fixed robotlog 1
```

## 🔧 故障排除

### 常见问题解决
1. **端口被占用**: `netstat -tlnp | grep 端口号` 查看占用进程
2. **权限不足**: 确保文件权限设置正确
3. **Java内存不足**: 修改JVM参数或增加系统内存
4. **数据库锁定**: 删除 `mkdb/mkdb.inuse` 文件

### 日志位置
- 系统日志: `/home/<USER>/script.log`
- 游戏日志: `/home/<USER>/game/server1/game_server/logs/`
- 机器人日志: `/home/<USER>/game/server1/game_server/robot/logs/`

## 📊 端口说明
- 名称服务: 22200
- SDK服务: 29200
- 大区1 GM: 41001
- 大区1网关: 42001
- 大区1游戏: 43001

## ✅ 验证清单
- [ ] 所有文件上传完成
- [ ] 文件权限设置正确
- [ ] 脚本语法检查通过
- [ ] 公共服务启动成功
- [ ] 大区1启动成功
- [ ] 机器人功能正常
- [ ] 所有端口正常监听

---
**🎮 部署完成后，游戏服务器即可投入使用！**
