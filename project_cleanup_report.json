{"timestamp": "2025-07-26T19:43:05.512124", "cleanup_steps": ["临时文件清理", "EffectRole修复", "重新编译", "修复验证"], "files_cleaned": [".idea\\workspace.xml"], "issues_fixed": [], "compilation_results": {"EffectRole.java": {"success": true, "return_code": 0, "stdout": "", "stderr": ""}, "IJavaScriptEngine.java": {"success": true, "return_code": 0, "stdout": "", "stderr": ""}, "AbstractJSEngine.java": {"success": false, "return_code": 1, "stdout": "", "stderr": "src\\fire\\TestSignal.java:13: 警告: Signal是内部专用 API, 可能会在未来发行版中删除\nimport sun.misc.Signal;\n               ^\nsrc\\fire\\TestSignal.java:14: 警告: SignalHandler是内部专用 API, 可能会在未来发行版中删除\nimport sun.misc.SignalHand"}, "SceneJSEngine.java": {"success": false, "return_code": 1, "stdout": "", "stderr": "src\\fire\\TestSignal.java:13: 警告: Signal是内部专用 API, 可能会在未来发行版中删除\nimport sun.misc.Signal;\n               ^\nsrc\\fire\\TestSignal.java:14: 警告: SignalHandler是内部专用 API, 可能会在未来发行版中删除\nimport sun.misc.SignalHand"}, "FightJSEngine.java": {"success": false, "return_code": 1, "stdout": "", "stderr": "src\\fire\\TestSignal.java:13: 警告: Signal是内部专用 API, 可能会在未来发行版中删除\nimport sun.misc.Signal;\n               ^\nsrc\\fire\\TestSignal.java:14: 警告: SignalHandler是内部专用 API, 可能会在未来发行版中删除\nimport sun.misc.SignalHand"}, "JavaScriptEngineFix.java": {"success": true, "return_code": 0, "stdout": "", "stderr": ""}}, "cleanup_success": false, "verification_results": {"EffectRole": {"class_exists": true, "size": 390}, "IJavaScriptEngine.class": {"exists": true, "size": 562}, "AbstractJSEngine.class": {"exists": true, "size": 4251}, "SceneJSEngine.class": {"exists": true, "size": 2556}, "FightJSEngine.class": {"exists": true, "size": 5457}, "JavaScriptEngineFix.class": {"exists": true, "size": 5433}}, "cleanup_summary": {"cleanup_date": "2025-07-26T19:43:36.628545", "total_steps": 4, "files_cleaned": 1, "issues_fixed": 0, "compilation_success_rate": 50.0}}