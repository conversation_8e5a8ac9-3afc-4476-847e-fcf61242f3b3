
package fire.pb.friends;

// {{{ RPCGEN_IMPORT_BEGIN
// {{{ DO NOT EDIT THIS
import com.locojoy.base.Marshal.OctetsStream;
import com.locojoy.base.Marshal.MarshalException;

abstract class __CGetSpaceInfo__ extends mkio.Protocol { }

/** 获取某角色空间数据
*/
// DO NOT EDIT THIS }}}
// RPCGEN_IMPORT_END }}}

public class CGetSpaceInfo extends __CGetSpaceInfo__ {
	@Override
	protected void process() {
		// protocol handle
		final long cur_roleid = gnet.link.Onlines.getInstance().findRoleid(this);
		if (cur_roleid < 0)
			return;
		xbean.RoleSpace rs = xtable.Rolespaces.select(this.roleid);
		SGetSpaceInfo snd = new SGetSpaceInfo();
		if(rs != null) {
			snd.giftnum = rs.getGift();
			snd.popularity = rs.getPopularity();
			snd.revnum = rs.getRecvgift();
		} else {
			snd.giftnum = 0;
			snd.popularity = 0;
			snd.revnum = 0;
		}
		gnet.link.Onlines.getInstance().send(cur_roleid, snd);
	}

	// {{{ RPCGEN_DEFINE_BEGIN
	// {{{ DO NOT EDIT THIS
	public static final int PROTOCOL_TYPE = 806639;

	public int getType() {
		return 806639;
	}

	public long roleid; // 目标空间的角色id

	public CGetSpaceInfo() {
	}

	public CGetSpaceInfo(long _roleid_) {
		this.roleid = _roleid_;
	}

	public final boolean _validator_() {
		return true;
	}

	public OctetsStream marshal(OctetsStream _os_) {
		if (!_validator_()) {
			throw new VerifyError("validator failed");
		}
		_os_.marshal(roleid);
		return _os_;
	}

	public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
		roleid = _os_.unmarshal_long();
		if (!_validator_()) {
			throw new VerifyError("validator failed");
		}
		return _os_;
	}

	public boolean equals(Object _o1_) {
		if (_o1_ == this) return true;
		if (_o1_ instanceof CGetSpaceInfo) {
			CGetSpaceInfo _o_ = (CGetSpaceInfo)_o1_;
			if (roleid != _o_.roleid) return false;
			return true;
		}
		return false;
	}

	public int hashCode() {
		int _h_ = 0;
		_h_ += (int)roleid;
		return _h_;
	}

	public String toString() {
		StringBuilder _sb_ = new StringBuilder();
		_sb_.append("(");
		_sb_.append(roleid).append(",");
		_sb_.append(")");
		return _sb_.toString();
	}

	public int compareTo(CGetSpaceInfo _o_) {
		if (_o_ == this) return 0;
		int _c_ = 0;
		_c_ = Long.signum(roleid - _o_.roleid);
		if (0 != _c_) return _c_;
		return _c_;
	}

	// DO NOT EDIT THIS }}}
	// RPCGEN_DEFINE_END }}}

}

