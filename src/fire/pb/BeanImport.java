
package fire.pb;

// {{{ RPCGEN_IMPORT_BEGIN
// {{{ DO NOT EDIT THIS
import com.locojoy.base.Marshal.OctetsStream;
import com.locojoy.base.Marshal.MarshalException;

abstract class __BeanImport__ extends mkio.Protocol { }

// DO NOT EDIT THIS }}}
// RPCGEN_IMPORT_END }}}

public class BeanImport extends __BeanImport__ {
	@Override
	protected void process() {
		// protocol handle
	}

	// {{{ RPCGEN_DEFINE_BEGIN
	// {{{ DO NOT EDIT THIS
	public static final int PROTOCOL_TYPE = 786432;

	public int getType() {
		return 786432;
	}

	public fire.pb.item.BagTypes b1;
	public fire.pb.KickErrConst b4;
	public fire.pb.DataInit b8;
	public fire.pb.SysConfigType b9;
	public fire.pb.pet.PetTypeEnum b10;
	public fire.pb.pet.PetColumnTypes b11;
	public fire.pb.battle.OperationType b14;
	public fire.pb.battle.ResultType b15;
	public fire.pb.battle.BattlerType b16;
	public fire.pb.team.TeamMemberState b17;
	public fire.pb.npc.NpcServices b18;
	public fire.pb.team.TeamError b20;
	public fire.pb.title.TitleInfo b21;
	public fire.pb.title.TitleError b22;
	public fire.pb.npc.NpcHealType b23;
	public fire.pb.attr.AttrType b27;
	public fire.pb.attr.EffectType b28;
	public fire.pb.item.IDType b29;
	public fire.pb.move.PickItemTypes b31;
	public fire.pb.pet.PetChatLimit b33;
	public fire.pb.ErrorCodes b43;
	public fire.pb.npc.MultiExpError b49;
	public fire.pb.npc.BattleToNpcError b54;
	public fire.pb.circletask.SpecialQuestType b55;
	public fire.pb.circletask.SpecialQuestNpcMsg b56;
	public fire.pb.circletask.SpecialQuestID b58;
	public fire.pb.npc.SubmitType b61;
	public fire.pb.battle.BattleAimRelation b63;
	public fire.pb.npc.NpcBuyType b64;
	public fire.pb.school.ShouXiLimit b65;
	public fire.pb.school.ShouXiMsgID b66;
	public fire.pb.attr.FightAttrType b72;
	public fire.pb.skill.SkillInfo b73;
	public fire.pb.npc.ImpExamType b74;
	public fire.pb.mission.AllowTeam b75;
	public fire.pb.talk.ChatMsgId b76;
	public fire.pb.battle.BattleType b87;
	public fire.pb.npc.TransmitTypes b89;
	public fire.pb.master.RelationType b90;
	public fire.pb.master.OnLineState b91;
	public fire.pb.master.DataConfig b92;
	public fire.pb.master.InitiativeType b97;
	public fire.pb.mission.FairylandStatus b101;
	public fire.pb.KeyCounterIndex b102;
	public fire.pb.pet.PetColour b105;
	public fire.pb.BeginnerTipType b106;
	public fire.pb.mission.ReadTimeType b109;
	public fire.pb.move.SceneState b111;
	public fire.pb.move.DynamicSceneType b112;
	public fire.pb.RoleState b113;
	public fire.pb.battle.BattleEnviroment b114;
	public fire.pb.LogPriority b116;
	public fire.pb.ranklist.LevelRankData b120;
	public fire.pb.team.TeamState b135;
	public fire.pb.ranklist.PetGradeRankData b148;
	public fire.pb.move.SpriteComponents b151;
	public fire.pb.item.EquipItemType b152;
	public fire.pb.AU2GameType b156;
	public fire.pb.Pet b168;
	public fire.pb.Petskill b169;
	public fire.pb.clan.ClanPositionType b170;
	public fire.pb.clan.ClanDMapInfo b174;
	public fire.pb.clan.ClanSkill b176;
	public fire.pb.clan.ClanInfo b181;
	public fire.pb.circletask.CircTaskClass b191;
	public fire.pb.shop.ShopBuyType b192;
	public fire.pb.hook.HookDataUpdateEnum b193;
	public fire.pb.hook.HookGetDpointResEnum b194;
	public fire.pb.hook.HookMsg2ClientEnum b195;
	public fire.pb.hook.HookFreeDpointResEnum b196;
	public fire.pb.activity.ActivityType b197;
	public fire.pb.circletask.CircTaskGotoType b198;
	public fire.pb.game.MoneyType b199;
	public fire.pb.npc.NpcServiceMappingTypes b200;
	public fire.pb.shop.MarketType b201;
	public fire.pb.attr.ScoreType b202;
	public fire.pb.shop.GoodsType b203;
	public fire.pb.clan.FireReasonType b204;
	public fire.pb.attr.RoleCurrency b205;
	public fire.pb.battle.QCmodelType b206;
	public fire.pb.npc.ImpExamAssistType b207;
	public fire.pb.skill.AssistSkill b208;
	public fire.pb.skill.EquipSkill b209;
	public fire.pb.battle.ResultItem b210;
	public fire.pb.battle.livedie.LDmodelType b211;
	public fire.pb.fushi.redpack.RedPackType b212;
	public fire.pb.fushi.redpack.RedPackState b213;
	public fire.pb.move.PickUpItem b214;
	public fire.pb.RoleInfo b215;
	public fire.pb.talk.FunModelType b216;
	public fire.pb.FunOpenCloseType b217;

	public BeanImport() {
		b1 = new fire.pb.item.BagTypes();
		b4 = new fire.pb.KickErrConst();
		b8 = new fire.pb.DataInit();
		b9 = new fire.pb.SysConfigType();
		b10 = new fire.pb.pet.PetTypeEnum();
		b11 = new fire.pb.pet.PetColumnTypes();
		b14 = new fire.pb.battle.OperationType();
		b15 = new fire.pb.battle.ResultType();
		b16 = new fire.pb.battle.BattlerType();
		b17 = new fire.pb.team.TeamMemberState();
		b18 = new fire.pb.npc.NpcServices();
		b20 = new fire.pb.team.TeamError();
		b21 = new fire.pb.title.TitleInfo();
		b22 = new fire.pb.title.TitleError();
		b23 = new fire.pb.npc.NpcHealType();
		b27 = new fire.pb.attr.AttrType();
		b28 = new fire.pb.attr.EffectType();
		b29 = new fire.pb.item.IDType();
		b31 = new fire.pb.move.PickItemTypes();
		b33 = new fire.pb.pet.PetChatLimit();
		b43 = new fire.pb.ErrorCodes();
		b49 = new fire.pb.npc.MultiExpError();
		b54 = new fire.pb.npc.BattleToNpcError();
		b55 = new fire.pb.circletask.SpecialQuestType();
		b56 = new fire.pb.circletask.SpecialQuestNpcMsg();
		b58 = new fire.pb.circletask.SpecialQuestID();
		b61 = new fire.pb.npc.SubmitType();
		b63 = new fire.pb.battle.BattleAimRelation();
		b64 = new fire.pb.npc.NpcBuyType();
		b65 = new fire.pb.school.ShouXiLimit();
		b66 = new fire.pb.school.ShouXiMsgID();
		b72 = new fire.pb.attr.FightAttrType();
		b73 = new fire.pb.skill.SkillInfo();
		b74 = new fire.pb.npc.ImpExamType();
		b75 = new fire.pb.mission.AllowTeam();
		b76 = new fire.pb.talk.ChatMsgId();
		b87 = new fire.pb.battle.BattleType();
		b89 = new fire.pb.npc.TransmitTypes();
		b90 = new fire.pb.master.RelationType();
		b91 = new fire.pb.master.OnLineState();
		b92 = new fire.pb.master.DataConfig();
		b97 = new fire.pb.master.InitiativeType();
		b101 = new fire.pb.mission.FairylandStatus();
		b102 = new fire.pb.KeyCounterIndex();
		b105 = new fire.pb.pet.PetColour();
		b106 = new fire.pb.BeginnerTipType();
		b109 = new fire.pb.mission.ReadTimeType();
		b111 = new fire.pb.move.SceneState();
		b112 = new fire.pb.move.DynamicSceneType();
		b113 = new fire.pb.RoleState();
		b114 = new fire.pb.battle.BattleEnviroment();
		b116 = new fire.pb.LogPriority();
		b120 = new fire.pb.ranklist.LevelRankData();
		b135 = new fire.pb.team.TeamState();
		b148 = new fire.pb.ranklist.PetGradeRankData();
		b151 = new fire.pb.move.SpriteComponents();
		b152 = new fire.pb.item.EquipItemType();
		b156 = new fire.pb.AU2GameType();
		b168 = new fire.pb.Pet();
		b169 = new fire.pb.Petskill();
		b170 = new fire.pb.clan.ClanPositionType();
		b174 = new fire.pb.clan.ClanDMapInfo();
		b176 = new fire.pb.clan.ClanSkill();
		b181 = new fire.pb.clan.ClanInfo();
		b191 = new fire.pb.circletask.CircTaskClass();
		b192 = new fire.pb.shop.ShopBuyType();
		b193 = new fire.pb.hook.HookDataUpdateEnum();
		b194 = new fire.pb.hook.HookGetDpointResEnum();
		b195 = new fire.pb.hook.HookMsg2ClientEnum();
		b196 = new fire.pb.hook.HookFreeDpointResEnum();
		b197 = new fire.pb.activity.ActivityType();
		b198 = new fire.pb.circletask.CircTaskGotoType();
		b199 = new fire.pb.game.MoneyType();
		b200 = new fire.pb.npc.NpcServiceMappingTypes();
		b201 = new fire.pb.shop.MarketType();
		b202 = new fire.pb.attr.ScoreType();
		b203 = new fire.pb.shop.GoodsType();
		b204 = new fire.pb.clan.FireReasonType();
		b205 = new fire.pb.attr.RoleCurrency();
		b206 = new fire.pb.battle.QCmodelType();
		b207 = new fire.pb.npc.ImpExamAssistType();
		b208 = new fire.pb.skill.AssistSkill();
		b209 = new fire.pb.skill.EquipSkill();
		b210 = new fire.pb.battle.ResultItem();
		b211 = new fire.pb.battle.livedie.LDmodelType();
		b212 = new fire.pb.fushi.redpack.RedPackType();
		b213 = new fire.pb.fushi.redpack.RedPackState();
		b214 = new fire.pb.move.PickUpItem();
		b215 = new fire.pb.RoleInfo();
		b216 = new fire.pb.talk.FunModelType();
		b217 = new fire.pb.FunOpenCloseType();
	}

	public BeanImport(fire.pb.item.BagTypes _b1_, fire.pb.KickErrConst _b4_, fire.pb.DataInit _b8_, fire.pb.SysConfigType _b9_, fire.pb.pet.PetTypeEnum _b10_, fire.pb.pet.PetColumnTypes _b11_, fire.pb.battle.OperationType _b14_, fire.pb.battle.ResultType _b15_, fire.pb.battle.BattlerType _b16_, fire.pb.team.TeamMemberState _b17_, fire.pb.npc.NpcServices _b18_, fire.pb.team.TeamError _b20_, fire.pb.title.TitleInfo _b21_, fire.pb.title.TitleError _b22_, fire.pb.npc.NpcHealType _b23_, fire.pb.attr.AttrType _b27_, fire.pb.attr.EffectType _b28_, fire.pb.item.IDType _b29_, fire.pb.move.PickItemTypes _b31_, fire.pb.pet.PetChatLimit _b33_, fire.pb.ErrorCodes _b43_, fire.pb.npc.MultiExpError _b49_, fire.pb.npc.BattleToNpcError _b54_, fire.pb.circletask.SpecialQuestType _b55_, fire.pb.circletask.SpecialQuestNpcMsg _b56_, fire.pb.circletask.SpecialQuestID _b58_, fire.pb.npc.SubmitType _b61_, fire.pb.battle.BattleAimRelation _b63_, fire.pb.npc.NpcBuyType _b64_, fire.pb.school.ShouXiLimit _b65_, fire.pb.school.ShouXiMsgID _b66_, fire.pb.attr.FightAttrType _b72_, fire.pb.skill.SkillInfo _b73_, fire.pb.npc.ImpExamType _b74_, fire.pb.mission.AllowTeam _b75_, fire.pb.talk.ChatMsgId _b76_, fire.pb.battle.BattleType _b87_, fire.pb.npc.TransmitTypes _b89_, fire.pb.master.RelationType _b90_, fire.pb.master.OnLineState _b91_, fire.pb.master.DataConfig _b92_, fire.pb.master.InitiativeType _b97_, fire.pb.mission.FairylandStatus _b101_, fire.pb.KeyCounterIndex _b102_, fire.pb.pet.PetColour _b105_, fire.pb.BeginnerTipType _b106_, fire.pb.mission.ReadTimeType _b109_, fire.pb.move.SceneState _b111_, fire.pb.move.DynamicSceneType _b112_, fire.pb.RoleState _b113_, fire.pb.battle.BattleEnviroment _b114_, fire.pb.LogPriority _b116_, fire.pb.ranklist.LevelRankData _b120_, fire.pb.team.TeamState _b135_, fire.pb.ranklist.PetGradeRankData _b148_, fire.pb.move.SpriteComponents _b151_, fire.pb.item.EquipItemType _b152_, fire.pb.AU2GameType _b156_, fire.pb.Pet _b168_, fire.pb.Petskill _b169_, fire.pb.clan.ClanPositionType _b170_, fire.pb.clan.ClanDMapInfo _b174_, fire.pb.clan.ClanSkill _b176_, fire.pb.clan.ClanInfo _b181_, fire.pb.circletask.CircTaskClass _b191_, fire.pb.shop.ShopBuyType _b192_, fire.pb.hook.HookDataUpdateEnum _b193_, fire.pb.hook.HookGetDpointResEnum _b194_, fire.pb.hook.HookMsg2ClientEnum _b195_, fire.pb.hook.HookFreeDpointResEnum _b196_, fire.pb.activity.ActivityType _b197_, fire.pb.circletask.CircTaskGotoType _b198_, fire.pb.game.MoneyType _b199_, fire.pb.npc.NpcServiceMappingTypes _b200_, fire.pb.shop.MarketType _b201_, fire.pb.attr.ScoreType _b202_, fire.pb.shop.GoodsType _b203_, fire.pb.clan.FireReasonType _b204_, fire.pb.attr.RoleCurrency _b205_, fire.pb.battle.QCmodelType _b206_, fire.pb.npc.ImpExamAssistType _b207_, fire.pb.skill.AssistSkill _b208_, fire.pb.skill.EquipSkill _b209_, fire.pb.battle.ResultItem _b210_, fire.pb.battle.livedie.LDmodelType _b211_, fire.pb.fushi.redpack.RedPackType _b212_, fire.pb.fushi.redpack.RedPackState _b213_, fire.pb.move.PickUpItem _b214_, fire.pb.RoleInfo _b215_, fire.pb.talk.FunModelType _b216_, fire.pb.FunOpenCloseType _b217_) {
		this.b1 = _b1_;
		this.b4 = _b4_;
		this.b8 = _b8_;
		this.b9 = _b9_;
		this.b10 = _b10_;
		this.b11 = _b11_;
		this.b14 = _b14_;
		this.b15 = _b15_;
		this.b16 = _b16_;
		this.b17 = _b17_;
		this.b18 = _b18_;
		this.b20 = _b20_;
		this.b21 = _b21_;
		this.b22 = _b22_;
		this.b23 = _b23_;
		this.b27 = _b27_;
		this.b28 = _b28_;
		this.b29 = _b29_;
		this.b31 = _b31_;
		this.b33 = _b33_;
		this.b43 = _b43_;
		this.b49 = _b49_;
		this.b54 = _b54_;
		this.b55 = _b55_;
		this.b56 = _b56_;
		this.b58 = _b58_;
		this.b61 = _b61_;
		this.b63 = _b63_;
		this.b64 = _b64_;
		this.b65 = _b65_;
		this.b66 = _b66_;
		this.b72 = _b72_;
		this.b73 = _b73_;
		this.b74 = _b74_;
		this.b75 = _b75_;
		this.b76 = _b76_;
		this.b87 = _b87_;
		this.b89 = _b89_;
		this.b90 = _b90_;
		this.b91 = _b91_;
		this.b92 = _b92_;
		this.b97 = _b97_;
		this.b101 = _b101_;
		this.b102 = _b102_;
		this.b105 = _b105_;
		this.b106 = _b106_;
		this.b109 = _b109_;
		this.b111 = _b111_;
		this.b112 = _b112_;
		this.b113 = _b113_;
		this.b114 = _b114_;
		this.b116 = _b116_;
		this.b120 = _b120_;
		this.b135 = _b135_;
		this.b148 = _b148_;
		this.b151 = _b151_;
		this.b152 = _b152_;
		this.b156 = _b156_;
		this.b168 = _b168_;
		this.b169 = _b169_;
		this.b170 = _b170_;
		this.b174 = _b174_;
		this.b176 = _b176_;
		this.b181 = _b181_;
		this.b191 = _b191_;
		this.b192 = _b192_;
		this.b193 = _b193_;
		this.b194 = _b194_;
		this.b195 = _b195_;
		this.b196 = _b196_;
		this.b197 = _b197_;
		this.b198 = _b198_;
		this.b199 = _b199_;
		this.b200 = _b200_;
		this.b201 = _b201_;
		this.b202 = _b202_;
		this.b203 = _b203_;
		this.b204 = _b204_;
		this.b205 = _b205_;
		this.b206 = _b206_;
		this.b207 = _b207_;
		this.b208 = _b208_;
		this.b209 = _b209_;
		this.b210 = _b210_;
		this.b211 = _b211_;
		this.b212 = _b212_;
		this.b213 = _b213_;
		this.b214 = _b214_;
		this.b215 = _b215_;
		this.b216 = _b216_;
		this.b217 = _b217_;
	}

	public final boolean _validator_() {
		if (!b1._validator_()) return false;
		if (!b4._validator_()) return false;
		if (!b8._validator_()) return false;
		if (!b9._validator_()) return false;
		if (!b10._validator_()) return false;
		if (!b11._validator_()) return false;
		if (!b14._validator_()) return false;
		if (!b15._validator_()) return false;
		if (!b16._validator_()) return false;
		if (!b17._validator_()) return false;
		if (!b18._validator_()) return false;
		if (!b20._validator_()) return false;
		if (!b21._validator_()) return false;
		if (!b22._validator_()) return false;
		if (!b23._validator_()) return false;
		if (!b27._validator_()) return false;
		if (!b28._validator_()) return false;
		if (!b29._validator_()) return false;
		if (!b31._validator_()) return false;
		if (!b33._validator_()) return false;
		if (!b43._validator_()) return false;
		if (!b49._validator_()) return false;
		if (!b54._validator_()) return false;
		if (!b55._validator_()) return false;
		if (!b56._validator_()) return false;
		if (!b58._validator_()) return false;
		if (!b61._validator_()) return false;
		if (!b63._validator_()) return false;
		if (!b64._validator_()) return false;
		if (!b65._validator_()) return false;
		if (!b66._validator_()) return false;
		if (!b72._validator_()) return false;
		if (!b73._validator_()) return false;
		if (!b74._validator_()) return false;
		if (!b75._validator_()) return false;
		if (!b76._validator_()) return false;
		if (!b87._validator_()) return false;
		if (!b89._validator_()) return false;
		if (!b90._validator_()) return false;
		if (!b91._validator_()) return false;
		if (!b92._validator_()) return false;
		if (!b97._validator_()) return false;
		if (!b101._validator_()) return false;
		if (!b102._validator_()) return false;
		if (!b105._validator_()) return false;
		if (!b106._validator_()) return false;
		if (!b109._validator_()) return false;
		if (!b111._validator_()) return false;
		if (!b112._validator_()) return false;
		if (!b113._validator_()) return false;
		if (!b114._validator_()) return false;
		if (!b116._validator_()) return false;
		if (!b120._validator_()) return false;
		if (!b135._validator_()) return false;
		if (!b148._validator_()) return false;
		if (!b151._validator_()) return false;
		if (!b152._validator_()) return false;
		if (!b156._validator_()) return false;
		if (!b168._validator_()) return false;
		if (!b169._validator_()) return false;
		if (!b170._validator_()) return false;
		if (!b174._validator_()) return false;
		if (!b176._validator_()) return false;
		if (!b181._validator_()) return false;
		if (!b191._validator_()) return false;
		if (!b192._validator_()) return false;
		if (!b193._validator_()) return false;
		if (!b194._validator_()) return false;
		if (!b195._validator_()) return false;
		if (!b196._validator_()) return false;
		if (!b197._validator_()) return false;
		if (!b198._validator_()) return false;
		if (!b199._validator_()) return false;
		if (!b200._validator_()) return false;
		if (!b201._validator_()) return false;
		if (!b202._validator_()) return false;
		if (!b203._validator_()) return false;
		if (!b204._validator_()) return false;
		if (!b205._validator_()) return false;
		if (!b206._validator_()) return false;
		if (!b207._validator_()) return false;
		if (!b208._validator_()) return false;
		if (!b209._validator_()) return false;
		if (!b210._validator_()) return false;
		if (!b211._validator_()) return false;
		if (!b212._validator_()) return false;
		if (!b213._validator_()) return false;
		if (!b214._validator_()) return false;
		if (!b215._validator_()) return false;
		if (!b216._validator_()) return false;
		if (!b217._validator_()) return false;
		return true;
	}

	public OctetsStream marshal(OctetsStream _os_) {
		if (!_validator_()) {
			throw new VerifyError("validator failed");
		}
		_os_.marshal(b1);
		_os_.marshal(b4);
		_os_.marshal(b8);
		_os_.marshal(b9);
		_os_.marshal(b10);
		_os_.marshal(b11);
		_os_.marshal(b14);
		_os_.marshal(b15);
		_os_.marshal(b16);
		_os_.marshal(b17);
		_os_.marshal(b18);
		_os_.marshal(b20);
		_os_.marshal(b21);
		_os_.marshal(b22);
		_os_.marshal(b23);
		_os_.marshal(b27);
		_os_.marshal(b28);
		_os_.marshal(b29);
		_os_.marshal(b31);
		_os_.marshal(b33);
		_os_.marshal(b43);
		_os_.marshal(b49);
		_os_.marshal(b54);
		_os_.marshal(b55);
		_os_.marshal(b56);
		_os_.marshal(b58);
		_os_.marshal(b61);
		_os_.marshal(b63);
		_os_.marshal(b64);
		_os_.marshal(b65);
		_os_.marshal(b66);
		_os_.marshal(b72);
		_os_.marshal(b73);
		_os_.marshal(b74);
		_os_.marshal(b75);
		_os_.marshal(b76);
		_os_.marshal(b87);
		_os_.marshal(b89);
		_os_.marshal(b90);
		_os_.marshal(b91);
		_os_.marshal(b92);
		_os_.marshal(b97);
		_os_.marshal(b101);
		_os_.marshal(b102);
		_os_.marshal(b105);
		_os_.marshal(b106);
		_os_.marshal(b109);
		_os_.marshal(b111);
		_os_.marshal(b112);
		_os_.marshal(b113);
		_os_.marshal(b114);
		_os_.marshal(b116);
		_os_.marshal(b120);
		_os_.marshal(b135);
		_os_.marshal(b148);
		_os_.marshal(b151);
		_os_.marshal(b152);
		_os_.marshal(b156);
		_os_.marshal(b168);
		_os_.marshal(b169);
		_os_.marshal(b170);
		_os_.marshal(b174);
		_os_.marshal(b176);
		_os_.marshal(b181);
		_os_.marshal(b191);
		_os_.marshal(b192);
		_os_.marshal(b193);
		_os_.marshal(b194);
		_os_.marshal(b195);
		_os_.marshal(b196);
		_os_.marshal(b197);
		_os_.marshal(b198);
		_os_.marshal(b199);
		_os_.marshal(b200);
		_os_.marshal(b201);
		_os_.marshal(b202);
		_os_.marshal(b203);
		_os_.marshal(b204);
		_os_.marshal(b205);
		_os_.marshal(b206);
		_os_.marshal(b207);
		_os_.marshal(b208);
		_os_.marshal(b209);
		_os_.marshal(b210);
		_os_.marshal(b211);
		_os_.marshal(b212);
		_os_.marshal(b213);
		_os_.marshal(b214);
		_os_.marshal(b215);
		_os_.marshal(b216);
		_os_.marshal(b217);
		return _os_;
	}

	public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
		b1.unmarshal(_os_);
		b4.unmarshal(_os_);
		b8.unmarshal(_os_);
		b9.unmarshal(_os_);
		b10.unmarshal(_os_);
		b11.unmarshal(_os_);
		b14.unmarshal(_os_);
		b15.unmarshal(_os_);
		b16.unmarshal(_os_);
		b17.unmarshal(_os_);
		b18.unmarshal(_os_);
		b20.unmarshal(_os_);
		b21.unmarshal(_os_);
		b22.unmarshal(_os_);
		b23.unmarshal(_os_);
		b27.unmarshal(_os_);
		b28.unmarshal(_os_);
		b29.unmarshal(_os_);
		b31.unmarshal(_os_);
		b33.unmarshal(_os_);
		b43.unmarshal(_os_);
		b49.unmarshal(_os_);
		b54.unmarshal(_os_);
		b55.unmarshal(_os_);
		b56.unmarshal(_os_);
		b58.unmarshal(_os_);
		b61.unmarshal(_os_);
		b63.unmarshal(_os_);
		b64.unmarshal(_os_);
		b65.unmarshal(_os_);
		b66.unmarshal(_os_);
		b72.unmarshal(_os_);
		b73.unmarshal(_os_);
		b74.unmarshal(_os_);
		b75.unmarshal(_os_);
		b76.unmarshal(_os_);
		b87.unmarshal(_os_);
		b89.unmarshal(_os_);
		b90.unmarshal(_os_);
		b91.unmarshal(_os_);
		b92.unmarshal(_os_);
		b97.unmarshal(_os_);
		b101.unmarshal(_os_);
		b102.unmarshal(_os_);
		b105.unmarshal(_os_);
		b106.unmarshal(_os_);
		b109.unmarshal(_os_);
		b111.unmarshal(_os_);
		b112.unmarshal(_os_);
		b113.unmarshal(_os_);
		b114.unmarshal(_os_);
		b116.unmarshal(_os_);
		b120.unmarshal(_os_);
		b135.unmarshal(_os_);
		b148.unmarshal(_os_);
		b151.unmarshal(_os_);
		b152.unmarshal(_os_);
		b156.unmarshal(_os_);
		b168.unmarshal(_os_);
		b169.unmarshal(_os_);
		b170.unmarshal(_os_);
		b174.unmarshal(_os_);
		b176.unmarshal(_os_);
		b181.unmarshal(_os_);
		b191.unmarshal(_os_);
		b192.unmarshal(_os_);
		b193.unmarshal(_os_);
		b194.unmarshal(_os_);
		b195.unmarshal(_os_);
		b196.unmarshal(_os_);
		b197.unmarshal(_os_);
		b198.unmarshal(_os_);
		b199.unmarshal(_os_);
		b200.unmarshal(_os_);
		b201.unmarshal(_os_);
		b202.unmarshal(_os_);
		b203.unmarshal(_os_);
		b204.unmarshal(_os_);
		b205.unmarshal(_os_);
		b206.unmarshal(_os_);
		b207.unmarshal(_os_);
		b208.unmarshal(_os_);
		b209.unmarshal(_os_);
		b210.unmarshal(_os_);
		b211.unmarshal(_os_);
		b212.unmarshal(_os_);
		b213.unmarshal(_os_);
		b214.unmarshal(_os_);
		b215.unmarshal(_os_);
		b216.unmarshal(_os_);
		b217.unmarshal(_os_);
		if (!_validator_()) {
			throw new VerifyError("validator failed");
		}
		return _os_;
	}

	public boolean equals(Object _o1_) {
		if (_o1_ == this) return true;
		if (_o1_ instanceof BeanImport) {
			BeanImport _o_ = (BeanImport)_o1_;
			if (!b1.equals(_o_.b1)) return false;
			if (!b4.equals(_o_.b4)) return false;
			if (!b8.equals(_o_.b8)) return false;
			if (!b9.equals(_o_.b9)) return false;
			if (!b10.equals(_o_.b10)) return false;
			if (!b11.equals(_o_.b11)) return false;
			if (!b14.equals(_o_.b14)) return false;
			if (!b15.equals(_o_.b15)) return false;
			if (!b16.equals(_o_.b16)) return false;
			if (!b17.equals(_o_.b17)) return false;
			if (!b18.equals(_o_.b18)) return false;
			if (!b20.equals(_o_.b20)) return false;
			if (!b21.equals(_o_.b21)) return false;
			if (!b22.equals(_o_.b22)) return false;
			if (!b23.equals(_o_.b23)) return false;
			if (!b27.equals(_o_.b27)) return false;
			if (!b28.equals(_o_.b28)) return false;
			if (!b29.equals(_o_.b29)) return false;
			if (!b31.equals(_o_.b31)) return false;
			if (!b33.equals(_o_.b33)) return false;
			if (!b43.equals(_o_.b43)) return false;
			if (!b49.equals(_o_.b49)) return false;
			if (!b54.equals(_o_.b54)) return false;
			if (!b55.equals(_o_.b55)) return false;
			if (!b56.equals(_o_.b56)) return false;
			if (!b58.equals(_o_.b58)) return false;
			if (!b61.equals(_o_.b61)) return false;
			if (!b63.equals(_o_.b63)) return false;
			if (!b64.equals(_o_.b64)) return false;
			if (!b65.equals(_o_.b65)) return false;
			if (!b66.equals(_o_.b66)) return false;
			if (!b72.equals(_o_.b72)) return false;
			if (!b73.equals(_o_.b73)) return false;
			if (!b74.equals(_o_.b74)) return false;
			if (!b75.equals(_o_.b75)) return false;
			if (!b76.equals(_o_.b76)) return false;
			if (!b87.equals(_o_.b87)) return false;
			if (!b89.equals(_o_.b89)) return false;
			if (!b90.equals(_o_.b90)) return false;
			if (!b91.equals(_o_.b91)) return false;
			if (!b92.equals(_o_.b92)) return false;
			if (!b97.equals(_o_.b97)) return false;
			if (!b101.equals(_o_.b101)) return false;
			if (!b102.equals(_o_.b102)) return false;
			if (!b105.equals(_o_.b105)) return false;
			if (!b106.equals(_o_.b106)) return false;
			if (!b109.equals(_o_.b109)) return false;
			if (!b111.equals(_o_.b111)) return false;
			if (!b112.equals(_o_.b112)) return false;
			if (!b113.equals(_o_.b113)) return false;
			if (!b114.equals(_o_.b114)) return false;
			if (!b116.equals(_o_.b116)) return false;
			if (!b120.equals(_o_.b120)) return false;
			if (!b135.equals(_o_.b135)) return false;
			if (!b148.equals(_o_.b148)) return false;
			if (!b151.equals(_o_.b151)) return false;
			if (!b152.equals(_o_.b152)) return false;
			if (!b156.equals(_o_.b156)) return false;
			if (!b168.equals(_o_.b168)) return false;
			if (!b169.equals(_o_.b169)) return false;
			if (!b170.equals(_o_.b170)) return false;
			if (!b174.equals(_o_.b174)) return false;
			if (!b176.equals(_o_.b176)) return false;
			if (!b181.equals(_o_.b181)) return false;
			if (!b191.equals(_o_.b191)) return false;
			if (!b192.equals(_o_.b192)) return false;
			if (!b193.equals(_o_.b193)) return false;
			if (!b194.equals(_o_.b194)) return false;
			if (!b195.equals(_o_.b195)) return false;
			if (!b196.equals(_o_.b196)) return false;
			if (!b197.equals(_o_.b197)) return false;
			if (!b198.equals(_o_.b198)) return false;
			if (!b199.equals(_o_.b199)) return false;
			if (!b200.equals(_o_.b200)) return false;
			if (!b201.equals(_o_.b201)) return false;
			if (!b202.equals(_o_.b202)) return false;
			if (!b203.equals(_o_.b203)) return false;
			if (!b204.equals(_o_.b204)) return false;
			if (!b205.equals(_o_.b205)) return false;
			if (!b206.equals(_o_.b206)) return false;
			if (!b207.equals(_o_.b207)) return false;
			if (!b208.equals(_o_.b208)) return false;
			if (!b209.equals(_o_.b209)) return false;
			if (!b210.equals(_o_.b210)) return false;
			if (!b211.equals(_o_.b211)) return false;
			if (!b212.equals(_o_.b212)) return false;
			if (!b213.equals(_o_.b213)) return false;
			if (!b214.equals(_o_.b214)) return false;
			if (!b215.equals(_o_.b215)) return false;
			if (!b216.equals(_o_.b216)) return false;
			if (!b217.equals(_o_.b217)) return false;
			return true;
		}
		return false;
	}

	public int hashCode() {
		int _h_ = 0;
		_h_ += b1.hashCode();
		_h_ += b4.hashCode();
		_h_ += b8.hashCode();
		_h_ += b9.hashCode();
		_h_ += b10.hashCode();
		_h_ += b11.hashCode();
		_h_ += b14.hashCode();
		_h_ += b15.hashCode();
		_h_ += b16.hashCode();
		_h_ += b17.hashCode();
		_h_ += b18.hashCode();
		_h_ += b20.hashCode();
		_h_ += b21.hashCode();
		_h_ += b22.hashCode();
		_h_ += b23.hashCode();
		_h_ += b27.hashCode();
		_h_ += b28.hashCode();
		_h_ += b29.hashCode();
		_h_ += b31.hashCode();
		_h_ += b33.hashCode();
		_h_ += b43.hashCode();
		_h_ += b49.hashCode();
		_h_ += b54.hashCode();
		_h_ += b55.hashCode();
		_h_ += b56.hashCode();
		_h_ += b58.hashCode();
		_h_ += b61.hashCode();
		_h_ += b63.hashCode();
		_h_ += b64.hashCode();
		_h_ += b65.hashCode();
		_h_ += b66.hashCode();
		_h_ += b72.hashCode();
		_h_ += b73.hashCode();
		_h_ += b74.hashCode();
		_h_ += b75.hashCode();
		_h_ += b76.hashCode();
		_h_ += b87.hashCode();
		_h_ += b89.hashCode();
		_h_ += b90.hashCode();
		_h_ += b91.hashCode();
		_h_ += b92.hashCode();
		_h_ += b97.hashCode();
		_h_ += b101.hashCode();
		_h_ += b102.hashCode();
		_h_ += b105.hashCode();
		_h_ += b106.hashCode();
		_h_ += b109.hashCode();
		_h_ += b111.hashCode();
		_h_ += b112.hashCode();
		_h_ += b113.hashCode();
		_h_ += b114.hashCode();
		_h_ += b116.hashCode();
		_h_ += b120.hashCode();
		_h_ += b135.hashCode();
		_h_ += b148.hashCode();
		_h_ += b151.hashCode();
		_h_ += b152.hashCode();
		_h_ += b156.hashCode();
		_h_ += b168.hashCode();
		_h_ += b169.hashCode();
		_h_ += b170.hashCode();
		_h_ += b174.hashCode();
		_h_ += b176.hashCode();
		_h_ += b181.hashCode();
		_h_ += b191.hashCode();
		_h_ += b192.hashCode();
		_h_ += b193.hashCode();
		_h_ += b194.hashCode();
		_h_ += b195.hashCode();
		_h_ += b196.hashCode();
		_h_ += b197.hashCode();
		_h_ += b198.hashCode();
		_h_ += b199.hashCode();
		_h_ += b200.hashCode();
		_h_ += b201.hashCode();
		_h_ += b202.hashCode();
		_h_ += b203.hashCode();
		_h_ += b204.hashCode();
		_h_ += b205.hashCode();
		_h_ += b206.hashCode();
		_h_ += b207.hashCode();
		_h_ += b208.hashCode();
		_h_ += b209.hashCode();
		_h_ += b210.hashCode();
		_h_ += b211.hashCode();
		_h_ += b212.hashCode();
		_h_ += b213.hashCode();
		_h_ += b214.hashCode();
		_h_ += b215.hashCode();
		_h_ += b216.hashCode();
		_h_ += b217.hashCode();
		return _h_;
	}

	public String toString() {
		StringBuilder _sb_ = new StringBuilder();
		_sb_.append("(");
		_sb_.append(b1).append(",");
		_sb_.append(b4).append(",");
		_sb_.append(b8).append(",");
		_sb_.append(b9).append(",");
		_sb_.append(b10).append(",");
		_sb_.append(b11).append(",");
		_sb_.append(b14).append(",");
		_sb_.append(b15).append(",");
		_sb_.append(b16).append(",");
		_sb_.append(b17).append(",");
		_sb_.append(b18).append(",");
		_sb_.append(b20).append(",");
		_sb_.append(b21).append(",");
		_sb_.append(b22).append(",");
		_sb_.append(b23).append(",");
		_sb_.append(b27).append(",");
		_sb_.append(b28).append(",");
		_sb_.append(b29).append(",");
		_sb_.append(b31).append(",");
		_sb_.append(b33).append(",");
		_sb_.append(b43).append(",");
		_sb_.append(b49).append(",");
		_sb_.append(b54).append(",");
		_sb_.append(b55).append(",");
		_sb_.append(b56).append(",");
		_sb_.append(b58).append(",");
		_sb_.append(b61).append(",");
		_sb_.append(b63).append(",");
		_sb_.append(b64).append(",");
		_sb_.append(b65).append(",");
		_sb_.append(b66).append(",");
		_sb_.append(b72).append(",");
		_sb_.append(b73).append(",");
		_sb_.append(b74).append(",");
		_sb_.append(b75).append(",");
		_sb_.append(b76).append(",");
		_sb_.append(b87).append(",");
		_sb_.append(b89).append(",");
		_sb_.append(b90).append(",");
		_sb_.append(b91).append(",");
		_sb_.append(b92).append(",");
		_sb_.append(b97).append(",");
		_sb_.append(b101).append(",");
		_sb_.append(b102).append(",");
		_sb_.append(b105).append(",");
		_sb_.append(b106).append(",");
		_sb_.append(b109).append(",");
		_sb_.append(b111).append(",");
		_sb_.append(b112).append(",");
		_sb_.append(b113).append(",");
		_sb_.append(b114).append(",");
		_sb_.append(b116).append(",");
		_sb_.append(b120).append(",");
		_sb_.append(b135).append(",");
		_sb_.append(b148).append(",");
		_sb_.append(b151).append(",");
		_sb_.append(b152).append(",");
		_sb_.append(b156).append(",");
		_sb_.append(b168).append(",");
		_sb_.append(b169).append(",");
		_sb_.append(b170).append(",");
		_sb_.append(b174).append(",");
		_sb_.append(b176).append(",");
		_sb_.append(b181).append(",");
		_sb_.append(b191).append(",");
		_sb_.append(b192).append(",");
		_sb_.append(b193).append(",");
		_sb_.append(b194).append(",");
		_sb_.append(b195).append(",");
		_sb_.append(b196).append(",");
		_sb_.append(b197).append(",");
		_sb_.append(b198).append(",");
		_sb_.append(b199).append(",");
		_sb_.append(b200).append(",");
		_sb_.append(b201).append(",");
		_sb_.append(b202).append(",");
		_sb_.append(b203).append(",");
		_sb_.append(b204).append(",");
		_sb_.append(b205).append(",");
		_sb_.append(b206).append(",");
		_sb_.append(b207).append(",");
		_sb_.append(b208).append(",");
		_sb_.append(b209).append(",");
		_sb_.append(b210).append(",");
		_sb_.append(b211).append(",");
		_sb_.append(b212).append(",");
		_sb_.append(b213).append(",");
		_sb_.append(b214).append(",");
		_sb_.append(b215).append(",");
		_sb_.append(b216).append(",");
		_sb_.append(b217).append(",");
		_sb_.append(")");
		return _sb_.toString();
	}

	// DO NOT EDIT THIS }}}
	// RPCGEN_DEFINE_END }}}

}

