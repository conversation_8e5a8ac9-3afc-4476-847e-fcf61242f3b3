
package fire.pb;

// {{{ RPCGEN_IMPORT_BEGIN
// {{{ DO NOT EDIT THIS
import com.locojoy.base.Marshal.OctetsStream;
import com.locojoy.base.Marshal.MarshalException;

abstract class __CReqRoleProp__ extends mkio.Protocol { }

/** 客户端请求其他玩家的某个属性信息
*/
// DO NOT EDIT THIS }}}
// RPCGEN_IMPORT_END }}}

public class CReqRoleProp extends __CReqRoleProp__ {
	@Override
	protected void process() {
		// protocol handle
		final long reqroleid = gnet.link.Onlines.getInstance().findRoleid(this);
		if (reqroleid <= 0)
			return;
		
		if (!fire.pb.StateCommon.isOnline(roleid))
			return;
		
		final fire.pb.SRetRoleProp send = new fire.pb.SRetRoleProp();
		send.roleid = roleid;
		if (proptype == fire.pb.attr.AttrType.LEVEL){
			final fire.pb.PropRole prole = new fire.pb.PropRole(roleid, true);
			send.datas.put(fire.pb.attr.AttrType.LEVEL, (float)prole.getLevel());
			gnet.link.Onlines.getInstance().send(reqroleid, send);
			return;		
		}
		
	}

	// {{{ RPCGEN_DEFINE_BEGIN
	// {{{ DO NOT EDIT THIS
	public static final int PROTOCOL_TYPE = 786453;

	public int getType() {
		return 786453;
	}

	public long roleid;
	public int proptype;

	public CReqRoleProp() {
	}

	public CReqRoleProp(long _roleid_, int _proptype_) {
		this.roleid = _roleid_;
		this.proptype = _proptype_;
	}

	public final boolean _validator_() {
		if (roleid <= 0) return false;
		if (proptype <= 0) return false;
		return true;
	}

	public OctetsStream marshal(OctetsStream _os_) {
		if (!_validator_()) {
			throw new VerifyError("validator failed");
		}
		_os_.marshal(roleid);
		_os_.marshal(proptype);
		return _os_;
	}

	public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
		roleid = _os_.unmarshal_long();
		proptype = _os_.unmarshal_int();
		if (!_validator_()) {
			throw new VerifyError("validator failed");
		}
		return _os_;
	}

	public boolean equals(Object _o1_) {
		if (_o1_ == this) return true;
		if (_o1_ instanceof CReqRoleProp) {
			CReqRoleProp _o_ = (CReqRoleProp)_o1_;
			if (roleid != _o_.roleid) return false;
			if (proptype != _o_.proptype) return false;
			return true;
		}
		return false;
	}

	public int hashCode() {
		int _h_ = 0;
		_h_ += (int)roleid;
		_h_ += proptype;
		return _h_;
	}

	public String toString() {
		StringBuilder _sb_ = new StringBuilder();
		_sb_.append("(");
		_sb_.append(roleid).append(",");
		_sb_.append(proptype).append(",");
		_sb_.append(")");
		return _sb_.toString();
	}

	public int compareTo(CReqRoleProp _o_) {
		if (_o_ == this) return 0;
		int _c_ = 0;
		_c_ = Long.signum(roleid - _o_.roleid);
		if (0 != _c_) return _c_;
		_c_ = proptype - _o_.proptype;
		if (0 != _c_) return _c_;
		return _c_;
	}

	// DO NOT EDIT THIS }}}
	// RPCGEN_DEFINE_END }}}

}

