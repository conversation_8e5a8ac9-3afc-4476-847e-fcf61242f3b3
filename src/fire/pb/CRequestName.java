
package fire.pb;

// {{{ RPCGEN_IMPORT_BEGIN
// {{{ DO NOT EDIT THIS
import com.locojoy.base.Marshal.OctetsStream;
import com.locojoy.base.Marshal.MarshalException;

abstract class __CRequestName__ extends mkio.Protocol { }

// DO NOT EDIT THIS }}}
// RPCGEN_IMPORT_END }}}

public class CRequestName extends __CRequestName__ {
	@Override
	protected void process() {
		final int userid=((gnet.link.Dispatch)CRequestName.this.getContext()).userid;
		if(userid>0){
			new PRequestNameProc(userid,sex,this).submit();		
		}
	}

	// {{{ RPCGEN_DEFINE_BEGIN
	// {{{ DO NOT EDIT THIS
	public static final int PROTOCOL_TYPE = 786474;

	public int getType() {
		return 786474;
	}

	public short sex;

	public CRequestName() {
	}

	public CRequestName(short _sex_) {
		this.sex = _sex_;
	}

	public final boolean _validator_() {
		return true;
	}

	public OctetsStream marshal(OctetsStream _os_) {
		if (!_validator_()) {
			throw new VerifyError("validator failed");
		}
		_os_.marshal(sex);
		return _os_;
	}

	public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
		sex = _os_.unmarshal_short();
		if (!_validator_()) {
			throw new VerifyError("validator failed");
		}
		return _os_;
	}

	public boolean equals(Object _o1_) {
		if (_o1_ == this) return true;
		if (_o1_ instanceof CRequestName) {
			CRequestName _o_ = (CRequestName)_o1_;
			if (sex != _o_.sex) return false;
			return true;
		}
		return false;
	}

	public int hashCode() {
		int _h_ = 0;
		_h_ += sex;
		return _h_;
	}

	public String toString() {
		StringBuilder _sb_ = new StringBuilder();
		_sb_.append("(");
		_sb_.append(sex).append(",");
		_sb_.append(")");
		return _sb_.toString();
	}

	public int compareTo(CRequestName _o_) {
		if (_o_ == this) return 0;
		int _c_ = 0;
		_c_ = sex - _o_.sex;
		if (0 != _c_) return _c_;
		return _c_;
	}

	// DO NOT EDIT THIS }}}
	// RPCGEN_DEFINE_END }}}

}

