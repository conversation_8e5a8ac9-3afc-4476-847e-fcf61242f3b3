
package fire.pb.instancezone.bingfeng;


import fire.pb.ranklist.SRequestRankList;
import fire.pb.ranklist.proc.RankListManager;

// {{{ RPCGEN_IMPORT_BEGIN
// {{{ DO NOT EDIT THIS
import com.locojoy.base.Marshal.OctetsStream;
import com.locojoy.base.Marshal.MarshalException;

abstract class __CReqBingFengRank__ extends mkio.Protocol { }

// DO NOT EDIT THIS }}}
// RPCGEN_IMPORT_END }}}

public class CReqBingFengRank extends __CReqBingFengRank__ {
	
	@Override
	protected void process() {
		// protocol handle
		final long srcRoleid = gnet.link.Onlines.getInstance().findRoleid(this);
		if (srcRoleid < 0)
			return;
		int rankid = BingFengLandMgr.getInstance().getRankIdByInstzoneId(
				landid);
		SRequestRankList requestRankList = RankListManager.getInstance()
				.getRankListResponse(rankid, srcRoleid, -1);
		gnet.link.Onlines.getInstance().send(srcRoleid, requestRankList);
		fire.pb.instancezone.bingfeng.BingFengLandMgr.sendSBingFengInfo(
				srcRoleid, rankid);
	}

	// {{{ RPCGEN_DEFINE_BEGIN
	// {{{ DO NOT EDIT THIS
	public static final int PROTOCOL_TYPE = 804556;

	public int getType() {
		return 804556;
	}

	public long npckey;
	public int landid; // landId,根据配置表里的数据,比如40级冰封王座是1,50级冰封王座是2

	public CReqBingFengRank() {
	}

	public CReqBingFengRank(long _npckey_, int _landid_) {
		this.npckey = _npckey_;
		this.landid = _landid_;
	}

	public final boolean _validator_() {
		return true;
	}

	public OctetsStream marshal(OctetsStream _os_) {
		if (!_validator_()) {
			throw new VerifyError("validator failed");
		}
		_os_.marshal(npckey);
		_os_.marshal(landid);
		return _os_;
	}

	public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
		npckey = _os_.unmarshal_long();
		landid = _os_.unmarshal_int();
		if (!_validator_()) {
			throw new VerifyError("validator failed");
		}
		return _os_;
	}

	public boolean equals(Object _o1_) {
		if (_o1_ == this) return true;
		if (_o1_ instanceof CReqBingFengRank) {
			CReqBingFengRank _o_ = (CReqBingFengRank)_o1_;
			if (npckey != _o_.npckey) return false;
			if (landid != _o_.landid) return false;
			return true;
		}
		return false;
	}

	public int hashCode() {
		int _h_ = 0;
		_h_ += (int)npckey;
		_h_ += landid;
		return _h_;
	}

	public String toString() {
		StringBuilder _sb_ = new StringBuilder();
		_sb_.append("(");
		_sb_.append(npckey).append(",");
		_sb_.append(landid).append(",");
		_sb_.append(")");
		return _sb_.toString();
	}

	public int compareTo(CReqBingFengRank _o_) {
		if (_o_ == this) return 0;
		int _c_ = 0;
		_c_ = Long.signum(npckey - _o_.npckey);
		if (0 != _c_) return _c_;
		_c_ = landid - _o_.landid;
		if (0 != _c_) return _c_;
		return _c_;
	}

	// DO NOT EDIT THIS }}}
	// RPCGEN_DEFINE_END }}}

}

