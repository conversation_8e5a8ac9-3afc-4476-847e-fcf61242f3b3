
package fire.pb.mission;

import java.util.Map;

import fire.pb.PropRole;
import fire.pb.mission.InstanceTimes;
import fire.pb.mission.instance.InstanceManager;
import fire.pb.util.DateValidate;
import gnet.link.Onlines;








// {{{ RPCGEN_IMPORT_BEGIN
// {{{ DO NOT EDIT THIS
import com.locojoy.base.Marshal.OctetsStream;
import com.locojoy.base.Marshal.MarshalException;

abstract class __CAskLandTimes__ extends mkio.Protocol { }

// DO NOT EDIT THIS }}}
// RPCGEN_IMPORT_END }}}

public class CAskLandTimes extends __CAskLandTimes__ {
	@Override
	protected void process() {
		final long roleid = gnet.link.Onlines.getInstance().findRoleid(this);
		if (roleid >0) {
			int allTimes = 0;
			int finishTimes = 0;
			PropRole pRole = new PropRole(roleid, true);
			SLandTimes send = new SLandTimes();
			
			if (pRole.getLevel() >= 30) {
				allTimes = InstanceManager.getInstance().getDayMaxTimes(roleid);
				xbean.InstanceInfoCol taskInfo = xtable.Roleinstancetask.select(roleid);
				for (Map.Entry<Integer, xbean.InstanceTaskInfo> infos : taskInfo.getInstinfo().entrySet()) {
					if (taskInfo != null && DateValidate.inTheSameDay(infos.getValue().getAccepttime(), System.currentTimeMillis())){
						finishTimes = infos.getValue().getCounts();
					}
					InstanceTimes instanceTime = new InstanceTimes(infos.getKey(), finishTimes, allTimes);
					
					send.instances.put(infos.getKey(), instanceTime);
				}
			}
			
			Onlines.getInstance().send(roleid, send);
		}
	}

	// {{{ RPCGEN_DEFINE_BEGIN
	// {{{ DO NOT EDIT THIS
	public static final int PROTOCOL_TYPE = 805469;

	public int getType() {
		return 805469;
	}


	public CAskLandTimes() {
	}

	public final boolean _validator_() {
		return true;
	}

	public OctetsStream marshal(OctetsStream _os_) {
		if (!_validator_()) {
			throw new VerifyError("validator failed");
		}
		return _os_;
	}

	public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
		if (!_validator_()) {
			throw new VerifyError("validator failed");
		}
		return _os_;
	}

	public boolean equals(Object _o1_) {
		if (_o1_ == this) return true;
		if (_o1_ instanceof CAskLandTimes) {
			return true;
		}
		return false;
	}

	public int hashCode() {
		int _h_ = 0;
		return _h_;
	}

	public String toString() {
		StringBuilder _sb_ = new StringBuilder();
		_sb_.append("(");
		_sb_.append(")");
		return _sb_.toString();
	}

	public int compareTo(CAskLandTimes _o_) {
		if (_o_ == this) return 0;
		int _c_ = 0;
		return _c_;
	}

	// DO NOT EDIT THIS }}}
	// RPCGEN_DEFINE_END }}}

}

