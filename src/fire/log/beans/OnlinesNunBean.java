package fire.log.beans;

public class OnlinesNunBean {

	private String tick;

	private int historyUserNum;
	
	private int increaseUserNum;
	
	private int historyRoleNum;

	private int increaseRoleNum;
	
	private int chargeUserNum;
	
	private int chargeTotal;
	
	private int consumeRoleNum;
	
	private int consumeTotal;

	public int getHistoryUserNum() {
		return historyUserNum;
	}

	public void setHistoryUserNum(int historyUserNum) {
		this.historyUserNum = historyUserNum;
	}

	public int getIncreaseUserNum() {
		return increaseUserNum;
	}

	public void setIncreaseUserNum(int increaseUserNum) {
		this.increaseUserNum = increaseUserNum;
	}

	public int getHistoryRoleNum() {
		return historyRoleNum;
	}

	public void setHistoryRoleNum(int historyRoleNum) {
		this.historyRoleNum = historyRoleNum;
	}

	public int getIncreaseRoleNum() {
		return increaseRoleNum;
	}

	public void setIncreaseRoleNum(int increaseRoleNum) {
		this.increaseRoleNum = increaseRoleNum;
	}

	public int getChargeUserNum() {
		return chargeUserNum;
	}

	public void setChargeUserNum(int chargeUserNum) {
		this.chargeUserNum = chargeUserNum;
	}

	public int getChargeTotal() {
		return chargeTotal;
	}

	public void setChargeTotal(int chargeTotal) {
		this.chargeTotal = chargeTotal;
	}

	public int getConsumeRoleNum() {
		return consumeRoleNum;
	}

	public void setConsumeRoleNum(int consumeRoleNum) {
		this.consumeRoleNum = consumeRoleNum;
	}

	public int getConsumeTotal() {
		return consumeTotal;
	}

	public void setConsumeTotal(int consumeTotal) {
		this.consumeTotal = consumeTotal;
	}
	
	public String getTick() {
		return tick;
	}

	public void setTick(String tick) {
		this.tick = tick;
	}
	
}
