package fire.log;
public interface RemoteLogParam{
	//日志的参数类型
	public final static String REG_TIME = "reg_time";
	public final static String SOURCE = "source";
	public final static String GAME_ID = "game_id";
	public final static String CARDTYPE = "cardtype";
	public final static String SOURCESERVER = "sourceserver";
	public final static String SOURCEPROC = "sourceproc";
	public final static String ACCOUNT = "account";
	public final static String USERID = "userid";
	public final static String PEER = "peer";
	public final static String MAC = "mac";
	public final static String TIME = "time";
	public final static String STATUS = "status";
	public final static String MAPID = "mapid";
	public final static String X = "x";
	public final static String Y = "y";
	public final static String FROM = "from";
	public final static String ROLEID = "roleid";
	public final static String SHAPEID = "shapeid";
	public final static String SCHOOL = "school";
	public final static String LEV = "lev";
	public final static String OS = "os";
	public final static String CAMP = "camp";
	public final static String MACADDRESS = "macaddress";
	public final static String CLIENTSOURCE = "clientsource";
	public final static String ACCOUNTSOURCE = "accountsource";
	public final static String TOTALONLINETIME = "totalonlinetime";
	public final static String CREATE_TIME = "create_time";
	public final static String GMUSERID = "gmuserid";
	public final static String FORBID_TIME = "forbid_time";
	public final static String REASON = "reason";
	public final static String TYPE = "type";
	public final static String BEFORELEV = "beforelev";
	public final static String EXP = "exp";
	public final static String MONEY = "money";
	public final static String SMONEY = "smoney";
	public final static String INBORNID = "inbornid";
	public final static String INBORNLEV = "inbornlev";
	public final static String MONEYNEED = "moneyneed";
	public final static String SMONEYNEED = "smoneyneed";
	public final static String SAVINGNEED = "savingneed";
	public final static String EXPCHANGED = "expchanged";
	public final static String SKILLFROM = "skillfrom";
	public final static String SKILLID = "skillid";
	public final static String SKILLLEV = "skilllev";
	public final static String FACTIONSWNEED = "factionswneed";
	public final static String RENQINEED = "renqineed";
	public final static String ITEMID = "itemid";
	public final static String ITEMNUM = "itemnum";
	public final static String UID = "uid";
	public final static String CASH_NEED1 = "cash_need1";
	public final static String CASH_NEED2 = "cash_need2";
	public final static String CASH_NEED3 = "cash_need3";
	public final static String ORDER_ID = "order_id";
	public final static String ITEM_ID = "item_id";
	public final static String ITEM_COUNT = "item_count";
	public final static String BUY_TYPE = "buy_type";
	public final static String CASH_NEED = "cash_need";
	public final static String CASH_LEFT = "cash_left";
	public final static String POINT = "point";
	public final static String LEVTYPE = "levtype";
	public final static String MADETYPE = "madetype";
	public final static String BANDTYPE = "bandtype";
	public final static String ITEMTYPE = "itemtype";
	public final static String ITEMSUBTYPE = "itemsubtype";
	public final static String ITEMCOST = "itemcost";
	public final static String GUID = "guid";
	public final static String CONSUMETYPE = "consumetype";
	public final static String ITEMS = "items";
	public final static String LOTTERYID = "lotteryid";
	public final static String TASKID = "taskid";
	public final static String ITEM = "item";
	public final static String RINGNUM = "ringnum";
	public final static String ROUNDNUM = "roundnum";
	public final static String TASKPARA = "taskpara";
	public final static String CASH_RMB = "cash_rmb";
	public final static String TOTALCASH = "totalcash";
	public final static String OLDSERIAL = "oldserial";
	public final static String NEWSERIAL = "newserial";
	public final static String CASH_ADD = "cash_add";
	public final static String DELTA = "delta";
	public final static String VIPLV = "viplv";
	public final static String IDFA = "idfa";
	public final static String CURRENCY = "currency";
	public final static String TID = "tid";
	public final static String TRADEFLAG = "tradeflag";
	public final static String ORDER_CASH = "order_cash";
	public final static String ORDER_MONEY = "order_money";
	public final static String ORDERFEE_MONEY = "orderfee_money";
	public final static String VOLUME = "volume";
	public final static String DEAL_CASH = "deal_cash";
	public final static String DEAL_MONEY = "deal_money";
	public final static String CASH_BUY = "cash_buy";
	public final static String CASH_SELL = "cash_sell";
	public final static String LOGTIME = "logtime";
	public final static String OLD = "old";
	public final static String NEW = "new";
	public final static String MONEYCHANGE = "moneychange";
	public final static String GRAPHID = "graphid";
	public final static String MONSTER = "monster";
	public final static String EQUIPID = "equipid";
	public final static String EQUIPUNIQID = "equipuniqid";
	public final static String EQUIPPREFIX = "equipprefix";
	public final static String EQUIPDURMAX = "equipdurmax";
	public final static String EQUIPBASE = "equipbase";
	public final static String EQUIPSPECIAL = "equipspecial";
	public final static String EQUIPSTARLEV = "equipstarlev";
	public final static String EQUIPCOLOUR = "equipcolour";
	public final static String EQUIPGEM = "equipgem";
	public final static String EQUIPSKILL = "equipskill";
	public final static String EQUIPMAKER = "equipmaker";
	public final static String EQUIPPOS = "equippos";
	public final static String RESULT = "result";
	public final static String SKILL = "skill";
	public final static String FACTIONID = "factionid";
	public final static String EXPCHANGE = "expchange";
	public final static String SFROM = "sfrom";
	public final static String SUSERID = "suserid";
	public final static String SROLEID = "sroleid";
	public final static String SSHAPEID = "sshapeid";
	public final static String SSCHOOL = "sschool";
	public final static String SLEV = "slev";
	public final static String DFROM = "dfrom";
	public final static String DUSERID = "duserid";
	public final static String DROLEID = "droleid";
	public final static String DSHAPEID = "dshapeid";
	public final static String DSCHOOL = "dschool";
	public final static String DLEV = "dlev";
	public final static String SRCMONEY = "srcmoney";
	public final static String DSTMONEY = "dstmoney";
	public final static String SRCITEM = "srcitem";
	public final static String DSTITEM = "dstitem";
	public final static String AREATYPE = "areatype";
	public final static String AREAID = "areaid";
	public final static String MONEYBEFORETRADE = "moneybeforetrade";
	public final static String NPCID = "npcid";
	public final static String SMONEYBEFORETRADE = "smoneybeforetrade";
	public final static String TEAMID = "teamid";
	public final static String TEAMROLELIST = "teamrolelist";
	public final static String TEAMOUTROLELIST = "teamoutrolelist";
	public final static String LEADERID = "leaderid";
	public final static String ZHENFA = "zhenfa";
	public final static String BATTLEID = "battleid";
	public final static String MONSTERIDLIST = "monsteridlist";
	public final static String MONSTERNUMBER = "monsternumber";
	public final static String MONSTERLEV = "monsterlev";
	public final static String STEAMID = "steamid";
	public final static String STEAMROLELIST = "steamrolelist";
	public final static String STEAMOUTROLELIST = "steamoutrolelist";
	public final static String SLEADERID = "sleaderid";
	public final static String SZHENFA = "szhenfa";
	public final static String DTEAMID = "dteamid";
	public final static String DTEAMROLELIST = "dteamrolelist";
	public final static String DTEAMOUTROLELIST = "dteamoutrolelist";
	public final static String DLEADERID = "dleaderid";
	public final static String DZHENFA = "dzhenfa";
	public final static String BABYUSED = "babyused";
	public final static String SRCUSERID = "srcuserid";
	public final static String SRCRID = "srcrid";
	public final static String DSTUSERID = "dstuserid";
	public final static String DSTRID = "dstrid";
	public final static String SRCLEV = "srclev";
	public final static String SRCLEVTYPE = "srclevtype";
	public final static String DSTLEV = "dstlev";
	public final static String MID = "mid";
	public final static String SIZE = "size";
	public final static String MSG = "msg";
	public final static String FLAG = "flag";
	public final static String REPUTIONID = "reputionid";
	public final static String REPUTIONNEED = "reputionneed";
	public final static String ITEMOUT = "itemout";
	public final static String ITEMIN = "itemin";
	public final static String APPENDITEM = "appenditem";
	public final static String LUCYSTONE = "lucystone";
	public final static String ITEMGUID = "itemguid";
	public final static String ENCHANT = "enchant";
	public final static String GEM = "gem";
	public final static String CHARM = "charm";
	public final static String FAMILYID = "familyid";
	public final static String FAMILYNUM = "familynum";
	public final static String FAMILYLEV = "familylev";
	public final static String FAMLEADER = "famleader";
	public final static String FAMVICELEADER = "famviceleader";
	public final static String FAMMONEY = "fammoney";
	public final static String FAMSPECIALCOUNT = "famspecialcount";
	public final static String FAMDRUGCOUNT = "famdrugcount";
	public final static String FAMBUILDINGDEGREE = "fambuildingdegree";
	public final static String FACTION = "faction";
	public final static String FACTIONNUM = "factionnum";
	public final static String FACTIONLEV = "factionlev";
	public final static String FACLEADER = "facleader";
	public final static String FACVICELEADER = "facviceleader";
	public final static String FACMONEY = "facmoney";
	public final static String FACJUYITINGLEVEL = "facjuyitinglevel";
	public final static String FACLIANGONGFANGLEVEL = "facliangongfanglevel";
	public final static String FACGONGFANGLEVEL = "facgongfanglevel";
	public final static String FACBUILDINGDEGREE = "facbuildingdegree";
	public final static String UPGRADE_TYPE = "upgrade_type";
	public final static String MAPCOPYID = "mapcopyid";
	public final static String INSTANCEID = "instanceid";
	public final static String SAVE = "save";
	public final static String POST = "post";
	public final static String GMROLEID = "gmroleid";
	public final static String GMORDER = "gmorder";
	public final static String PARAMETER = "parameter";
	public final static String SMONEYCHANGE = "smoneychange";
	public final static String HINT = "hint";
	public final static String CURRENTNUM = "currentnum";
	public final static String CURRENTMACHINE = "currentmachine";
	public final static String LEVELNUMLIST = "levelnumlist";
	public final static String MAXNUMONSRV = "maxnumonsrv";
	public final static String CASH_USED = "cash_used";
	public final static String TOKENNUM = "tokennum";
	public final static String TEAMID1 = "teamid1";
	public final static String TEAMROLELIST1 = "teamrolelist1";
	public final static String TEAMOUTROLELIST1 = "teamoutrolelist1";
	public final static String LEADERID1 = "leaderid1";
	public final static String ZHENFA1 = "zhenfa1";
	public final static String CONTRACTTIME = "contracttime";
	public final static String CONTRACTID = "contractid";
	public final static String FROM1 = "from1";
	public final static String USERID1 = "userid1";
	public final static String ROLEID1 = "roleid1";
	public final static String SHAPEID1 = "shapeid1";
	public final static String SCHOOL1 = "school1";
	public final static String LEV1 = "lev1";
	public final static String DURCHANGE = "durchange";
	public final static String PETID = "petid";
	public final static String PETUNIQID = "petuniqid";
	public final static String PETSKILL = "petskill";
	public final static String PETBLOOD = "petblood";
	public final static String PETROYAL = "petroyal";
	public final static String PETCOLOR = "petcolor";
	public final static String PETLEV = "petlev";
	public final static String PETTYPE = "pettype";
	public final static String PETID1 = "petid1";
	public final static String PETUNIQID1 = "petuniqid1";
	public final static String PETSKILL1 = "petskill1";
	public final static String PETBLOOD1 = "petblood1";
	public final static String PETROYAL1 = "petroyal1";
	public final static String PETCOLOR1 = "petcolor1";
	public final static String PETLEV1 = "petlev1";
	public final static String PETTYPE1 = "pettype1";
	public final static String ROYALCHANGE = "royalchange";
	public final static String PRODUCESKILL = "produceskill";
	public final static String ITEMS1 = "items1";
	public final static String BONUSID = "bonusid";
	public final static String OLDPOSITION = "oldposition";
	public final static String NEWPOSITION = "newposition";
	public final static String SHOPID = "shopid";
	public final static String SHOPNUMBER = "shopnumber";
	public final static String SHOPMASTER = "shopmaster";
	public final static String SHOPMONEY = "shopmoney";
	public final static String SHOPDESKNUM = "shopdesknum";
	public final static String SHOPDESKNUMCLOSE = "shopdesknumclose";
	public final static String OPERATING_FEE = "operating_fee";
	public final static String SHOPID1 = "shopid1";
	public final static String SHOPNUMBER1 = "shopnumber1";
	public final static String SHOPMASTER1 = "shopmaster1";
	public final static String SHOPMONEY1 = "shopmoney1";
	public final static String SHOPDESKNUM1 = "shopdesknum1";
	public final static String SHOPDESKNUMCLOSE1 = "shopdesknumclose1";
	public final static String OPERATING_FEE1 = "operating_fee1";
	public final static String MONSTERTYPE = "monstertype";
	public final static String MONSTERID = "monsterid";
	public final static String REPUTION = "repution";
	public final static String REPUTION1 = "repution1";
	public final static String SAVING_CHANGE = "saving_change";
	public final static String SHOPMONEYCHANGE = "shopmoneychange";
	public final static String CASHCHANGE1 = "cashchange1";
	public final static String CASHCHANGE2 = "cashchange2";
	public final static String CASHCHANGE3 = "cashchange3";
	public final static String GROUPID = "groupid";
	public final static String KEY = "key";
	public final static String CREATETYPE = "createtype";
	public final static String HASPRACTISETIMES = "haspractisetimes";
	public final static String REMAINPRACTISETIMES = "remainpractisetimes";
	public final static String STARLEVEL = "starlevel";
	public final static String GIVESKILL = "giveskill";
	public final static String GIVEBLOOD = "giveblood";
	public final static String PETAMULET = "petamulet";
	public final static String ADDTYPE = "addtype";
	public final static String OPTION = "option";
	public final static String GEMPUT1 = "gemput1";
	public final static String GEMPUT2 = "gemput2";
	public final static String GEMPUT3 = "gemput3";
	public final static String GEMPUT4 = "gemput4";
	public final static String GEMPUT5 = "gemput5";
	public final static String GEMGET = "gemget";
	public final static String ITEMID0 = "itemid0";
	public final static String UID0 = "uid0";
	public final static String ITEMID1 = "itemid1";
	public final static String UID1 = "uid1";
	public final static String PETID0 = "petid0";
	public final static String ACCOUNTID = "accountid";
	public final static String NAME = "name";
	public final static String MAXTIME = "maxtime";
	public final static String MINTIME = "mintime";
	public final static String AVGTIME = "avgtime";
	public final static String LOSSRATE = "lossrate";
	public final static String ISSUCC = "issucc";
	public final static String FILENAME = "filename";
	public final static String MODULENAME = "modulename";
	public final static String PROTOCOL = "protocol";
	public final static String DETAIL = "detail";
	public final static String ADD = "add";
	public final static String TOTAL = "total";
	public final static String REWARD = "reward";
	public final static String ERRORITEMS = "erroritems";
	public final static String ROLENAME = "rolename";
	public final static String SITEM = "sitem";
	public final static String DITEM = "ditem";
	public final static String CARD = "card";
	public final static String JBFACTIONMONEY = "jbfactionmoney";
	public final static String FACTIONID1 = "factionid1";
	public final static String FACTIONID2 = "factionid2";
	public final static String FACTIONID3 = "factionid3";
	public final static String FACTIONID4 = "factionid4";
	public final static String FACTIONID5 = "factionid5";
	public final static String FACTIONID6 = "factionid6";
	public final static String FACTIONID7 = "factionid7";
	public final static String FACTIONID8 = "factionid8";
	public final static String FACTIONID9 = "factionid9";
	public final static String FACTIONID10 = "factionid10";
	public final static String FACTIONID11 = "factionid11";
	public final static String FACTIONID12 = "factionid12";
	public final static String FACTIONID13 = "factionid13";
	public final static String FACTIONID14 = "factionid14";
	public final static String FACTIONID15 = "factionid15";
	public final static String FACTIONID16 = "factionid16";
	public final static String FACTIONID17 = "factionid17";
	public final static String FACTIONID18 = "factionid18";
	public final static String FACTIONID19 = "factionid19";
	public final static String FACTIONID20 = "factionid20";
	public final static String FACTIONID21 = "factionid21";
	public final static String FACTIONID22 = "factionid22";
	public final static String FACTIONID23 = "factionid23";
	public final static String FACTIONID24 = "factionid24";
	public final static String RESULTS = "results";
	public final static String DMONEY = "dmoney";
	public final static String TIMETYPE = "timetype";
	public final static String SELLPRICE = "sellprice";
	public final static String BUYPRICE = "buyprice";
	public final static String MODEL = "model";
	public final static String SYSTEMTYPE = "systemtype";
	public final static String SYSTEMVERSION = "systemversion";
	public final static String RESOLUTION = "resolution";
	public final static String CPUNAME = "cpuname";
	public final static String CPUMAXFARQ = "cpumaxfarq";
	public final static String CPUCOUNT = "cpucount";
	public final static String PHONENUM = "phonenum";
	public final static String GPRSIMFO = "gprsimfo";
	public final static String ACCESSPOINT = "accesspoint";
	public final static String TOTALMEMSIZE = "totalmemsize";
	public final static String UDID = "udid";
	public final static String SCORE = "score";
	public final static String PLATFORM = "platform";
	public final static String CASH = "cash";
	public final static String FUSHI = "fushi";
	public final static String ID1 = "id1";
	public final static String ID2 = "id2";
	public final static String TYPEINFO = "typeinfo";
	public final static String GETVIPKIND = "getvipkind";
	public final static String VIPTIME = "viptime";
	public final static String LOSEVIPKIND = "losevipkind";
	public final static String RANK = "rank";
	public final static String PETNAME = "petname";
	public final static String PET_SCORE = "pet_score";
	public final static String MERCENARYNAME = "mercenaryname";
	public final static String MERCENARY_SCORE = "mercenary_score";
	public final static String SPECIES = "species";
	public final static String ROBBERY = "robbery";
	public final static String DUIWU = "duiwu";
	public final static String ATTEND = "attend";
	public final static String FLOWER = "flower";
	public final static String FACTIONNAME = "factionname";
	public final static String LEADERNAME = "leadername";
	public final static String FACLEVEL = "faclevel";
	public final static String FACMEMBERCOUNT = "facmembercount";
	public final static String FACBUILDINGDEREEE = "facbuildingdereee";
	public final static String XIAKEID = "xiakeid";
	public final static String XIAKEKEY = "xiakekey";
	public final static String GET = "get";
	public final static String COLOR = "color";
	public final static String STARLV = "starlv";
	public final static String LOSE = "lose";
	public final static String XIAYI = "xiayi";
	public final static String PARMS = "parms";
	public final static String SKILLS = "skills";
	public final static String SKILLS1 = "skills1";
	public final static String PROP = "prop";
	public final static String PROP1 = "prop1";
	public final static String EXP1 = "exp1";
	public final static String CHONGXUELV = "chongxuelv";
	public final static String CHONGXUEPROCESS = "chongxueprocess";
	public final static String POINTCHANGED = "pointchanged";
	public final static String EXPREST = "exprest";
	public final static String SMPOINT = "smpoint";
	public final static String WGPOINT = "wgpoint";
	public final static String NGPOINT = "ngpoint";
	public final static String WFPOINT = "wfpoint";
	public final static String NFPOINT = "nfpoint";
	public final static String SDPOINT = "sdpoint";
	public final static String TEAMNAME = "teamname";
	public final static String CREATETIME = "createtime";
	public final static String TEAMMEMBERS = "teammembers";
	public final static String DISSOLVETIME = "dissolvetime";
	public final static String TEAMMEMBES = "teammembes";
	public final static String REMAINPOINT = "remainpoint";
	public final static String TEAMNAME1 = "teamname1";
	public final static String CHANGCI1 = "changci1";
	public final static String TEAMWINTIMES = "teamwintimes";
	public final static String TEAMID2 = "teamid2";
	public final static String TEAMNAME2 = "teamname2";
	public final static String CHANGCI2 = "changci2";
	public final static String TEAMWINTIMES2 = "teamwintimes2";
	public final static String RESON = "reson";
	public final static String CHANGEDNUM = "changednum";
	public final static String CENGSHU = "cengshu";
	public final static String ITEMINFO = "iteminfo";
	public final static String PLAYTIME = "playtime";
	public final static String EQUIPITEMID = "equipitemid";
	public final static String SRCAPPENDATTRID = "srcappendattrid";
	public final static String INDEX = "index";
	public final static String NEWAPPENDATTRID = "newappendattrid";
	public final static String ACTIONTYPE = "actiontype";
	public final static String ID = "id";
	public final static String DEVOICETYPE = "devoicetype";
	public final static String ACTTYPE = "acttype";
	public final static String ACTSWORNGET = "actswornget";
	public final static String MAXACTSWORN = "maxactsworn";
	public final static String MAXDAILYSWORN = "maxdailysworn";
	public final static String ACTUALSWORN = "actualsworn";
	public final static String TOTOLSWORN = "totolsworn";
	public final static String SWORNLEVEL = "swornlevel";
	public final static String REMARK = "remark";
	public final static String PUTINTO = "putinto";
	public final static String COST = "cost";
	public final static String NUM = "num";
	public final static String PREVBLESSLV = "prevblesslv";
	public final static String NEXTBLESSLV = "nextblesslv";
	public final static String CHECKPOINT = "checkpoint";
	public final static String PUTIN = "putin";
	public final static String PUTOUT = "putout";
	public final static String YINLIANGNUM = "yinliangnum";
	public final static String GOLDNUM = "goldcoinnum";
	public final static String FUSHINUM = "fushinum";
	public final static String ATTR = "attr";
}
