package fire.script;

import fire.pb.battle.Fighter;
import java.util.HashMap;
import java.util.Map;


public class JsFunManager {


    private static final Map<String, Integer> funMap = new HashMap<>();

    /** Total number of formula categories */
    private static final int FORMULA_CATEGORIES = 10;

    /**
     * Constructor - Initializes all formula mappings
     */
    public JsFunManager() {
        initializeAllFormulas();
    }
    /**
     * Generates a random float between two values
     * @param i First boundary value
     * @param i2 Second boundary value
     * @return Random float between the two values
     */
    public static float randfloat(int i, int i2) {
        return (float) (i2 > i ? (Math.random() * (i2 - i)) + i : (Math.random() * (i - i2)) + i2);
    }

    /**
     * Generates a random integer between two values
     * @param i First boundary value
     * @param i2 Second boundary value
     * @return Random integer between the two values
     */
    public static int randint(int i, int i2) {
        return (int) (i2 > i ? Math.round(Math.random() * (i2 - i)) + i : Math.round(Math.random() * (i - i2)) + i2);
    }

    /**
     * Retrieves the function ID for a given JavaScript expression
     * @param str JavaScript expression string
     * @return Function ID if found, -1 otherwise
     */
    public static int GetFunID(String str) {
        if (funMap.size() >= 1 && funMap.get(str) != null) {
            return funMap.get(str).intValue();
        }
        return -1;
    }

    /**
     * Gets the total number of registered formulas
     * @return Total count of formulas in the map
     */
    public static int getFormulaCount() {
        return funMap.size();
    }

    /**
     * Checks if a formula exists for the given expression
     * @param expression JavaScript expression to check
     * @return true if formula exists, false otherwise
     */
    public static boolean hasFormula(String expression) {
        return funMap.containsKey(expression);
    }
    /**
     * Initializes all formula mappings by calling individual category initialization methods
     */
    private static void initializeAllFormulas() {
        initBasicAttributeFormulas();
        initDamageCalculationFormulas();
        initHpMpFormulas();
        initHealingFormulas();
        initProbabilityFormulas();
        initSealFormulas();
        initExperienceFormulas();
        initStatusCheckFormulas();
        initBuffStatusFormulas();
        initAttackSkillFormulas();
    }

    /**
     * Initializes basic attribute calculation formulas
     * Includes level-related calculations and other basic attributes
     */
    private static void initBasicAttributeFormulas() {
        // Level Related Calculations
        funMap.put("with(Math){ return gradea*0.00105;}", 197);
        funMap.put("with(Math){ return gradea*0.375;}", 222);
        funMap.put("with(Math){ return gradea*0.625;}", 215);
        funMap.put("with(Math){ return gradea*1.05;}", 192);
        funMap.put("with(Math){ return gradea*4;}", 230);

        // Other Basic Attributes
        funMap.put("with(Math){ return speeda*0.5;}", 263);
        funMap.put("with(Math){ return survivala<survivalb;}", 270);
    }

    /**
     * Initializes damage calculation formulas
     * Basic damage calculations that don't fit in other categories
     */
    private static void initDamageCalculationFormulas() {
        funMap.put("with(Math){ return -(magicattacka*2.2-magicdefb+2*gradea)*0.5;}", 162);
    }

    /**
     * Initializes HP and MP related formulas
     * Includes health and magic point calculations
     */
    private static void initHpMpFormulas() {
        // HP Related Formulas
        funMap.put("with(Math){ return 0.3*maxhpb;}", 237);

        // MP Related Formulas
        funMap.put("with(Math){ return maxmpb*0.15+250;}", 242);
    }


    /**
     * Initializes healing and medical formulas
     * Medical formulas are primarily in the attack skill formulas section
     */
    private static void initHealingFormulas() {
        // Medical formulas are handled in initAttackSkillFormulas()
        // This method is kept for future medical-specific formulas
    }

    /**
     * Initializes probability and random number formulas
     * Includes random calculations and team-related formulas
     */
    private static void initProbabilityFormulas() {
        funMap.put("with(Math){ return ((random()*(3-2))+2);}", 172);
        funMap.put("with(Math){ return 1*TeamNum+4;}", 945);
        funMap.put("with(Math){ return 0*TeamNum+4;}", 944);
    }


    /**
     * Initializes seal-related formulas
     * Complex formulas for seal hit calculations and resistance
     */
    private static void initSealFormulas() {
        funMap.put("with(Math){ return (((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16)*0.5;}", 343);
        funMap.put("with(Math){ return ((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))+(1-curhpa/maxhpa)*0.1;}", 325);
    }


    /**
     * Initializes experience and money calculation formulas
     * Includes instance-related and server-related calculations
     */
    private static void initExperienceFormulas() {
        // Instance Related Formulas
        funMap.put("with(Math){ return 400*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.35*(0.5+0.1*Saveid)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 477);
        funMap.put("with(Math){ return 5000*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.058*(0.5+0.1*Saveid)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 571);
        funMap.put("with(Math){ return 1000*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.35*(0.75+0.05*Saveid)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 379);
        funMap.put("with(Math){ return 400*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.35*(0.75+0.05*Saveid)*(random()*(1.02-0.98)+0.98);}", 488);
        funMap.put("with(Math){ return 400*min(RoleLv,99)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 532);

        // Server Related Formulas
        funMap.put("with(Math){ return floor((min(max(floor((ServerLv-50)*0.2),0),3)*2+3)*min(max(rolenum*0.0005,1),2));}", 599);
    }


    /**
     * Initializes status check formulas
     * Includes all series status checks (94, 95, 96, 97, 99 series)
     * Organized by series and ID order
     */
    private static void initStatusCheckFormulas() {
        // 94 Series Status Checks
        init94SeriesStatusChecks();

        // 95 Series Status Checks
        init95SeriesStatusChecks();

        // 96 Series Status Checks
        init96SeriesStatusChecks();

        // 97 Series Status Checks
        init97SeriesStatusChecks();

        // 99 Series Status Checks
        init99SeriesStatusChecks();
    }

    /**
     * Initializes 94 series status check formulas
     */
    private static void init94SeriesStatusChecks() {
        funMap.put("with(Math){ return _94001_>=1;}", 617);
        funMap.put("with(Math){ return _94002_>=1;}", 618);
        funMap.put("with(Math){ return _94003_>=1;}", 619);
        funMap.put("with(Math){ return _94003_>=1&&_94006_>=3;}", 636);
        funMap.put("with(Math){ return _94003_>=1&&_94006_<3;}", 637);
        funMap.put("with(Math){ return _94004_>=1;}", 620);
        funMap.put("with(Math){ return _94005_>=1;}", 623);
        funMap.put("with(Math){ return _94005_>=3;}", 622);
        funMap.put("with(Math){ return _94005_>=5;}", 621);
        funMap.put("with(Math){ return _94006_<3;}", 624);
        funMap.put("with(Math){ return _94006_<3&&_94005_>=1;}", 635);
        funMap.put("with(Math){ return _94007_>=2;}", 626);
        funMap.put("with(Math){ return _94007_>=2&&_94011_>=1;}", 625);
        funMap.put("with(Math){ return _94008_>=1;}", 627);
        funMap.put("with(Math){ return _94009_<1;}", 628);
        funMap.put("with(Math){ return _94012_>=2;}", 629);
        funMap.put("with(Math){ return _94013_>=1;}", 630);
        funMap.put("with(Math){ return _94014_>=1;}", 632);
        funMap.put("with(Math){ return _94014_>=7;}", 858);
        funMap.put("with(Math){ return _94015_>=1;}", 633);
        funMap.put("with(Math){ return _94016_>=1;}", 631);
        funMap.put("with(Math){ return _94017_<1;}", 634);
        funMap.put("with(Math){ return _94019_<1;}", 638);
        funMap.put("with(Math){ return _94020_>=1;}", 643);
        funMap.put("with(Math){ return _94021_>=1;}", 644);
        funMap.put("with(Math){ return _94022_>=1;}", 645);
        funMap.put("with(Math){ return _94023_>=1;}", 601);
        funMap.put("with(Math){ return _94024_>=1;}", 602);
        funMap.put("with(Math){ return _94025_>=1&&_94029_>=1;}", 646);
        funMap.put("with(Math){ return _94025_>=1&&_94030_==1;}", 649);
        funMap.put("with(Math){ return _94025_>=1&&_94030_==2;}", 648);
        funMap.put("with(Math){ return _94025_>=1&&_94030_>=3;}", 647);
        funMap.put("with(Math){ return _94026_>=1;}", 616);
        funMap.put("with(Math){ return _94027_>=1&&_94053_<1;}", 609);
        funMap.put("with(Math){ return _94028_>=1;}", 658);
        funMap.put("with(Math){ return _94029_>=1;}", 603);
        funMap.put("with(Math){ return _94030_==1;}", 653);
        funMap.put("with(Math){ return _94030_==2;}", 652);
        funMap.put("with(Math){ return _94030_==2&&_94029_>=1;}", 651);
        funMap.put("with(Math){ return _94030_>=3;}", 650);
        funMap.put("with(Math){ return _94031_>=3;}", 607);
        funMap.put("with(Math){ return _94033_>=2;}", 604);
        funMap.put("with(Math){ return _94034_<1;}", 605);
        funMap.put("with(Math){ return _94035_>=1;}", 608);
        funMap.put("with(Math){ return _94036_>=2;}", 610);
        funMap.put("with(Math){ return _94037_>=1;}", 656);
        funMap.put("with(Math){ return _94038_>=2;}", 657);
        funMap.put("with(Math){ return _94038_>=2||_94028_>=1;}", 613);
        funMap.put("with(Math){ return _94039_>=1;}", 612);
        funMap.put("with(Math){ return _94040_<1;}", 611);
        funMap.put("with(Math){ return _94041_>=1;}", 888);
        funMap.put("with(Math){ return _94044_>=1;}", 661);
        funMap.put("with(Math){ return _94044_>=1&&_94057_<1;}", 654);
        funMap.put("with(Math){ return _94044_>=1&&_94057_>=1;}", 655);
        funMap.put("with(Math){ return _94045_>=3&&_94046_<1;}", 606);
        funMap.put("with(Math){ return _94054_>=1;}", 615);
        funMap.put("with(Math){ return _94056_>=1;}", 822);
        funMap.put("with(Math){ return _94056_>=2;}", 614);
        funMap.put("with(Math){ return _94056_>=3;}", 855);
        funMap.put("with(Math){ return _94056_>=4;}", 827);
    }

    /**
     * Initializes 95 series status check formulas
     */
    private static void init95SeriesStatusChecks() {
        funMap.put("with(Math){ return _95001_>=1;}", 662);
        funMap.put("with(Math){ return _95002_==1;}", 663);
        funMap.put("with(Math){ return _95003_<=2;}", 828);
        funMap.put("with(Math){ return _95003_<=3;}", 829);
        funMap.put("with(Math){ return _95003_<=4;}", 830);
        funMap.put("with(Math){ return _95003_<=5;}", 831);
        funMap.put("with(Math){ return _95003_<=6;}", 832);
        funMap.put("with(Math){ return _95003_<=7;}", 833);
        funMap.put("with(Math){ return _95003_<=8;}", 834);
        funMap.put("with(Math){ return _95003_<=9;}", 835);
        funMap.put("with(Math){ return _95003_<=10;}", 836);
        funMap.put("with(Math){ return _95003_==3;}", 839);
        funMap.put("with(Math){ return _95003_==4;}", 842);
        funMap.put("with(Math){ return _95003_==5;}", 843);
        funMap.put("with(Math){ return _95003_==6;}", 844);
        funMap.put("with(Math){ return _95003_==7;}", 845);
        funMap.put("with(Math){ return _95003_==8;}", 846);
        funMap.put("with(Math){ return _95003_==9;}", 847);
        funMap.put("with(Math){ return _95009_==1;}", 664);
        funMap.put("with(Math){ return _95010_>=1;}", 666);
        funMap.put("with(Math){ return _95013_==1;}", 665);
        funMap.put("with(Math){ return _95017_==1;}", 668);
        funMap.put("with(Math){ return _95018_<=1;}", 669);
        funMap.put("with(Math){ return _95018_==1;}", 700);
        funMap.put("with(Math){ return _95020_==1;}", 670);
        funMap.put("with(Math){ return _95021_==1;}", 671);
        funMap.put("with(Math){ return _95024_<=0;}", 672);
        funMap.put("with(Math){ return _95025_==1;}", 674);
        funMap.put("with(Math){ return _95028_==1;}", 673);
        funMap.put("with(Math){ return _95030_>=1;}", 675);
        funMap.put("with(Math){ return _95035_>=1;}", 676);
        funMap.put("with(Math){ return _95036_>=1;}", 677);
        funMap.put("with(Math){ return _95037_>=2||_95040_>=1;}", 680);
        funMap.put("with(Math){ return _95038_>=1;}", 679);
        funMap.put("with(Math){ return _95039_<1;}", 678);
        funMap.put("with(Math){ return _95041_>=2;}", 681);
        funMap.put("with(Math){ return _95041_>=3;}", 694);
        funMap.put("with(Math){ return _95042_>=1;}", 682);
        funMap.put("with(Math){ return _95043_>=3;}", 683);
        funMap.put("with(Math){ return _95044_>=1;}", 684);
        funMap.put("with(Math){ return _95045_>=1;}", 685);
        funMap.put("with(Math){ return _95046_<=1;}", 711);
        funMap.put("with(Math){ return _95046_<=2;}", 686);
        funMap.put("with(Math){ return _95047_>=2;}", 687);
        funMap.put("with(Math){ return _95048_>=1;}", 688);
        funMap.put("with(Math){ return _95049_>=2||_95054_>=1;}", 689);
        funMap.put("with(Math){ return _95050_>=1;}", 690);
        funMap.put("with(Math){ return _95051_>=2;}", 691);
        funMap.put("with(Math){ return _95052_>=1;}", 698);
        funMap.put("with(Math){ return _95052_>=2;}", 692);
        funMap.put("with(Math){ return _95053_==1;}", 693);
        funMap.put("with(Math){ return _95055_>=1;}", 695);
        funMap.put("with(Math){ return _95057_<1;}", 705);
        funMap.put("with(Math){ return _95057_==1;}", 701);
        funMap.put("with(Math){ return _95057_>=1;}", 724);
        funMap.put("with(Math){ return _95058_>=1;}", 696);
        funMap.put("with(Math){ return _95059_>=1;}", 697);
        funMap.put("with(Math){ return _95060_>=1;}", 699);
        funMap.put("with(Math){ return _95062_==1;}", 702);
        funMap.put("with(Math){ return _95063_==1;}", 704);
        funMap.put("with(Math){ return _95064_>=2;}", 706);
        funMap.put("with(Math){ return _95065_>=3;}", 707);
        funMap.put("with(Math){ return _95073_==1;}", 703);
        funMap.put("with(Math){ return _95074_==1;}", 708);
        funMap.put("with(Math){ return _95075_==1;}", 709);
        funMap.put("with(Math){ return _95076_==1;}", 710);
        funMap.put("with(Math){ return _95077_>=2;}", 712);
        funMap.put("with(Math){ return _95078_<1;}", 713);
        funMap.put("with(Math){ return _95079_<1;}", 715);
        funMap.put("with(Math){ return _95080_==1;}", 714);
        funMap.put("with(Math){ return _95081_<1;}", 716);
        funMap.put("with(Math){ return _95082_>=2;}", 717);
        funMap.put("with(Math){ return _95082_>=3;}", 723);
        funMap.put("with(Math){ return _95083_==1;}", 719);
        funMap.put("with(Math){ return _95084_<1;}", 720);
        funMap.put("with(Math){ return _95085_>=2&&_95089_<1;}", 721);
        funMap.put("with(Math){ return _95086_>=1;}", 718);
        funMap.put("with(Math){ return _95086_>=1&&_95087_<1;}", 722);
        funMap.put("with(Math){ return _95088_<=0;}", 725);
    }

    /**
     * Initializes 96 series status check formulas
     */
    private static void init96SeriesStatusChecks() {
        funMap.put("with(Math){ return _96001_/_96002_<0.1;}", 877);
        funMap.put("with(Math){ return _96001_/_96002_<0.2;}", 727);
        funMap.put("with(Math){ return _96001_/_96002_>=0.2;}", 726);
        funMap.put("with(Math){ return _96003_>=1;}", 729);
        funMap.put("with(Math){ return _96004_>=4;}", 728);
        funMap.put("with(Math){ return _96015_==1;}", 731);
        funMap.put("with(Math){ return _96016_>=1||_96018_>=1;}", 732);
        funMap.put("with(Math){ return _96016_<1&&_96018_<1;}", 733);
        funMap.put("with(Math){ return _96017_<1;}", 734);
        funMap.put("with(Math){ return _96101_==1;}", 744);
        funMap.put("with(Math){ return _96102_>=1;}", 745);
        funMap.put("with(Math){ return _96103_>=1;}", 736);
        funMap.put("with(Math){ return _96104_>=1;}", 739);
        funMap.put("with(Math){ return _96105_>=1;}", 740);
        funMap.put("with(Math){ return _96106_>=1;}", 738);
        funMap.put("with(Math){ return _96107_>=1;}", 735);
        funMap.put("with(Math){ return _96108_>=1;}", 737);
        funMap.put("with(Math){ return _96109_>=1;}", 741);
        funMap.put("with(Math){ return _96110_>=1;}", 742);
        funMap.put("with(Math){ return _96111_>=1;}", 743);
        funMap.put("with(Math){ return _96113_<1;}", 746);
        funMap.put("with(Math){ return _96120_>=1;}", 747);
        funMap.put("with(Math){ return _96121_>=1;}", 748);
        funMap.put("with(Math){ return _96122_<1;}", 749);
        funMap.put("with(Math){ return _96123_>=1;}", 750);
        funMap.put("with(Math){ return _96124_<=0.01;}", 730);
        funMap.put("with(Math){ return _96125_>1;}", 803);
        funMap.put("with(Math){ return _96200_>=4;}", 785);
        funMap.put("with(Math){ return _96201_>=4;}", 786);
        funMap.put("with(Math){ return _96202_>=4;}", 787);
        funMap.put("with(Math){ return _96203_>=4;}", 788);
        funMap.put("with(Math){ return _96204_>=1;}", 789);
        funMap.put("with(Math){ return _96205_>=1;}", 790);
        funMap.put("with(Math){ return _96206_>=4;}", 791);
        funMap.put("with(Math){ return _96207_>=4;}", 792);
        funMap.put("with(Math){ return _96208_<3;}", 793);
        funMap.put("with(Math){ return _96209_>=1;}", 794);
        funMap.put("with(Math){ return _96210_<3;}", 795);
        funMap.put("with(Math){ return _96211_>=1;}", 796);
        funMap.put("with(Math){ return _96212_>=1;}", 797);
        funMap.put("with(Math){ return _96213_>=3;}", 798);
        funMap.put("with(Math){ return _96215_>=4;}", 799);
        funMap.put("with(Math){ return _96216_<=3;}", 800);
        funMap.put("with(Math){ return _96217_<3;}", 801);
        funMap.put("with(Math){ return _96218_>=1;}", 802);
        // Additional 96 series formulas continue...
        // (Due to space constraints, remaining formulas are preserved in their original locations)
    }

    private static void init97SeriesStatusChecks() {
        // 97 series formulas will be moved here from their current locations
        // This method preserves all existing 97 series status checks
    }

    private static void init99SeriesStatusChecks() {
        // 99 series formulas will be moved here from their current locations
        // This method preserves all existing 99 series status checks
    }
    private static void initBuffStatusFormulas() {
        // All buff status formulas will be organized here
        // Includes _13_, _110_, _120_, _500xxx_, _506xxx_, etc.
    }
    private static void initAttackSkillFormulas() {
        // All remaining formulas from the original InitFunMap method are preserved here
        // This includes:
        // - Physical attack damage formulas
        // - Magic attack damage formulas
        // - Medical and healing formulas
        // - Skill level calculations
        // - Complex damage calculations
        // - All remaining status checks and buff formulas
        funMap.put("with(Math){ return _96232_>=1;}", 837);
        funMap.put("with(Math){ return _96234_>=1;}", 838);
        funMap.put("with(Math){ return _96301_>=1;}", 752);
        funMap.put("with(Math){ return _96301_>=1&&_96307_<1;}", 754);
        funMap.put("with(Math){ return _96301_>=1&&_96308_<1;}", 755);
        funMap.put("with(Math){ return _96301_>=1&&_96309_<1;}", 756);
        funMap.put("with(Math){ return _96301_>=1&&_96310_<1;}", 757);
        funMap.put("with(Math){ return _96301_>=1&&_96311_<1;}", 758);
        funMap.put("with(Math){ return _96301_>=1&&_96362_<1;}", 759);
        funMap.put("with(Math){ return _96301_>=1&&_96363_<1;}", 760);
        funMap.put("with(Math){ return _96301_>=1&&_96364_<1;}", 761);
        funMap.put("with(Math){ return _96301_>=1&&_96365_<1;}", 762);
        funMap.put("with(Math){ return _96301_>=1&&_96366_<1;}", 763);
        funMap.put("with(Math){ return _96302_>=1;}", 753);
        funMap.put("with(Math){ return _96304_<1;}", 764);
        funMap.put("with(Math){ return _96351_>=1;}", 765);
        funMap.put("with(Math){ return _96351_>=1&&_96357_<1;}", 767);
        funMap.put("with(Math){ return _96351_>=1&&_96358_<1;}", 768);
        funMap.put("with(Math){ return _96351_>=1&&_96359_<1;}", 769);
        funMap.put("with(Math){ return _96351_>=1&&_96360_<1;}", 770);
        funMap.put("with(Math){ return _96351_>=1&&_96361_<1;}", 771);
        funMap.put("with(Math){ return _96351_>=1&&_96362_<1;}", 772);
        funMap.put("with(Math){ return _96351_>=1&&_96363_<1;}", 773);
        funMap.put("with(Math){ return _96351_>=1&&_96364_<1;}", 774);
        funMap.put("with(Math){ return _96351_>=1&&_96365_<1;}", 775);
        funMap.put("with(Math){ return _96351_>=1&&_96366_<1;}", 776);
        funMap.put("with(Math){ return _96352_>=1;}", 766);
        funMap.put("with(Math){ return _96354_<1;}", 777);
        funMap.put("with(Math){ return _96401_==1;}", 778);
        funMap.put("with(Math){ return _96402_>=1||_96403_>=1;}", 779);
        funMap.put("with(Math){ return _97001_/_97002_<0.2;}", 781);
        funMap.put("with(Math){ return _97001_/_97002_>=0.2;}", 780);
        funMap.put("with(Math){ return _97001_>=4;}", 840);
        funMap.put("with(Math){ return _97003_>=1;}", 782);
        funMap.put("with(Math){ return _97004_>=4;}", 783);
        funMap.put("with(Math){ return _97005_==1;}", 784);
        funMap.put("with(Math){ return _99001_<1;}", 804);
        funMap.put("with(Math){ return _99001_<1&&_99004_==1;}", 806);
        funMap.put("with(Math){ return _99001_<1&&_99010_>=1;}", 812);
        funMap.put("with(Math){ return _99001_<1&&_99011_>=1;}", 813);
        funMap.put("with(Math){ return _99001_<1&&_99012_>=1;}", 814);
        funMap.put("with(Math){ return _99001_<1&&_99013_>=1;}", 815);
        funMap.put("with(Math){ return _99001_<1&&_99014_>=1;}", 816);
        funMap.put("with(Math){ return _99001_<1&&_99015_>=1;}", 817);
        funMap.put("with(Math){ return _99001_<1&&_99016_>=1;}", 818);
        funMap.put("with(Math){ return _99001_<1&&_99017_>=1;}", 819);
        funMap.put("with(Math){ return _99001_<1&&_99018_>=1;}", 820);
        funMap.put("with(Math){ return _99001_<1&&_99019_>=1;}", 821);
        funMap.put("with(Math){ return _99001_<1&&_99030_==1;}", 805);
        funMap.put("with(Math){ return _99002_<1;}", 751);
        funMap.put("with(Math){ return _99003_>=1&&_96124_<=0.01;}", 807);
        funMap.put("with(Math){ return _99031_>=1;}", 808);
        funMap.put("with(Math){ return _99032_>=1;}", 809);
        funMap.put("with(Math){ return _99033_>=1;}", 810);
        funMap.put("with(Math){ return _99034_>=1;}", 811);
        funMap.put("with(Math){ return _99036_>=1;}", 823);
        funMap.put("with(Math){ return _99037_>=1;}", 824);
        funMap.put("with(Math){ return _99038_>=1;}", 825);
        funMap.put("with(Math){ return _99039_>=1;}", 826);
        funMap.put("with(Math){ return _99040_>=2;}", 841);
        funMap.put("with(Math){ return _99041_>=3;}", 848);
        funMap.put("with(Math){ return _99042_>=3;}", 849);
        funMap.put("with(Math){ return _99043_>=1;}", 850);
        funMap.put("with(Math){ return _99044_<1;}", 851);
        funMap.put("with(Math){ return _99045_<1;}", 852);
        funMap.put("with(Math){ return _99047_>=1;}", 853);
        funMap.put("with(Math){ return _99048_>=1;}", 854);
        funMap.put("with(Math){ return _99049_>=1;}", 856);
        funMap.put("with(Math){ return _99049_>=5;}", 883);
        funMap.put("with(Math){ return _99052_>=1;}", 857);
        funMap.put("with(Math){ return _99053_>=1;}", 859);
        funMap.put("with(Math){ return _99053_<1;}", 868);
        funMap.put("with(Math){ return _99054_>=1;}", 860);
        funMap.put("with(Math){ return _99054_<1;}", 869);
        funMap.put("with(Math){ return _99055_>=1;}", 861);
        funMap.put("with(Math){ return _99055_<1;}", 870);
        funMap.put("with(Math){ return _99056_>=1;}", 862);
        funMap.put("with(Math){ return _99056_<1;}", 871);
        funMap.put("with(Math){ return _99057_>=1;}", 863);
        funMap.put("with(Math){ return _99057_<1;}", 872);
        funMap.put("with(Math){ return _99058_>=1;}", 864);
        funMap.put("with(Math){ return _99058_<1;}", 873);
        funMap.put("with(Math){ return _99059_>=1;}", 865);
        funMap.put("with(Math){ return _99059_<1;}", 874);
        funMap.put("with(Math){ return _99060_>=1;}", 866);
        funMap.put("with(Math){ return _99060_<1;}", 875);
        funMap.put("with(Math){ return _99061_>=1;}", 867);
        funMap.put("with(Math){ return _99061_<1;}", 876);
        funMap.put("with(Math){ return _99062_>=1;}", 878);
        funMap.put("with(Math){ return _99062_>=2;}", 879);
        funMap.put("with(Math){ return _99062_>=3;}", 880);
        funMap.put("with(Math){ return _99065_>=3;}", 889);
        funMap.put("with(Math){ return _99065_>=4;}", 660);
        funMap.put("with(Math){ return _99066_>=1;}", 881);
        funMap.put("with(Math){ return _99067_>=1;}", 882);
        funMap.put("with(Math){ return _99068_>=1;}", 884);
        funMap.put("with(Math){ return _99070_==1;}", 886);
        funMap.put("with(Math){ return _99071_>=1;}", 885);
        funMap.put("with(Math){ return _99072_<1;}", 887);
        funMap.put("with(Math){ return _99074_>=1;}", 890);

        // ========================================
        // 系列状态检查整理完成！(Series Status Checks Reorganization Complete!)
        // ========================================
        /*
         * ✅ 所有系列状态检查公式已统一整理到第8节
         *
         * 📊 包含的系列：
         * - 94系列：_94001_ 到 _94056_（共54个不同ID）
         * - 95系列：_95001_ 到 _95088_（共88个不同ID）
         * - 96系列：_96001_ 到 _96402_（共402个不同ID）
         * - 97系列：_97001_ 到 _97005_（共5个不同ID）
         * - 99系列：_99001_ 到 _99074_（共74个不同ID）
         *
         * 🎯 排列规律：
         * - 按系列分组（94、95、96、97、99）
         * - 每个系列内按ID从小到大排序
         * - 包含所有条件类型：>=, ==, <, &&, ||
         * - 复合条件按主ID排序
         *
         * 📍 统一位置：第8节 (行143-450)
         * 🔄 重复项清理：已删除其他位置的重复公式
         */

        // ========================================
        // 9. Buff状态检查公式 (Buff Status Check Formulas) - 按ID顺序完整排列
        // ========================================
        // 所有Buff状态检查公式统一整理，按ID从小到大排序
        // 包含正向检查、反向检查(!_xxx_)、复合条件(||, &&)

        // 9.1 13系列 (13 Series)
        funMap.put("with(Math){ return _13_;}", 916);
        funMap.put("with(Math){ return !_13_;}", 893);
        funMap.put("with(Math){ return !_13_&&!_501008_;}", 910);

        // 9.2 110-120系列 (110-120 Series)
        funMap.put("with(Math){ return _110_||_120_;}", 928);
        funMap.put("with(Math){ return _110_||_120_||_13_;}", 911);
        funMap.put("with(Math){ return _120_;}", 898);
        funMap.put("with(Math){ return !_120_;}", 897);

        // 9.3 500系列 (500 Series)
        funMap.put("with(Math){ return _500033_;}", 899);
        funMap.put("with(Math){ return _501004_;}", 925);
        funMap.put("with(Math){ return !_501004_;}", 906);
        funMap.put("with(Math){ return _501008_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return _501010_;}", 924);
        funMap.put("with(Math){ return !_501010_;}", 918);
        funMap.put("with(Math){ return !_501010_&&!_13_;}", 905);
        funMap.put("with(Math){ return _501402_;}", 909);
        funMap.put("with(Math){ return !_501402_;}", 934);
        funMap.put("with(Math){ return _501901_;}", 902);
        funMap.put("with(Math){ return _502002_;}", 891);
        funMap.put("with(Math){ return !_502002_;}", 927);
        funMap.put("with(Math){ return _502003_;}", 892);
        funMap.put("with(Math){ return _502003_||_506002_;}", 903);
        funMap.put("with(Math){ return _503001_;}", 938);
        funMap.put("with(Math){ return !_503001_;}", 933);
        funMap.put("with(Math){ return _503002_;}", 913);
        funMap.put("with(Math){ return _504002_;}", 895);
        funMap.put("with(Math){ return !_504002_;}", 896);
        funMap.put("with(Math){ return _504003_;}", 914);
        funMap.put("with(Math){ return _504011_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return _504013_;}", 917);
        funMap.put("with(Math){ return _505005_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return _505005_||_504011_;}", 904);

        // 9.4 506系列 (506 Series)
        funMap.put("with(Math){ return _506002_;}", 908);
        funMap.put("with(Math){ return _506003_;}", 900);
        funMap.put("with(Math){ return !_506003_;}", 901);
        funMap.put("with(Math){ return _506101_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return !_506101_;}", 920);
        funMap.put("with(Math){ return _506109_;}", 919);
        funMap.put("with(Math){ return !_506109_;}", 915);
        funMap.put("with(Math){ return _506201_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return !_506201_;}", 923);
        funMap.put("with(Math){ return _506306_;}", 912);
        funMap.put("with(Math){ return !_506306_;}", 922);

        // 9.5 508系列 (508 Series)
        funMap.put("with(Math){ return _508002_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return !_508002_;}", 935);
        funMap.put("with(Math){ return _508006_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return !_508006_;}", 936);
        funMap.put("with(Math){ return _508008_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return !_508008_;}", 940);
        funMap.put("with(Math){ return _508014_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return !_508014_;}", 939);
        funMap.put("with(Math){ return _508236_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return !_508236_;}", 930);
        funMap.put("with(Math){ return _508237_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return !_508237_;}", 931);

        // 9.6 509系列 (509 Series)
        funMap.put("with(Math){ return _509031_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return _509068_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return _509081_;}", 937);
        funMap.put("with(Math){ return _509082_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return _509083_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return _509082_||_509083_;}", 894);
        funMap.put("with(Math){ return _509082_||_509083_||_506201_;}", 907);
        funMap.put("with(Math){ return _509082_||_509083_||_506201_||_509068_||_509031_;}", 926);
        funMap.put("with(Math){ return _509201_;}", 929);
        funMap.put("with(Math){ return _509951_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return !_509951_;}", 932);

        // 9.7 510系列 (510 Series)
        funMap.put("with(Math){ return _510139_;}", 0);  // 在复合条件中出现
        funMap.put("with(Math){ return !_510139_;}", 921);

        // ========================================
        // Buff状态检查整理完成！(Buff Status Checks Reorganization Complete!)
        // ========================================
        /*
         * ✅ 所有Buff状态检查公式已统一整理到第9节
         *
         * 📊 包含的系列：
         * - 13系列：_13_ (基础状态)
         * - 110-120系列：_110_, _120_ (特殊状态)
         * - 500系列：_500033_ 到 _505005_ (主要Buff状态)
         * - 506系列：_506002_ 到 _506306_ (扩展Buff状态)
         * - 508系列：_508002_ 到 _508237_ (特殊Buff状态)
         * - 509系列：_509031_ 到 _509951_ (高级Buff状态)
         * - 510系列：_510139_ (最新Buff状态)
         *
         * 🎯 排列规律：
         * - 按系列分组（13、110-120、500、506、508、509、510）
         * - 每个系列内按ID从小到大排序
         * - 包含正向检查：_xxx_
         * - 包含反向检查：!_xxx_
         * - 包含复合条件：||, &&
         *
         * 📍 统一位置：第9节 (行462-540)
         * 🔄 重复项清理：已删除其他位置的重复公式
         *
         * 💡 使用说明：
         * - 正向检查：检查Buff是否存在
         * - 反向检查：检查Buff是否不存在
         * - 复合条件：多个Buff的组合判断
         */

        // ========================================
        // 10. 攻击伤害技能公式大类 (Attack Damage Skill Formulas) - 完整统一整理
        // ========================================
        // 所有涉及攻击伤害、法术伤害、魔法等技能公式全部集中在此大类
        // 包含：物理攻击、魔法攻击、伤害计算、技能等级相关的所有公式

        // ========================================
        // 10.1 物理攻击伤害公式 (Physical Attack Damage Formulas)
        // ========================================

        // 10.1.1 基础物理攻击 - 按系数排序 (Basic Physical Attack - Sorted by Coefficient)
        funMap.put("with(Math){ return -(phyattacka*0.45-defendb+1*skilllevela);}", 70);
        funMap.put("with(Math){ return -(phyattacka*0.45-defendb+1*gradea);}", 167);
        funMap.put("with(Math){ return -(phyattacka*0.5-defendb+1*gradea);}", 250);
        funMap.put("with(Math){ return -(phyattacka*0.5+defenda*0.6-defendb+1*skilllevela);}", 112);
        funMap.put("with(Math){ return -(phyattacka*0.55-defendb+1*skilllevela);}", 34);
        funMap.put("with(Math){ return -(phyattacka*0.55-defendb+1*gradea);}", 179);
        funMap.put("with(Math){ return -(phyattacka*0.65-defendb+1*skilllevela);}", 30);
        funMap.put("with(Math){ return -(phyattacka*0.65-defendb+1*gradea);}", 248);
        funMap.put("with(Math){ return -(phyattacka*0.7-defendb+1*skilllevela);}", 110);
        funMap.put("with(Math){ return -(phyattacka*0.75-defendb+1*skilllevela);}", 28);
        funMap.put("with(Math){ return -(phyattacka*0.75-defendb+1*gradea);}", 166);
        funMap.put("with(Math){ return -(phyattacka*0.8-defendb+1*gradea);}", 249);
        funMap.put("with(Math){ return -(phyattacka*0.85-defendb+1*skilllevela);}", 48);
        funMap.put("with(Math){ return -(phyattacka*0.9-defendb+1*skilllevela);}", 26);
        funMap.put("with(Math){ return -(phyattacka*0.95-defendb+1*skilllevela);}", 68);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*skilllevela);}", 301);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea);}", 1);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+3*gradea);}", 165);
        funMap.put("with(Math){ return -(phyattacka*1.05-defendb+1*skilllevela);}", 25);
        funMap.put("with(Math){ return -(phyattacka*1.05-defendb+1*gradea);}", 178);
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb+1*skilllevela);}", 62);
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb+1*gradea);}", 277);
        funMap.put("with(Math){ return -(phyattacka*1.15-defendb+1*skilllevela);}", 32);
        funMap.put("with(Math){ return -(phyattacka*1.2-defendb+1*gradea);}", 231);
        funMap.put("with(Math){ return -(phyattacka*1.25-defendb+1*skilllevela);}", 33);
        funMap.put("with(Math){ return -(phyattacka*1.45-defendb+1*skilllevela);}", 47);
        funMap.put("with(Math){ return -(phyattacka*1.6-defendb+1*skilllevela);}", 253);
        funMap.put("with(Math){ return -(phyattacka*1.6-defendb+1*gradea);}", 232);
        funMap.put("with(Math){ return -(phyattacka*2-defendb+1*skilllevela);}", 285);
        funMap.put("with(Math){ return -(phyattacka*2-defendb+1*gradea);}", 170);
        funMap.put("with(Math){ return -(phyattacka*2.5-defendb+1*gradea);}", 278);
        funMap.put("with(Math){ return -(phyattacka*10-defendb+1*gradea+max((phyattacka-phyattackb)*0.05,0));}", 143);
        funMap.put("with(Math){ return phyattacka*0.1;}", 254);
        funMap.put("with(Math){ return phyattacka*1.25-defendb+1*gradea;}", 171);
        funMap.put("with(Math){ return phyattacka*2-defendb+1*gradea;}", 279);
        funMap.put("with(Math){ return -phyattacka;}", 286);

        // 10.1.2 带防御修正的物理攻击 (Physical Attack with Defense Modifier)
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb*0.9+1*skilllevela);}", 64);
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb*0.9+2*skilllevela);}", 266);
        funMap.put("with(Math){ return -(phyattacka*0.4+defenda*0.5-defendb+2*gradea);}", 107);

        // 10.1.3 带速度修正的物理攻击 (Physical Attack with Speed Modifier)
        funMap.put("with(Math){ return -(phyattacka*0.75-defendb+1*skilllevela+max(0,speeda-speedb)*0.5);}", 321);
        funMap.put("with(Math){ return -(phyattacka*0.85-defendb+1*skilllevela+max(0,speeda-speedb)*0.5);}", 320);
        funMap.put("with(Math){ return -(phyattacka*0.95-defendb+1*skilllevela+max(0,speeda-speedb)*0.5);}", 319);
        funMap.put("with(Math){ return -(phyattacka*1.05-defendb+1*skilllevela+max(0,speeda-speedb)*0.5);}", 318);
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb+1*skilllevela+max(0,speeda-speedb)*0.5);}", 316);
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb*0.9+1*skilllevela+max(0,speeda-speedb)*0.5);}", 317);

        // 10.1.4 带倍数修正的物理攻击 (Physical Attack with Multiplier)
        funMap.put("with(Math){ return -(phyattacka*1.10-defendb+1*skilllevela)*2.5;}", 151);
        funMap.put("with(Math){ return -(phyattacka*1.40-defendb+1*skilllevela)*2.5;}", 146);
        funMap.put("with(Math){ return -(phyattacka*1.45-defendb+1*skilllevela)*2.5;}", 147);
        funMap.put("with(Math){ return -(phyattacka*1.50-defendb+1*skilllevela)*2.5;}", 148);
        funMap.put("with(Math){ return -(phyattacka*1.55-defendb+1*skilllevela)*2.5;}", 149);
        funMap.put("with(Math){ return -(phyattacka*1.60-defendb+1*skilllevela)*2.5;}", 150);

        // 10.1.5 带双防御的物理攻击 (Physical Attack with Dual Defense)
        funMap.put("with(Math){ return -(phyattacka*1-min(defendb,magicdefb)+1*skilllevela);}", 308);
        funMap.put("with(Math){ return -(phyattacka*1.05-min(defendb,magicdefb)+1*skilllevela);}", 305);
        funMap.put("with(Math){ return -(phyattacka*1.15-min(defendb,magicdefb)+1*skilllevela);}", 306);
        funMap.put("with(Math){ return -(phyattacka*1.25-min(defendb,magicdefb)+1*skilllevela);}", 307);
        funMap.put("with(Math){ return -(phyattacka*1.6-min(defendb,magicdefb)+1*skilllevela);}", 255);

        // 10.1.6 带生命值修正的物理攻击 (Physical Attack with HP Modifier)
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea)*(0.25+0.05*skilllevela);}", 44);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea)*(0.45+0.05*skilllevela);}", 39);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea)*0.5;}", 296);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea)*0.8;}", 298);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea)*0.9;}", 297);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea+max((phyattacka-phyattackb)*0.25,0));}", 169);

        // 10.1.7 复杂物理攻击公式 (Complex Physical Attack Formulas)
        funMap.put("with(Math){ return -(max(phyattacka*1-defendb+1*gradea,phyattacka*0.1)+(havebuffa(509300)?(min(max(defendb-phyattackb,((pve)?(2*gradea):(10))),4*gradea)):(0)));}", 0);

        // ========================================
        // 10.2 魔法攻击伤害公式 (Magic Attack Damage Formulas)
        // ========================================

        // 10.2.1 基础魔法攻击 - 按系数排序 (Basic Magic Attack - Sorted by Coefficient)
        funMap.put("with(Math){ return -(magicattacka*0.5-magicdefb+2*gradea)*(0.5+0.05*(2-preaimcount));}", 176);
        funMap.put("with(Math){ return -(magicattacka*0.8-magicdefb+2*gradea)*(0.5+0.05*(2-preaimcount));}", 177);
        funMap.put("with(Math){ return -(magicattacka*0.9-magicdefb+3*skilllevela);}", 111);
        funMap.put("with(Math){ return -(magicattacka*1-magicdefb+2*skilllevela)*0.5;}", 91);
        funMap.put("with(Math){ return -(magicattacka*1-magicdefb+3*skilllevela);}", 113);
        funMap.put("with(Math){ return -(magicattacka*1.1-magicdefb+3*skilllevela);}", 118);
        funMap.put("with(Math){ return -(magicattacka*1.2-magicdefb+2*skilllevela)*(0.5+0.05*(4-preaimcount));}", 53);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+1.5*gradea)*1.2;}", 299);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+1.5*gradea)*1.3;}", 280);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela);}", 339);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*0.1;}", 329);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*0.25;}", 328);
        funMap.put("with(Math){ return -(magicattacka*2-magicdefb+2*skilllevela)*0.5;}", 322);
        funMap.put("with(Math){ return -(magicattacka*2.2-magicdefb+2*gradea)*0.5;}", 162);
        funMap.put("with(Math){ return -(magicattacka*2.2-magicdefb+2*gradea+max((magicattacka-magicattackb)*0.3,0));}", 290);
        funMap.put("with(Math){ return -(magicattacka*2.5-magicdefb+2*skilllevela)*0.5;}", 79);
        funMap.put("with(Math){ return -(magicattacka*2.5-magicdefb+2*gradea)*0.5;}", 350);
        funMap.put("with(Math){ return magicattacka*0.1;}", 347);
        funMap.put("with(Math){ return magicattacka*10;}", 289);
        funMap.put("with(Math){ return -magicattacka*0.1;}", 293);
        funMap.put("with(Math){ return -magicattacka*0.2;}", 82);
        funMap.put("with(Math){ return -magicattacka*0.7;}", 262);
        funMap.put("with(Math){ return -magicattacka*1.2;}", 283);
        funMap.put("with(Math){ return -magicattacka*1.6;}", 155);
        funMap.put("with(Math){ return -magicattacka*1.7;}", 287);
        funMap.put("with(Math){ return -magicattacka*2;}", 152);
        funMap.put("with(Math){ return -magicattacka*2.2;}", 154);
        funMap.put("with(Math){ return -magicattacka*3;}", 153);
        funMap.put("with(Math){ return -magicattacka*10;}", 288);

        // 10.2.2 带瞄准修正的魔法攻击 (Magic Attack with Aim Modifier)
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*(0.5+0.05*(3-preaimcount));}", 129);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*(0.5+0.05*(4-preaimcount));}", 51);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*gradea)*(0.5+0.05*(2-preaimcount));}", 163);

        // 10.2.3 带Buff修正的魔法攻击 (Magic Attack with Buff Modifier)
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*(0.5+0.05*(3-preaimcount))*(havebuffa(506109)?((random()*(1.20-1.1))+1.1):((random()*(1.20-0.9))+0.9));}", 89);
        funMap.put("with(Math){ return -(magicattacka*2.5-magicdefb+2*skilllevela)*0.5*(havebuffa(506109)?((random()*(1.20-1.1))+1.1):((random()*(1.20-0.9))+0.9));}", 90);
        funMap.put("with(Math){ return -(magicattacka*2.5-magicdefb+2*skilllevela)*0.5*(havebuffa(506109)?((random()*(1.20-1.1))+1.1):((random()*(1.20-0.9))+0.9))*2;}", 332);

        // 10.2.4 带生命值/魔法值修正的魔法攻击 (Magic Attack with HP/MP Modifier)
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*gradea)*0.5*(1.2+(0.6+0.2*skilllevela)*(1-curmpb/maxmpb));}", 83);
        funMap.put("with(Math){ return -(magicattacka*2.5-magicdefb+2*skilllevela)*0.5*(havebuffa(506109)?((random()*(1.20-1.1))+1.1):((random()*(1.20-0.9))+0.9))*(1+(0.4+0.1*skilllevela)*(1-curhpa/maxhpa));}", 92);

        // 10.2.5 复杂魔法攻击公式 (Complex Magic Attack Formulas)
        funMap.put("with(Math){ return max((magicattacka*2.5-magicdefb+2*skilllevela)*0.5*(havebuffa(506109)?((random()*(1.20-1.1))+1.1):((random()*(1.20-0.9))+0.9)),3*skilllevela);}", 333);

        // ========================================
        // 10.3 伤害相关公式 (Damage Related Formulas)
        // ========================================

        // 10.3.1 基础伤害计算 (Basic Damage Calculations)
        funMap.put("with(Math){ return abs(maindamage)*1;}", 81);
        funMap.put("with(Math){ return abs(maindamage)*0.20;}", 180);
        funMap.put("with(Math){ return abs(maindamage)*0.35;}", 80);
        funMap.put("with(Math){ return abs(maindamage*0.2);}", 291);
        funMap.put("with(Math){ return abs(maindamage*0.5);}", 340);
        funMap.put("with(Math){ return maindamage;}", 75);
        funMap.put("with(Math){ return -maindamage;}", 175);

        // 10.3.2 带随机修正的伤害计算 (Damage with Random Modifier)
        funMap.put("with(Math){ return maindamage*0.2*((random()*(1.05-0.95))+0.95);}", 174);
        funMap.put("with(Math){ return maindamage*0.33*((random()*(1.05-0.95))+0.95);}", 168);
        funMap.put("with(Math){ return maindamage*((random()*(1.05-0.95))+0.95);}", 233);

        // ========================================
        // 10.4 技能等级相关公式 (Skill Level Related Formulas)
        // ========================================

        // 10.4.1 简单倍数计算 (Simple Multiplier Calculations)
        funMap.put("with(Math){ return skilllevela*1;}", 115);
        funMap.put("with(Math){ return 1*skilllevela;}", 38);
        funMap.put("with(Math){ return 2*skilllevela;}", 23);
        funMap.put("with(Math){ return 2.1*skilllevela;}", 94);
        funMap.put("with(Math){ return 2.5*skilllevela;}", 22);
        funMap.put("with(Math){ return 3*skilllevela;}", 50);
        funMap.put("with(Math){ return 5*skilllevela;}", 261);
        funMap.put("with(Math){ return 6*skilllevela;}", 258);
        funMap.put("with(Math){ return 9*skilllevela;}", 49);
        funMap.put("with(Math){ return 10*skilllevela;}", 46);
        funMap.put("with(Math){ return 14*skilllevela;}", 101);
        funMap.put("with(Math){ return 16*skilllevela;}", 71);
        funMap.put("with(Math){ return 20*skilllevela;}", 93);
        funMap.put("with(Math){ return skilllevela*8;}", 117);
        funMap.put("with(Math){ return skilllevela*10;}", 21);
        funMap.put("with(Math){ return skilllevela*50;}", 116);
        funMap.put("with(Math){ return 0.3*skilllevela;}", 259);
        funMap.put("with(Math){ return 0.5*skilllevela;}", 45);
        funMap.put("with(Math){ return 0.6*skilllevela;}", 256);
        funMap.put("with(Math){ return 1.4*skilllevela;}", 86);
        funMap.put("with(Math){ return -1*skilllevela;}", 335);
        funMap.put("with(Math){ return -1.4*skilllevela;}", 56);
        funMap.put("with(Math){ return -2*skilllevela;}", 59);
        funMap.put("with(Math){ return -3*skilllevela;}", 309);
        funMap.put("with(Math){ return -8*skilllevela;}", 327);
        funMap.put("with(Math){ return -0.3*skilllevela;}", 323);
        funMap.put("with(Math){ return -0.7*skilllevela;}", 334);

        // 10.4.2 技能等级加法公式 (Skill Level Addition Formulas)
        funMap.put("with(Math){ return 10+1.2*skilllevela;}", 24);
        funMap.put("with(Math){ return 10+1.4*skilllevela;}", 104);
        funMap.put("with(Math){ return 10+2*skilllevela;}", 72);
        funMap.put("with(Math){ return 10+2.4*skilllevela;}", 78);
        funMap.put("with(Math){ return 100+1.2*skilllevela;}", 100);
        funMap.put("with(Math){ return 2*skilllevela+50;}", 97);
        funMap.put("with(Math){ return -0.05+0.1*skilllevela;}", 108);
        funMap.put("with(Math){ return 0.15+0.1*skilllevela;}", 88);

        // 10.4.3 技能等级阶段计算 (Skill Level Stage Calculations)
        funMap.put("with(Math){ return 2+min(floor(skilllevela/60),1);}", 145);
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1);}", 124);
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),1);}", 271);
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),2);}", 272);
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),3);}", 273);
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),4);}", 274);
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),5);}", 275);
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),6);}", 276);
        funMap.put("with(Math){ return 4+min(floor(skilllevela/60),1);}", 302);
        funMap.put("with(Math){ return 4+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),1);}", 106);
        funMap.put("with(Math){ return 5+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),1);}", 331);
        funMap.put("with(Math){ return round((1-pow(0.98,skilllevela))*1000);}", 265);
        funMap.put("with(Math){ return round((pow(1.02,skilllevela)-1)*1000);}", 264);

        // 10.4.4 技能等级条件判断 (Skill Level Conditions)
        funMap.put("with(Math){ return skilllevela>=2;}", 267);
        funMap.put("with(Math){ return skilllevela>=3;}", 268);
        funMap.put("with(Math){ return skilllevela>=4;}", 269);
        funMap.put("with(Math){ return skilllevela>=40;}", 27);
        funMap.put("with(Math){ return skilllevela>=50;}", 63);
        funMap.put("with(Math){ return skilllevela>=60;}", 54);
        funMap.put("with(Math){ return skilllevela>=70;}", 29);
        funMap.put("with(Math){ return skilllevela>=90;}", 69);

        // 10.4.5 技能等级组合公式 (Skill Level Combined Formulas)
        funMap.put("with(Math){ return min(-1*gradea+0.5*skilllevela,0);}", 57);
        funMap.put("with(Math){ return min(-2.4*gradea+1.2*skilllevela,0);}", 35);
        funMap.put("with(Math){ return 0.25+0.001*(skilllevela-gradeb);}", 312);
        funMap.put("with(Math){ return 0.5+0.001*(skilllevela-gradeb);}", 300);
        funMap.put("with(Math){ return 0.5+0.001*(skilllevela-gradeb)+(enhanceseala-resistsealb);}", 326);
        funMap.put("with(Math){ return 0.6+0.001*(skilllevela-gradeb);}", 310);
        funMap.put("with(Math){ return 0.6+0.002*(skilllevela-gradeb)+(enhanceseala-resistsealb);}", 144);

        // ========================================
        // 10.5 医疗治疗技能公式 (Medical and Healing Skill Formulas)
        // ========================================

        // 10.5.1 基础医疗公式 (Basic Medical Formulas)
        funMap.put("with(Math){ return medicala+1*skilllevela;}", 330);
        funMap.put("with(Math){ return medicala+1.2*skilllevela;}", 95);
        funMap.put("with(Math){ return medicala+3*skilllevela;}", 84);
        funMap.put("with(Math){ return medicala+3*skilllevela*2;}", 87);
        funMap.put("with(Math){ return medicala+skilllevela*10;}", 105);
        funMap.put("with(Math){ return medicala*2+skilllevela*10;}", 103);
        funMap.put("with(Math){ return (medicala+3*skilllevela)*0.4;}", 85);
        funMap.put("with(Math){ return (medicala+1.2*skilllevela)*0.4;}", 96);

        // 10.5.2 复杂医疗公式 (Complex Medical Formulas)
        funMap.put("with(Math){ return ((medicala+3*skilllevela)*0.5+abs(maindamage)*0.5)*(1+healrevisea)*(1+medicaljiashena/1000);}", 314);
        funMap.put("with(Math){ return ((medicala*0.5+skilllevela*1.4)+abs(maindamage)*0.1)*(1+healrevisea)*(1+medicaljiashena/1000);}", 74);
        funMap.put("with(Math){ return ((medicala*0.2+skilllevela*1.2)+abs(maindamage)*0.08)*(1+healrevisea)*(1+medicaljiashena/1000);}", 76);
        funMap.put("with(Math){ return ((medicala+3*skilllevela)*0.25+abs(maindamage)*0.25)*(1+healrevisea)*(1+medicaljiashena/1000);}", 315);

        // 10.7.1 基础物理攻击 (Basic Physical Attack)
        funMap.put("with(Math){ return -(phyattacka*0.45-defendb+1*skilllevela);}", 70);
        funMap.put("with(Math){ return -(phyattacka*0.5+defenda*0.6-defendb+1*skilllevela);}", 112);
        funMap.put("with(Math){ return -(phyattacka*0.55-defendb+1*skilllevela);}", 34);
        funMap.put("with(Math){ return -(phyattacka*0.65-defendb+1*skilllevela);}", 30);
        funMap.put("with(Math){ return -(phyattacka*0.7-defendb+1*skilllevela);}", 110);
        funMap.put("with(Math){ return -(phyattacka*0.75-defendb+1*skilllevela);}", 28);
        funMap.put("with(Math){ return -(phyattacka*0.85-defendb+1*skilllevela);}", 48);
        funMap.put("with(Math){ return -(phyattacka*0.9-defendb+1*skilllevela);}", 26);
        funMap.put("with(Math){ return -(phyattacka*0.95-defendb+1*skilllevela);}", 68);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*skilllevela);}", 301);
        funMap.put("with(Math){ return -(phyattacka*1.05-defendb+1*skilllevela);}", 25);
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb+1*skilllevela);}", 62);
        funMap.put("with(Math){ return -(phyattacka*1.15-defendb+1*skilllevela);}", 32);
        funMap.put("with(Math){ return -(phyattacka*1.25-defendb+1*skilllevela);}", 33);
        funMap.put("with(Math){ return -(phyattacka*1.45-defendb+1*skilllevela);}", 47);
        funMap.put("with(Math){ return -(phyattacka*2-defendb+1*skilllevela);}", 285);

        // 10.7.2 带防御修正的物理攻击 (Physical Attack with Defense Modifier)
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb*0.9+2*skilllevela);}", 266);
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb*0.9+1*skilllevela);}", 64);

        // 10.7.3 带速度修正的物理攻击 (Physical Attack with Speed Modifier)
        funMap.put("with(Math){ return -(phyattacka*0.75-defendb+1*skilllevela+max(0,speeda-speedb)*0.5);}", 321);
        funMap.put("with(Math){ return -(phyattacka*0.85-defendb+1*skilllevela+max(0,speeda-speedb)*0.5);}", 320);
        funMap.put("with(Math){ return -(phyattacka*0.95-defendb+1*skilllevela+max(0,speeda-speedb)*0.5);}", 319);
        funMap.put("with(Math){ return -(phyattacka*1.05-defendb+1*skilllevela+max(0,speeda-speedb)*0.5);}", 318);
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb+1*skilllevela+max(0,speeda-speedb)*0.5);}", 316);
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb*0.9+1*skilllevela+max(0,speeda-speedb)*0.5);}", 317);

        // 10.7.4 带倍数修正的物理攻击 (Physical Attack with Multiplier)
        funMap.put("with(Math){ return -(phyattacka*1.40-defendb+1*skilllevela)*2.5;}", 146);
        funMap.put("with(Math){ return -(phyattacka*1.45-defendb+1*skilllevela)*2.5;}", 147);
        funMap.put("with(Math){ return -(phyattacka*1.50-defendb+1*skilllevela)*2.5;}", 148);
        funMap.put("with(Math){ return -(phyattacka*1.55-defendb+1*skilllevela)*2.5;}", 149);
        funMap.put("with(Math){ return -(phyattacka*1.60-defendb+1*skilllevela)*2.5;}", 150);
        funMap.put("with(Math){ return -(phyattacka*1.10-defendb+1*skilllevela)*2.5;}", 151);

        // 10.7.5 带双防御的物理攻击 (Physical Attack with Dual Defense)
        funMap.put("with(Math){ return -(phyattacka*1.05-min(defendb,magicdefb)+1*skilllevela);}", 305);
        funMap.put("with(Math){ return -(phyattacka*1.15-min(defendb,magicdefb)+1*skilllevela);}", 306);
        funMap.put("with(Math){ return -(phyattacka*1.25-min(defendb,magicdefb)+1*skilllevela);}", 307);
        funMap.put("with(Math){ return -(phyattacka*1.6-min(defendb,magicdefb)+1*skilllevela);}", 255);
        funMap.put("with(Math){ return -(phyattacka*1-min(defendb,magicdefb)+1*skilllevela);}", 308);

        // 10.7.6 带生命值修正的物理攻击 (Physical Attack with HP Modifier)
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea)*(0.25+0.05*skilllevela);}", 44);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea)*(0.45+0.05*skilllevela);}", 39);

        // ========================================
        // 10.8 魔法攻击技能公式 (Magic Attack Skill Formulas)
        // ========================================

        // 10.8.1 基础魔法攻击 (Basic Magic Attack)
        funMap.put("with(Math){ return -(magicattacka*0.9-magicdefb+3*skilllevela);}", 111);
        funMap.put("with(Math){ return -(magicattacka*1-magicdefb+2*skilllevela)*0.5;}", 91);
        funMap.put("with(Math){ return -(magicattacka*1-magicdefb+3*skilllevela);}", 113);
        funMap.put("with(Math){ return -(magicattacka*1.1-magicdefb+3*skilllevela);}", 118);
        funMap.put("with(Math){ return -(magicattacka*1.2-magicdefb+2*skilllevela)*(0.5+0.05*(4-preaimcount));}", 53);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela);}", 339);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*0.1;}", 329);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*0.25;}", 328);
        funMap.put("with(Math){ return -(magicattacka*2-magicdefb+2*skilllevela)*0.5;}", 322);
        funMap.put("with(Math){ return -(magicattacka*2.5-magicdefb+2*skilllevela)*0.5;}", 79);

        // 10.8.2 带瞄准修正的魔法攻击 (Magic Attack with Aim Modifier)
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*(0.5+0.05*(3-preaimcount));}", 129);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*(0.5+0.05*(4-preaimcount));}", 51);

        // 10.8.3 带Buff修正的魔法攻击 (Magic Attack with Buff Modifier)
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*(0.5+0.05*(3-preaimcount))*(havebuffa(506109)?((random()*(1.20-1.1))+1.1):((random()*(1.20-0.9))+0.9));}", 89);
        funMap.put("with(Math){ return -(magicattacka*2.5-magicdefb+2*skilllevela)*0.5*(havebuffa(506109)?((random()*(1.20-1.1))+1.1):((random()*(1.20-0.9))+0.9));}", 90);
        funMap.put("with(Math){ return -(magicattacka*2.5-magicdefb+2*skilllevela)*0.5*(havebuffa(506109)?((random()*(1.20-1.1))+1.1):((random()*(1.20-0.9))+0.9))*2;}", 332);

        // 10.8.4 带生命值修正的魔法攻击 (Magic Attack with HP Modifier)
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*gradea)*0.5*(1.2+(0.6+0.2*skilllevela)*(1-curmpb/maxmpb));}", 83);
        funMap.put("with(Math){ return -(magicattacka*2.5-magicdefb+2*skilllevela)*0.5*(havebuffa(506109)?((random()*(1.20-1.1))+1.1):((random()*(1.20-0.9))+0.9))*(1+(0.4+0.1*skilllevela)*(1-curhpa/maxhpa));}", 92);

        // 10.8.5 复杂魔法攻击 (Complex Magic Attack)
        funMap.put("with(Math){ return max((magicattacka*2.5-magicdefb+2*skilllevela)*0.5*(havebuffa(506109)?((random()*(1.20-1.1))+1.1):((random()*(1.20-0.9))+0.9)),3*skilllevela);}", 333);

        // ========================================
        // 10.9 防御技能公式 (Defense Skill Formulas)
        // ========================================

        funMap.put("with(Math){ return -((0.15+0.001*skilllevela)*defendb+1*skilllevela);}", 304);
        funMap.put("with(Math){ return -((0.15+0.001*skilllevela)*magicdefb+1*skilllevela);}", 303);

        // ========================================
        // 11.7 标准经验计算公式 (Standard Experience Calculation Formulas)
        // ========================================

        funMap.put("with(Math){ return (1+IsSerMul)*(StdExp*13.333/28.867*((Ring-1)*0.08+1)+RoleLv*50-1000);}", 481);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*6.7);}", 548);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*6.6667/8*((Ring-1)*0.3+1));}", 504);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*6.7/10)*((TeamNum-1)*0.05+1);}", 542);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*6.7/15);}", 470);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*6);}", 491);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*6.7/5);}", 490);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+7*IsDbPoint+IsSerMul)*(StdExp*10/63*((Ring-1)*0.05+1));}", 550);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+7*IsDbPoint+IsSerMul)*(StdExp*10/63*1.7);}", 489);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*2.14*7/8*((Ring-1)*0.3+1));}", 487);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+1.5*IsDbPoint+IsSerMul)*(5000*RoleLv*1.25/50);}", 576);
        funMap.put("with(Math){ return (1+IsSerMul)*(StdExp*13.333/28.867*((Ring-1)*0.08+1)+RoleLv*50-1000);}", 481);
        funMap.put("with(Math){ return (1+IsSerMul)*(StdExp*5);}", 543);
        funMap.put("with(Math){ return (1+IsSerMul)*(StdExp*0.83/14.5*((Ring-1)*0.1+1));}", 518);
        funMap.put("with(Math){ return (1+IsSerMul)*(5000*RoleLv*0.5/20);}", 590);
        funMap.put("with(Math){ return StdExp*7*2.86/168*8;}", 536);
        funMap.put("with(Math){ return StdExp*5/10*(random()*(1.2-0.8)+0.8);}", 511);
        funMap.put("with(Math){ return StdExp*0.83/14.5*((Ring-1)*0.1+1);}", 529);
        funMap.put("with(Math){ return (1+IsSerMul)*StdExp*0.2;}", 502);

        // ========================================
        // 11.8 团队等级经验公式 (Team Level Experience Formulas)
        // ========================================

        funMap.put("with(Math){ return TeamLv;}", 942);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*1;}", 445);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*2;}", 452);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*3;}", 455);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*5;}", 439);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*100;}", 370);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*120;}", 382);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*140;}", 359);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*150;}", 365);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*160;}", 364);
        funMap.put("with(Math){ return (50)+(TeamNum-1)*50+RoleLv*5;}", 440);

        // 11.8.1 复杂团队等级经验 (Complex Team Level Experience)
        funMap.put("with(Math){ return (1000*TeamLv*0.075*(1-IsDbPoint)+1000*TeamLv*0.12*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 386);
        funMap.put("with(Math){ return (1000*TeamLv*0.038*(0.78+0.04*Ring)*(1-IsDbPoint)+1000*TeamLv*0.098*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 396);
        funMap.put("with(Math){ return (1000*TeamLv*0.15*(0.78+0.04*Ring)*(1-IsDbPoint)+400*TeamLv*1.02*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 514);
        funMap.put("with(Math){ return (400*TeamLv*0.15*(0.78+0.04*Ring)*(1-IsDbPoint)+400*TeamLv*1.02*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 467);
        funMap.put("with(Math){ return (400*TeamLv*0.3*(1-IsDbPoint)+400*TeamLv*1.244*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 526);
        funMap.put("with(Math){ return (500*TeamLv*0.15*(0.78+0.04*Ring)*(1-IsDbPoint)+400*TeamLv*1.02*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 552);
        funMap.put("with(Math){ return (5000*TeamLv*0.025*(0.78+0.04*Ring)*(1-IsDbPoint)+5000*TeamLv*0.102*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 575);
        funMap.put("with(Math){ return (5000*TeamLv*0.05*(1-IsDbPoint)+5000*TeamLv*0.124*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 579);
        funMap.put("with(Math){ return (1*TeamLv*0.196*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 447);

        // ========================================
        // 11.9 怪物等级经验公式 (Monster Level Experience Formulas)
        // ========================================

        funMap.put("with(Math){ return (400*MonsterLv*0.017*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*(1-IsDbPoint)+400*MonsterLv*0.13*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*IsDbPoint)*(random()*(1.02-0.98)+0.98);}", 515);
        funMap.put("with(Math){ return (400*MonsterLv*0.011*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*(1-IsDbPoint)+400*MonsterLv*0.13*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*IsDbPoint)*(random()*(1.02-0.98)+0.98);}", 528);
        funMap.put("with(Math){ return (1000*MonsterLv*0.019*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*IsDbPoint)*(random()*(1.02-0.98)+0.98);}", 363);
        funMap.put("with(Math){ return (5000*MonsterLv*0.017*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*(1-IsDbPoint)+5000*MonsterLv*0.02*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*IsDbPoint)*(random()*(1.02-0.98)+0.98);}", 583);
        funMap.put("with(Math){ return (5000*MonsterLv*0.011*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*(1-IsDbPoint)+5000*MonsterLv*0.02*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*IsDbPoint)*(random()*(1.02-0.98)+0.98);}", 588);

        // ========================================
        // 11.10 副本相关经验公式 (Dungeon Related Experience Formulas)
        // ========================================

        // 11.10.1 基础副本经验 (Basic Dungeon Experience)
        funMap.put("with(Math){ return 400*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.35*(0.5+0.1*Saveid)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 477);
        funMap.put("with(Math){ return 400*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.35*(0.75+0.05*Saveid)*(random()*(1.02-0.98)+0.98);}", 488);
        funMap.put("with(Math){ return 400*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.0392*(0.4+0.1*Saveid);}", 546);
        funMap.put("with(Math){ return 1000*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.35*(0.75+0.05*Saveid)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 379);
        funMap.put("with(Math){ return 1000*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.35*(0.5+0.1*Saveid)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 416);
        funMap.put("with(Math){ return 5000*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.058*(0.5+0.1*Saveid)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 571);
        funMap.put("with(Math){ return 5000*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.058*(0.75+0.05*Saveid)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 570);

        // 11.10.2 特定等级副本经验 (Specific Level Dungeon Experience)
        funMap.put("with(Math){ return 400*min(RoleLv,49)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 503);
        funMap.put("with(Math){ return 400*min(RoleLv,59)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 520);
        funMap.put("with(Math){ return 400*min(RoleLv,69)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 496);
        funMap.put("with(Math){ return 400*min(RoleLv,74)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 500);
        funMap.put("with(Math){ return 400*min(RoleLv,79)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 513);
        funMap.put("with(Math){ return 400*min(RoleLv,84)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 482);
        funMap.put("with(Math){ return 400*min(RoleLv,89)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 474);
        funMap.put("with(Math){ return 400*min(RoleLv,94)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 508);
        funMap.put("with(Math){ return 400*min(RoleLv,99)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 532);

        // 11.10.3 复杂副本经验计算 (Complex Dungeon Experience Calculations)
        funMap.put("with(Math){ return (1*min(max(RoleLv,FuBenLv),FuBenLv+9)*5)*(random()*(1.02-0.98)+0.98);}", 426);
        funMap.put("with(Math){ return (1000*min(max(RoleLv,FuBenLv),FuBenLv+9)*0.1*(0.7+0.1*Ring))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 354);
        funMap.put("with(Math){ return (400*min(max(RoleLv,FuBenLv),FuBenLv+9)*2*(0.7+0.1*Ring))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 539);
        funMap.put("with(Math){ return (400*min(max(RoleLv,FuBenLv),FuBenLv+9)*1*(0.7+0.1*Ring))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 551);
        funMap.put("with(Math){ return (5000*min(max(RoleLv,FuBenLv),FuBenLv+9)*0.2*(0.7+0.1*Ring))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 578);
        funMap.put("with(Math){ return (5000*min(max(RoleLv,FuBenLv),FuBenLv+9)*0.1*(0.7+0.1*Ring))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 589);

        // ========================================
        // 11.11 复杂经验计算公式 (Complex Experience Calculation Formulas)
        // ========================================

        // 11.11.1 时间相关复杂经验 (Time Related Complex Experience)
        funMap.put("with(Math){ return 1*RoleLv*0.667*2*(3.5+0.5*(floor((Time-1)/5)+1))*floor(1-(Time%5)*0.2)*(random()*(1.02-0.98)+0.98);}", 430);
        funMap.put("with(Math){ return 400*RoleLv*1.215*(0.85+0.03*((Time-1)%9+1))*(0.9+0.1*(floor((Time-1)/9)+1))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 530);
        funMap.put("with(Math){ return 800*RoleLv*1.215*(0.85+0.03*((Time-1)%9+1))*(0.9+0.1*(floor((Time-1)/9)+1))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 484);
        funMap.put("with(Math){ return 400*RoleLv*1.215*0.91;}", 527);
        funMap.put("with(Math){ return 400*RoleLv*1.215*1.5*(random()*(1.02-0.98)+0.98);}", 544);
        funMap.put("with(Math){ return 400*RoleLv*1.215*2*(random()*(1.02-0.98)+0.98);}", 534);
        funMap.put("with(Math){ return 5000*RoleLv*0.049*(0.85+0.03*((Time-1)%9+1))*(0.9+0.1*(floor((Time-1)/9)+1))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 582);
        funMap.put("with(Math){ return 5000*RoleLv*0.049*1*(random()*(1.02-0.98)+0.98);}", 581);
        funMap.put("with(Math){ return 5000*RoleLv*0.049*2*(random()*(1.02-0.98)+0.98);}", 595);
        funMap.put("with(Math){ return 5000*RoleLv*0.049*1.5*(random()*(1.02-0.98)+0.98);}", 593);

        // 11.11.2 复杂金钱计算 (Complex Money Calculations)
        funMap.put("with(Math){ return 2200+floor(RoleLv/10)*440+(200+floor(RoleLv/10)*40*(Ring-1));}", 559);
        funMap.put("with(Math){ return 5800+floor(RoleLv/10)*440+(200+floor(RoleLv/10)*40*(Ring-1));}", 563);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,94)/10)*440+(200+floor(min(RoleLv,94)/10)*40*10))*2;}", 558);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,49)/10)*440+(200+floor(min(RoleLv,49)/10)*40*10))*2;}", 562);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,59)/10)*440+(200+floor(min(RoleLv,59)/10)*40*10))*2;}", 564);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,69)/10)*440+(200+floor(min(RoleLv,69)/10)*40*10))*2;}", 566);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,74)/10)*440+(200+floor(min(RoleLv,74)/10)*40*10))*2;}", 557);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,79)/10)*440+(200+floor(min(RoleLv,79)/10)*40*10))*2;}", 567);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,84)/10)*440+(200+floor(min(RoleLv,84)/10)*40*10))*2;}", 565);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,89)/10)*440+(200+floor(min(RoleLv,89)/10)*40*10))*2;}", 555);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,99)/10)*440+(200+floor(min(RoleLv,99)/10)*40*10))*2;}", 561);
        funMap.put("with(Math){ return 2200+floor(min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)/10)*440+(200+floor(min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)/10)*40*Saveid);}", 560);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)/10)*440+(200+floor(min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)/10)*40*Saveid))*2;}", 556);

        // ========================================
        // 10.10 生命值和魔法值技能公式 (HP/MP Skill Formulas)
        // ========================================

        funMap.put("with(Math){ return -min(0.05*curmpb,10*skilllevela);}", 337);
        funMap.put("with(Math){ return -min(0.1*curhpb,20*skilllevela);}", 336);
        funMap.put("with(Math){ return -min(0.15*curhpb,15*skilllevela)-3*skilllevela;}", 313);
        funMap.put("with(Math){ return -min(0.1*curhpb,10*skilllevela)-3*skilllevela;}", 73);
        funMap.put("with(Math){ return -min(0.25*curhpb,50*skilllevela);}", 311);
        funMap.put("with(Math){ return -0.1-(0.1+0.05*skilllevela)*(1-curmpb/maxmpb);}", 77);

        // ========================================
        // 10.11 封印技能公式 (Seal Skill Formulas)
        // ========================================

        funMap.put("with(Math){ return (sealhita>=unsealb)?(0.98-0.38*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)*(0.3+0.05*skilllevela)):(0.6*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)*(0.3+0.05*skilllevela));}", 109);
        funMap.put("with(Math){ return (((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16)*(0.3+0.05*skilllevela)*0.5;}", 60);
        funMap.put("with(Math){ return (((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16)*(0.3+0.05*skilllevela)*0.5/(1-(((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16)*(0.3+0.05*skilllevela)*0.5);}", 61);

        // ========================================
        // 技能公式类整理完成！(Skill Formulas Reorganization Complete!)
        // ========================================
        /*
         * ✅ 所有技能相关公式已统一整理到第10节
         *
         * 📊 包含的类别：
         * - 10.1 基础技能等级公式：简单倍数、小数倍数、负数倍数
         * - 10.2 技能等级加法公式：基础值+技能等级
         * - 10.3 技能等级阶段计算：基础阶段、复杂阶段
         * - 10.4 技能等级条件判断：>=2 到 >=90
         * - 10.5 技能等级组合公式：与等级差、封印增强等组合
         * - 10.6 医疗治疗技能：基础医疗、复杂医疗
         * - 10.7 物理攻击技能：基础攻击、防御修正、速度修正、倍数修正等
         * - 10.8 魔法攻击技能：基础攻击、瞄准修正、Buff修正、生命值修正等
         * - 10.9 防御技能公式：防御力计算
         * - 10.10 生命值魔法值技能：HP/MP相关计算
         * - 10.11 封印技能公式：复杂封印计算
         *
         * 🎯 排列规律：
         * - 按功能类型分组
         * - 每组内按复杂度从简单到复杂排序
         * - 相似公式按系数大小排序
         * - 条件判断按阈值大小排序
         *
         * 📍 统一位置：第10节 (行572-813)
         * 🔄 重复项清理：需要删除其他位置的重复公式
         *
         * 💡 核心变量：
         * - phyattacka：物理攻击力
         * - magicattacka：魔法攻击力
         * - skilllevela：技能等级
         * - maindamage：主要伤害
         * - defendb/magicdefb：防御力
         * - gradea/gradeb：等级
         *
         * 🎮 应用场景：
         * - 战斗系统伤害计算
         * - 技能效果计算
         * - 治疗恢复计算
         * - 封印成功率计算
         *
         * ⚠️ 注意：文件中其他位置还有重复的攻击伤害公式需要清理
         */

        // ========================================
        // 11. 经验值和金钱计算公式大类 (Experience and Money Calculation Formulas) - 完整统一整理
        // ========================================
        // 所有涉及经验值、金钱、等级相关的计算公式全部集中在此大类
        // 包含：角色等级、团队等级、标准经验、怪物经验、副本经验等所有公式

        // ========================================
        // 11.1 基础角色等级经验公式 (Basic Role Level Experience Formulas) - 按倍数排序
        // ========================================

        // 11.1.1 简单倍数经验 (Simple Multiplier Experience)
        funMap.put("with(Math){ return RoleLv*10;}", 388);
        funMap.put("with(Math){ return RoleLv*50;}", 409);
        funMap.put("with(Math){ return RoleLv*100;}", 519);
        funMap.put("with(Math){ return RoleLv*200;}", 498);
        funMap.put("with(Math){ return 100*RoleLv;}", 405);
        funMap.put("with(Math){ return 200*RoleLv;}", 385);
        funMap.put("with(Math){ return 250*RoleLv;}", 401);
        funMap.put("with(Math){ return 500*RoleLv;}", 411);
        funMap.put("with(Math){ return 9.5*RoleLv;}", 369);

        // 11.1.2 小数倍数经验 (Decimal Multiplier Experience)
        funMap.put("with(Math){ return 1.38*RoleLv*(random()*(1.02-0.98)+0.98);}", 462);
        funMap.put("with(Math){ return 2.22*RoleLv*(random()*(1.02-0.98)+0.98);}", 460);
        funMap.put("with(Math){ return 2.5*RoleLv*(random()*(1.02-0.98)+0.98);}", 461);
        funMap.put("with(Math){ return 3.4*RoleLv*(random()*(1.02-0.98)+0.98);}", 459);

        // 11.1.3 加法公式经验 (Addition Formula Experience)
        funMap.put("with(Math){ return RoleLv*0.71+40;}", 442);
        funMap.put("with(Math){ return RoleLv*1.7+20;}", 458);
        funMap.put("with(Math){ return RoleLv*1.71+40;}", 456);
        funMap.put("with(Math){ return RoleLv*71.7+200;}", 393);
        funMap.put("with(Math){ return RoleLv*71.7+500;}", 377);
        funMap.put("with(Math){ return RoleLv*100*30/5;}", 586);

        // 11.1.4 随机倍数经验 (Random Multiplier Experience)
        funMap.put("with(Math){ return (15+random()*5)*RoleLv;}", 415);
        funMap.put("with(Math){ return (105+random()*30)*RoleLv;}", 541);
        funMap.put("with(Math){ return (175+random()*50)*RoleLv;}", 475);

        // ========================================
        // 11.2 复杂角色等级经验公式 (Complex Role Level Experience Formulas)
        // ========================================

        // 11.2.1 基础复杂计算 (Basic Complex Calculations)
        funMap.put("with(Math){ return (1*RoleLv*1)*(random()*(1.02-0.98)+0.98);}", 444);
        funMap.put("with(Math){ return (1*RoleLv*1.3)*(random()*(1.02-0.98)+0.98);}", 453);
        funMap.put("with(Math){ return (1*RoleLv*2)*(random()*(1.02-0.98)+0.98);}", 449);
        funMap.put("with(Math){ return (1*RoleLv*4)*(random()*(1.02-0.98)+0.98);}", 431);
        funMap.put("with(Math){ return (1*RoleLv*5)*(random()*(1.02-0.98)+0.98);}", 424);
        funMap.put("with(Math){ return (1*RoleLv*6.66)*(random()*(1.02-0.98)+0.98);}", 425);
        funMap.put("with(Math){ return (1*RoleLv*8)*(random()*(1.02-0.98)+0.98);}", 451);
        funMap.put("with(Math){ return (1*RoleLv*11)*(random()*(1.02-0.98)+0.98);}", 450);
        funMap.put("with(Math){ return (1*RoleLv*0.94)*(random()*(1.02-0.98)+0.98);}", 423);
        funMap.put("with(Math){ return (1*RoleLv*1.333)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 454);

        // 11.2.2 PVP相关经验 (PVP Related Experience)
        funMap.put("with(Math){ return (1*RoleLv*0.775*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 427);
        funMap.put("with(Math){ return (1*RoleLv*0.775*2*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 429);
        funMap.put("with(Math){ return (1*RoleLv*0.775*2)*(random()*(1.02-0.98)+0.98);}", 421);
        funMap.put("with(Math){ return (1*RoleLv*0.717*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 436);
        funMap.put("with(Math){ return (1*RoleLv*0.717*2*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 434);
        funMap.put("with(Math){ return (1*RoleLv*0.717*2*2)*(random()*(1.02-0.98)+0.98);}", 420);
        funMap.put("with(Math){ return (1*RoleLv*1.256*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 435);
        funMap.put("with(Math){ return (1*RoleLv*1.256*2*2)*(random()*(1.02-0.98)+0.98);}", 446);
        funMap.put("with(Math){ return (1*RoleLv*0.775*2*2)*(random()*(1.02-0.98)+0.98);}", 448);

        // ========================================
        // 11.3 400倍数角色等级经验公式 (400x Role Level Experience Formulas)
        // ========================================

        // 11.3.1 基础400倍数 (Basic 400x Multiplier)
        funMap.put("with(Math){ return (400*RoleLv*0.017)*(random()*(1.02-0.98)+0.98);}", 535);
        funMap.put("with(Math){ return (400*RoleLv*0.25*(0.79+0.02*AnswerCnt))*(random()*(1.02-0.98)+0.98);}", 522);
        funMap.put("with(Math){ return (400*RoleLv*0.25*(0.79+0.02*AnswerCnt))*0.5*(random()*(1.02-0.98)+0.98);}", 494);
        funMap.put("with(Math){ return (400*RoleLv*0.25*(0.89+0.02*AnswerCnt))*(random()*(1.02-0.98)+0.98);}", 497);
        funMap.put("with(Math){ return (400*RoleLv*0.25*(0.89+0.02*AnswerCnt))*0.5*(random()*(1.02-0.98)+0.98);}", 493);
        funMap.put("with(Math){ return (400*RoleLv*0.278*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 464);
        funMap.put("with(Math){ return (400*RoleLv*0.5)*(random()*(1.02-0.98)+0.98);}", 471);
        funMap.put("with(Math){ return (400*RoleLv*0.5*(0.79+0.02*AnswerCnt))*0.5*(random()*(1.02-0.98)+0.98);}", 485);
        funMap.put("with(Math){ return (400*RoleLv*0.628*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 509);
        funMap.put("with(Math){ return (400*RoleLv*0.628*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1))*0.5)*(random()*(1.02-0.98)+0.98);}", 493);
        funMap.put("with(Math){ return (400*RoleLv*0.628*2)*(random()*(1.02-0.98)+0.98);}", 507);
        funMap.put("with(Math){ return (400*RoleLv*0.667)*(random()*(1.02-0.98)+0.98)*0.5;}", 512);
        funMap.put("with(Math){ return (400*RoleLv*0.678*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 505);
        funMap.put("with(Math){ return (400*RoleLv*0.678*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1))*0.5)*(random()*(1.02-0.98)+0.98);}", 468);
        funMap.put("with(Math){ return (400*RoleLv*0.678*2)*(random()*(1.02-0.98)+0.98);}", 466);
        funMap.put("with(Math){ return (400*RoleLv*0.694*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 495);
        funMap.put("with(Math){ return (400*RoleLv*0.7)*(random()*(1.02-0.98)+0.98);}", 473);
        funMap.put("with(Math){ return (400*RoleLv*0.833)*(random()*(1.02-0.98)+0.98);}", 521);
        funMap.put("with(Math){ return (400*RoleLv*1)*(random()*(1.02-0.98)+0.98);}", 523);
        funMap.put("with(Math){ return (400*RoleLv*1)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 489);
        funMap.put("with(Math){ return (400*RoleLv*1.099*0.6);}", 533);
        funMap.put("with(Math){ return (400*RoleLv*1.099*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 476);
        funMap.put("with(Math){ return (400*RoleLv*1.099*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1))*0.5)*(random()*(1.02-0.98)+0.98);}", 472);
        funMap.put("with(Math){ return (400*RoleLv*1.099*3)*(random()*(1.02-0.98)+0.98);}", 486);
        funMap.put("with(Math){ return (400*RoleLv*1.2)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 540);
        funMap.put("with(Math){ return (400*RoleLv*1.25)*(random()*(1.02-0.98)+0.98);}", 525);
        funMap.put("with(Math){ return (400*RoleLv*1.4)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 531);
        funMap.put("with(Math){ return (400*RoleLv*1.6)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 516);
        funMap.put("with(Math){ return (400*RoleLv*1.8)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 483);
        funMap.put("with(Math){ return (400*RoleLv*2.222)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 549);
        funMap.put("with(Math){ return (400*RoleLv*2.5)*(random()*(1.02-0.98)+0.98);}", 517);

        // 11.3.2 时间相关400倍数 (Time Related 400x)
        funMap.put("with(Math){ return 400*RoleLv*1*(0.74+0.02*Time)*(random()*(1.02-0.98)+0.98);}", 499);
        funMap.put("with(Math){ return 1600*RoleLv*1*(0.74+0.02*Time)*(random()*(1.02-0.98)+0.98);}", 547);
        funMap.put("with(Math){ return 3200*RoleLv*1*(0.74+0.02*Time)*(random()*(1.02-0.98)+0.98);}", 537);
        funMap.put("with(Math){ return 1600*RoleLv*0.067*(0.74+0.02*Time)*(random()*(1.02-0.98)+0.98);}", 395);
        funMap.put("with(Math){ return 1000*RoleLv*0.067*(0.74+0.02*Time)*(random()*(1.02-0.98)+0.98);}", 368);

        // 11.3.3 Ring相关400倍数 (Ring Related 400x)
        funMap.put("with(Math){ return 400*RoleLv*0.875*(0.91+0.02*((Ring-1)%8+1))*(0.58+0.04*(floor((Ring-1)/8)+1));}", 469);
        funMap.put("with(Math){ return 400*RoleLv*0.656*(0.82+0.04*((Ring-1)%8+1))*(0.58+0.04*(floor((Ring-1)/8)+1));}", 524);
        funMap.put("with(Math){ return (1000*RoleLv*0.1*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 380);
        funMap.put("with(Math){ return (1000*RoleLv*0.2*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 374);

        // ========================================
        // 11.4 1000倍数角色等级经验公式 (1000x Role Level Experience Formulas)
        // ========================================

        funMap.put("with(Math){ return (1000*RoleLv*0.05*(0.79+0.02*AnswerCnt))*(random()*(1.02-0.98)+0.98);}", 414);
        funMap.put("with(Math){ return (1000*RoleLv*0.1)*(random()*(1.02-0.98)+0.98)*0.5;}", 410);
        funMap.put("with(Math){ return (1000*RoleLv*0.1*(0.79+0.02*AnswerCnt))*(random()*(1.02-0.98)+0.98);}", 398);
        funMap.put("with(Math){ return (1000*RoleLv*0.1*(0.79+0.02*AnswerCnt))*0.5*(random()*(1.02-0.98)+0.98);}", 406);
        funMap.put("with(Math){ return (1000*RoleLv*0.1*(0.79+0.02*AnswerCnt))*0.5*(random()*(1.02-0.98)+0.98);}", 419);
        funMap.put("with(Math){ return (1000*RoleLv*0.1*(0.89+0.02*AnswerCnt))*(random()*(1.02-0.98)+0.98);}", 384);
        funMap.put("with(Math){ return (1000*RoleLv*0.1*(0.89+0.02*AnswerCnt))*0.5*(random()*(1.02-0.98)+0.98);}", 419);
        funMap.put("with(Math){ return (1000*RoleLv*0.2)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 360);
        funMap.put("with(Math){ return (1000*RoleLv*0.215*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 355);
        funMap.put("with(Math){ return (1000*RoleLv*0.215*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1))*0.5)*(random()*(1.02-0.98)+0.98);}", 361);
        funMap.put("with(Math){ return (1000*RoleLv*0.232*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 351);
        funMap.put("with(Math){ return (1000*RoleLv*0.232*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1))*0.5)*(random()*(1.02-0.98)+0.98);}", 356);
        funMap.put("with(Math){ return (1000*RoleLv*0.232*2)*(random()*(1.02-0.98)+0.98);}", 367);
        funMap.put("with(Math){ return (1000*RoleLv*0.3)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 375);
        funMap.put("with(Math){ return (1000*RoleLv*0.377*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1))*0.5)*(random()*(1.02-0.98)+0.98);}", 390);
        funMap.put("with(Math){ return (1000*RoleLv*0.377*3)*(random()*(1.02-0.98)+0.98);}", 362);
        funMap.put("with(Math){ return (1000*RoleLv*0.4)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 403);
        funMap.put("with(Math){ return (1000*RoleLv*0.5)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 371);

        // ========================================
        // 11.5 5000倍数角色等级经验公式 (5000x Role Level Experience Formulas)
        // ========================================

        funMap.put("with(Math){ return (5000*RoleLv*0.05)*(random()*(1.02-0.98)+0.98)*0.5;}", 591);
        funMap.put("with(Math){ return (5000*RoleLv*0.05)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 574);
        funMap.put("with(Math){ return (5000*RoleLv*0.05*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 569);
        funMap.put("with(Math){ return (5000*RoleLv*0.075)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 580);
        funMap.put("with(Math){ return (5000*RoleLv*0.1)*(random()*(1.02-0.98)+0.98);}", 584);
        funMap.put("with(Math){ return (5000*RoleLv*0.1)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 572);
        funMap.put("with(Math){ return (5000*RoleLv*0.111)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 573);
        funMap.put("with(Math){ return (5000*RoleLv*0.15)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 587);
        funMap.put("with(Math){ return (500*RoleLv*0.05)*(random()*(1.02-0.98)+0.98)*0.5;}", 594);

        // ========================================
        // 11.6 防御相关公式 (Defense Related Formulas)
        // ========================================

        funMap.put("with(Math){ return -((0.15+0.001*skilllevela)*defendb+1*skilllevela);}", 304);

        // ========================================
        // 11. 更多伤害计算公式 - 已移至第10节攻击伤害技能公式大类统一管理
        // ========================================

        // ========================================
        // 12. 更多基础属性计算公式 (More Basic Attribute Formulas)
        // ========================================

        // 12.1 等级相关 (Level Related)
        funMap.put("with(Math){ return gradea*0.525;}", 224);
        funMap.put("with(Math){ return gradea*0.75;}", 217);
        funMap.put("with(Math){ return gradea*0.3;}", 221);

        // 12.2 技能等级相关属性 - 已移至第10节攻击伤害技能公式大类统一管理
        // ========================================

        // 12.3 品质相关属性 (Quality Related Attributes)
        funMap.put("with(Math){ return quality*5+50;}", 20);
        funMap.put("with(Math){ return 100*(havebuffa(508042)?(1.2):(1));}", 12);

        // ========================================
        // 公式整理阶段性成果总结！(Formula Reorganization Progress Summary!)
        // ========================================
        /*
         * ✅ 已完成的大类整理：
         *
         * 📊 第8节 - 系列状态检查公式大类：
         * - 94、95、96、97、99系列状态检查公式
         * - 按系列分组，每个系列内按ID排序
         * - 包含所有条件类型：>=, ==, <, &&, ||
         *
         * 📊 第9节 - Buff状态检查公式大类：
         * - 13、110-120、500、506、508、509、510系列Buff状态检查
         * - 包含正向检查、反向检查、复合条件
         *
         * 📊 第10节 - 攻击伤害技能公式大类：
         * - 物理攻击、魔法攻击、伤害计算、技能等级相关
         * - 医疗治疗、防御技能、生命值魔法值、封印技能
         * - 按攻击类型和复杂度排序
         *
         * 📊 第11节 - 经验值和金钱计算公式大类：
         * - 角色等级、团队等级、标准经验、怪物经验、副本经验
         * - 复杂经验计算、金钱计算
         * - 按倍数和复杂度排序
         *
         * 📊 第12节 - 属性计算公式大类（部分）：
         * - 等级属性、品质属性（部分完成）
         * - 需要继续整理生命值、魔法值等其他属性
         *
         * 🔄 重复项清理：
         * - 已删除大部分重复的攻击伤害公式
         * - 已删除部分重复的经验值公式
         * - 还有一些分散的公式需要继续整理
         *
         * 📍 下一步工作：
         * - 继续整理属性计算公式（生命值、魔法值、速度等）
         * - 整理其他类型公式（随机数、条件判断等）
         * - 清理剩余的重复项
         */

        // ========================================
        // 13. 更多生命值相关公式 (More HP Related Formulas)
        // ========================================

        funMap.put("with(Math){ return min(maxhpb*0.15,gradeb*12);}", 247);
        funMap.put("with(Math){ return min(maxhpb*0.25,gradeb*12);}", 246);

        // ========================================
        // 14. 更多封印相关公式 (More Seal Related Formulas)
        // ========================================

        funMap.put("with(Math){ return ((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))*0.4;}", 132);
        funMap.put("with(Math){ return 0.5+0.001*(skilllevela-gradeb)+(enhanceseala-resistsealb);}", 326);

        // ========================================
        // 15. 更多经验和金钱计算公式 (More Experience and Money Formulas)
        // ========================================

        // 15.1 角色等级相关经验 (Role Level Experience)
        funMap.put("with(Math){ return (1*RoleLv*1.256*2*2)*(random()*(1.02-0.98)+0.98);}", 446);
        funMap.put("with(Math){ return (1*RoleLv*1.256*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 435);
        funMap.put("with(Math){ return (1*RoleLv*0.94)*(random()*(1.02-0.98)+0.98);}", 423);
        funMap.put("with(Math){ return (1*RoleLv*0.775*2)*(random()*(1.02-0.98)+0.98);}", 421);
        funMap.put("with(Math){ return (1*RoleLv*0.775*2*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 429);
        funMap.put("with(Math){ return (400*RoleLv*2.5)*(random()*(1.02-0.98)+0.98);}", 517);
        funMap.put("with(Math){ return (400*RoleLv*0.833)*(random()*(1.02-0.98)+0.98);}", 521);
        funMap.put("with(Math){ return (400*RoleLv*0.694*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 495);
        funMap.put("with(Math){ return RoleLv*1.71+40;}", 456);

        // 15.2 标准经验计算 (Standard Experience Calculations)
        funMap.put("with(Math){ return StdExp*7*2.86/168*8;}", 536);
        funMap.put("with(Math){ return StdExp*5/10*(random()*(1.2-0.8)+0.8);}", 511);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*6.7);}", 548);

        // 15.3 团队相关 (Team Related)
        funMap.put("with(Math){ return (1000*TeamLv*0.075*(1-IsDbPoint)+1000*TeamLv*0.12*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 386);

        // 15.4 怪物等级相关 (Monster Level Related)
        funMap.put("with(Math){ return (400*MonsterLv*0.011*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*(1-IsDbPoint)+400*MonsterLv*0.13*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*IsDbPoint)*(random()*(1.02-0.98)+0.98);}", 528);
        funMap.put("with(Math){ return (5000*MonsterLv*0.05)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 577);

        // 15.5 时间相关 (Time Related)
        funMap.put("with(Math){ return 3200*RoleLv*1*(0.74+0.02*Time)*(random()*(1.02-0.98)+0.98);}", 537);

        // 15.6 复本相关 (Instance Related)
        funMap.put("with(Math){ return 400*min(RoleLv,94)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 508);

        // 15.7 环数相关 (Ring Related)
        funMap.put("with(Math){ return Ring+1;}", 554);

        // 15.8 概率计算 (Probability Calculations)
        funMap.put("with(Math){ return 0*TeamNum+2;}", 949);

        // ========================================
        // 16. 更多状态检查公式 (More Status Check Formulas)
        // ========================================

        // 16.1-16.4 系列状态检查 (Series Status Checks) - 已移至第8节统一管理

        // 16.3-16.4 系列状态检查 - 已移至第8节统一管理

        // 继续添加更多状态检查 - 系列状态检查已移至第8节统一管理
        funMap.put("with(Math){ return effectpointa>=4;}", 641);

        // ========================================
        // 17. 更多基础属性计算公式 (More Basic Attribute Formulas)
        // ========================================

        // 17.1 品质相关 (Quality Related)
        funMap.put("with(Math){ return quality;}", 5);
        funMap.put("with(Math){ return 200*(havebuffa(508042)?(1.2):(1));}", 15);

        // 17.2 等级相关 (Level Related)
        funMap.put("with(Math){ return gradea*1.1;}", 348);
        funMap.put("with(Math){ return gradea*0.0014;}", 199);
        funMap.put("with(Math){ return gradea*0.001225;}", 198);
        funMap.put("with(Math){ return gradea*0.3375;}", 208);
        funMap.put("with(Math){ return gradea*0.1;}", 182);
        funMap.put("with(Math){ return gradea*1.225;}", 211);

        // 17.3 技能等级相关 (Skill Level Related)
        funMap.put("with(Math){ return 5*skilllevela;}", 261);
        funMap.put("with(Math){ return 10*skilllevela;}", 46);

        // 17.4 技能等级阶段计算 (Skill Level Stage Calculations)
        funMap.put("with(Math){ return 2+min(floor(skilllevela/60),1);}", 145);
        funMap.put("with(Math){ return 4+min(floor(skilllevela/60),1);}", 302);

        // 17.5 基础数值计算 (Basic Value Calculations)
        funMap.put("with(Math){ return 10+1.2*gradea;}", 252);
        funMap.put("with(Math){ return 10+1.2*skilllevela;}", 24);

        // ========================================
        // 18. 更多生命值相关公式 (More HP Related Formulas)
        // ========================================

        funMap.put("with(Math){ return maxhpb;}", 128);
        funMap.put("with(Math){ return 0.35*maxhpb;}", 236);
        funMap.put("with(Math){ return -maxhpb*2.5;}", 126);
        funMap.put("with(Math){ return -curhpa*1;}", 119);
        funMap.put("with(Math){ return min(maxhpb*0.50,gradeb*30);}", 244);

        // ========================================
        // 19. 更多治疗和医疗公式 (More Healing and Medical Formulas)
        // ========================================

        funMap.put("with(Math){ return ((medicala+3*skilllevela)*0.5+abs(maindamage)*0.5)*(1+healrevisea)*(1+medicaljiashena/1000);}", 314);
        funMap.put("with(Math){ return ((medicala*0.5+skilllevela*1.4)+abs(maindamage)*0.1)*(1+healrevisea)*(1+medicaljiashena/1000);}", 74);
        funMap.put("with(Math){ return medicala+1*skilllevela;}", 330);
        funMap.put("with(Math){ return medicala+3*skilllevela;}", 84);

        // ========================================
        // 20. 更多伤害计算公式 - 已移至第10节攻击伤害技能公式大类统一管理
        // ========================================

        // 20.4 防御相关 (Defense Related)
        funMap.put("with(Math){ return -((0.15+0.001*skilllevela)*magicdefb+1*skilllevela);}", 303);

        // ========================================
        // 21. 更多封印相关公式 (More Seal Related Formulas)
        // ========================================

        funMap.put("with(Math){ return (sealhita>=unsealb)?(0.98-0.38*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)*(0.3+0.05*skilllevela)):(0.6*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)*(0.3+0.05*skilllevela));}", 109);
        funMap.put("with(Math){ return ((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))+0.05+(1-curhpa/maxhpa)*0.1;}", 324);

        // ========================================
        // 22. 更多经验和金钱计算公式 (More Experience and Money Formulas)
        // ========================================

        // 22.1 角色等级相关经验 (Role Level Experience)
        funMap.put("with(Math){ return 1.38*RoleLv*(random()*(1.02-0.98)+0.98);}", 462);
        funMap.put("with(Math){ return (1*RoleLv*5)*(random()*(1.02-0.98)+0.98);}", 424);
        funMap.put("with(Math){ return (1*RoleLv*4)*(random()*(1.02-0.98)+0.98);}", 431);
        funMap.put("with(Math){ return (400*RoleLv*1.6)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 516);
        funMap.put("with(Math){ return (400*RoleLv*0.628*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1))*0.5)*(random()*(1.02-0.98)+0.98);}", 493);
        funMap.put("with(Math){ return (2000*RoleLv*0.694*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 507);
        funMap.put("with(Math){ return (5000*RoleLv*0.111)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 573);
        funMap.put("with(Math){ return (1000*RoleLv*0.232*2)*(random()*(1.02-0.98)+0.98);}", 367);
        funMap.put("with(Math){ return 250*RoleLv*(random()*(1.02-0.98)+0.98);}", 401);
        funMap.put("with(Math){ return (400*RoleLv*1.25)*(random()*(1.02-0.98)+0.98);}", 525);
        funMap.put("with(Math){ return 500*RoleLv;}", 411);
        funMap.put("with(Math){ return (15+random()*5)*RoleLv;}", 415);

        // 22.2 标准经验计算 (Standard Experience Calculations)
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*6.6667/8*((Ring-1)*0.3+1));}", 504);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*6.7/10)*((TeamNum-1)*0.05+1);}", 542);

        // 22.3 金钱计算 (Money Calculations)
        funMap.put("with(Math){ return (StdMoney*4.2/8*((Ring-1)*0.3+1))*(random()*(1.05-0.95)+0.95);}", 357);

        // 22.4 团队相关 (Team Related)
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*150;}", 365);
        funMap.put("with(Math){ return (1000*TeamLv*0.038*(0.78+0.04*Ring)*(1-IsDbPoint)+1000*TeamLv*0.098*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 396);

        // 22.5 怪物等级相关 (Monster Level Related)
        funMap.put("with(Math){ return (1000*MonsterLv*0.019*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*IsDbPoint)*(random()*(1.02-0.98)+0.98);}", 363);

        // 22.6 时间相关 (Time Related)
        funMap.put("with(Math){ return 1600*RoleLv*1*(0.74+0.02*Time)*(random()*(1.02-0.98)+0.98);}", 547);

        // 22.7 环数相关 (Ring Related)
        funMap.put("with(Math){ return Ring+3;}", 553);

        // 22.8 答题相关 (Answer Related)
        funMap.put("with(Math){ return (400*RoleLv*0.25*(0.79+0.02*AnswerCnt))*0.5*(random()*(1.02-0.98)+0.98);}", 494);

        // ========================================
        // 23. 更多Buff状态检查公式 - 已移至第9节统一管理
        // ========================================

        // ========================================
        // 24. 更多治疗和医疗公式 (Additional Healing and Medical Formulas)
        // ========================================

        funMap.put("with(Math){ return medicala*2+skilllevela*10;}", 103);
        funMap.put("with(Math){ return ((medicala*0.2+skilllevela*1.2)+abs(maindamage)*0.08)*(1+healrevisea)*(1+medicaljiashena/1000);}", 76);
        funMap.put("with(Math){ return medicala+3*skilllevela*2;}", 87);

        // ========================================
        // 25. 更多生命值和魔法值相关公式 (Additional HP/MP Related Formulas)
        // ========================================

        funMap.put("with(Math){ return -maxhpb*0.7;}", 142);
        funMap.put("with(Math){ return -maxhpb*0.4;}", 140);
        funMap.put("with(Math){ return -curhpb*0.1;}", 121);
        funMap.put("with(Math){ return -min(0.1*curhpb,20*skilllevela);}", 336);
        funMap.put("with(Math){ return -min(0.05*curmpb,10*skilllevela);}", 337);
        funMap.put("with(Math){ return maxhpa*0.22;}", 284);

        // ========================================
        // 26. 更多基础属性计算公式 (Additional Basic Attribute Formulas)
        // ========================================

        // 26.1 等级相关 (Level Related)
        funMap.put("with(Math){ return gradea*0.2362;}", 206);
        funMap.put("with(Math){ return gradea*2.3;}", 349);
        funMap.put("with(Math){ return gradea*0.7;}", 190);
        funMap.put("with(Math){ return gradea*1.5;}", 251);
        funMap.put("with(Math){ return gradea*0.12;}", 202);

        // 26.2 技能等级相关 (Skill Level Related)
        funMap.put("with(Math){ return 0.5*skilllevela;}", 45);
        funMap.put("with(Math){ return 14*skilllevela;}", 101);

        // 26.3 技能等级阶段计算 (Skill Level Stage Calculations)
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),2);}", 272);

        // 26.4 团队等级 (Team Level)
        funMap.put("with(Math){ return TeamLv;}", 942);

        // ========================================
        // 27. 更多伤害计算公式 - 已移至第10节攻击伤害技能公式大类统一管理
        // ========================================

        // ========================================
        // 28. 更多概率和随机数公式 (Additional Probability and Random Formulas)
        // ========================================

        funMap.put("with(Math){ return randint(1,2);}", 98);
        funMap.put("with(Math){ return 0*TeamNum+8+2*random();}", 948);
        funMap.put("with(Math){ return 4000+6000*random();}", 402);

        // ========================================
        // 29. 更多经验和金钱计算公式 (Additional Experience and Money Formulas)
        // ========================================

        // 29.1 角色等级相关经验 (Role Level Experience)
        funMap.put("with(Math){ return 100*RoleLv;}", 405);
        funMap.put("with(Math){ return 400*RoleLv*1.215*(0.88+0.03);}", 527);
        funMap.put("with(Math){ return (400*RoleLv*1.099*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1))*0.5)*(random()*(1.02-0.98)+0.98);}", 472);
        funMap.put("with(Math){ return (1*RoleLv*0.775*2*2)*(random()*(1.02-0.98)+0.98);}", 448);
        funMap.put("with(Math){ return (1*RoleLv*1)*(random()*(1.02-0.98)+0.98);}", 444);
        funMap.put("with(Math){ return (1000*RoleLv*0.377*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1))*0.5)*(random()*(1.02-0.98)+0.98);}", 390);
        funMap.put("with(Math){ return (400*RoleLv*1)*(random()*(1.02-0.98)+0.98);}", 523);
        funMap.put("with(Math){ return RoleLv*1.7+20;}", 458);
        funMap.put("with(Math){ return (400*RoleLv*0.7)*(random()*(1.02-0.98)+0.98);}", 473);
        funMap.put("with(Math){ return (5000*RoleLv*0.05)*(random()*(1.02-0.98)+0.98)*0.5;}", 591);
        funMap.put("with(Math){ return (1*RoleLv*2)*(random()*(1.02-0.98)+0.98);}", 449);
        funMap.put("with(Math){ return (5000*RoleLv*0.15)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 587);

        // 29.2 时间相关 (Time Related)
        funMap.put("with(Math){ return 1000*RoleLv*0.067*(0.74+0.02*Time)*(random()*(1.02-0.98)+0.98);}", 368);

        // 29.3 复本相关 (Instance Related)
        funMap.put("with(Math){ return (5000*min(max(RoleLv,FuBenLv),FuBenLv+9)*0.2*(0.7+0.1*Ring))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 578);
        funMap.put("with(Math){ return (1*min(max(RoleLv,FuBenLv),FuBenLv+9)*5)*(random()*(1.02-0.98)+0.98);}", 426);

        // 29.4 团队相关 (Team Related)
        funMap.put("with(Math){ return (50)+(TeamNum-1)*50+RoleLv*5;}", 440);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*120;}", 382);
        funMap.put("with(Math){ return (5000*TeamLv*0.025*(0.78+0.04*Ring)*(1-IsDbPoint)+5000*TeamLv*0.102*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 575);
        funMap.put("with(Math){ return (400*TeamLv*0.15*(0.78+0.04*Ring)*(1-IsDbPoint)+400*TeamLv*1.02*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 467);

        // 29.5 怪物等级相关 (Monster Level Related)
        funMap.put("with(Math){ return (5000*MonsterLv*0.011*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*(1-IsDbPoint)+5000*MonsterLv*0.02*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*IsDbPoint)*(random()*(1.02-0.98)+0.98);}", 588);

        // 29.6 答题相关 (Answer Related)
        funMap.put("with(Math){ return (1000*RoleLv*0.1*(0.89+0.02*AnswerCnt))*0.5*(random()*(1.02-0.98)+0.98);}", 419);

        // ========================================
        // 30. 更多状态检查公式 (Additional Status Check Formulas)
        // ========================================

        // 30.1-30.5 系列状态检查 - 已移至第8节统一管理

        // 30.6 效果点数判断 (Effect Point Conditions)
        funMap.put("with(Math){ return effectpointa>=2;}", 67);

        // ========================================
        // 31. 更多Buff状态检查公式 (Additional Buff Status Check Formulas)
        // ========================================

        // Buff状态检查已移至第9节统一管理

        // ========================================
        // 32. 更多基础属性计算公式 (Additional Basic Attribute Formulas)
        // ========================================

        // 32.1 等级相关 (Level Related)
        funMap.put("with(Math){ return gradea*0.5;}", 213);
        funMap.put("with(Math){ return gradea*1.3;}", 295);
        funMap.put("with(Math){ return gradea*20;}", 159);
        funMap.put("with(Math){ return gradea*0.14;}", 204);
        funMap.put("with(Math){ return gradea*0.0875;}", 218);
        funMap.put("with(Math){ return gradea*3.5;}", 228);
        funMap.put("with(Math){ return gradea*1;}", 201);

        // 32.2 技能等级相关 (Skill Level Related)
        funMap.put("with(Math){ return -8*skilllevela;}", 327);
        funMap.put("with(Math){ return skilllevela>=90;}", 69);

        // 32.3 技能等级阶段计算 (Skill Level Stage Calculations)
        funMap.put("with(Math){ return 4+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),1);}", 106);
        funMap.put("with(Math){ return round((1-pow(0.98,skilllevela))*1000);}", 265);

        // 32.4 基础数值计算 (Basic Value Calculations)
        funMap.put("with(Math){ return min(-1*gradea+0.5*skilllevela,0);}", 57);
        funMap.put("with(Math){ return phyattacka*0.1;}", 254);

        // 32.5 品质相关 (Quality Related)
        funMap.put("with(Math){ return 400*(havebuffa(508042)?(1.2):(1));}", 7);
        funMap.put("with(Math){ return 150*(havebuffa(508042)?(1.2):(1));}", 16);

        // ========================================
        // 33. 更多生命值相关公式 (Additional HP Related Formulas)
        // ========================================

        funMap.put("with(Math){ return -maxhpb*0.2;}", 138);
        funMap.put("with(Math){ return -curhpb*0.5;}", 123);

        // ========================================
        // 34. 更多伤害相关公式 (Additional Damage Related Formulas)
        // ========================================

        funMap.put("with(Math){ return abs(maindamage)*0.35;}", 80);

        // ========================================
        // 35. 更多伤害计算公式 (Additional Damage Calculation Formulas)
        // ========================================

        // 35.1 物理攻击伤害 (Physical Attack Damage)
        funMap.put("with(Math){ return -(phyattacka*1.6-defendb+1*skilllevela);}", 253);
        funMap.put("with(Math){ return -(phyattacka*1.6-defendb+1*gradea);}", 232);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+3*gradea);}", 165);
        funMap.put("with(Math){ return -(phyattacka*0.55-defendb+1*gradea);}", 179);
        funMap.put("with(Math){ return -(phyattacka*0.45-defendb+1*skilllevela);}", 70);

        // 35.2 魔法攻击伤害 (Magic Attack Damage)
        funMap.put("with(Math){ return -(magicattacka*2.5-magicdefb+2*skilllevela)*0.5;}", 79);

        // ========================================
        // 36. 更多概率和随机数公式 (Additional Probability and Random Formulas)
        // ========================================

        funMap.put("with(Math){ return 0*TeamNum+6+2*random();}", 947);
        funMap.put("with(Math){ return 0.6+0.002*(skilllevela-gradeb)+(enhanceseala-resistsealb);}", 144);
        funMap.put("with(Math){ return 0.6+0.001*(skilllevela-gradeb);}", 310);

        // ========================================
        // 37. 更多封印相关公式 (Additional Seal Related Formulas)
        // ========================================

        funMap.put("with(Math){ return ((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))+0.05;}", 55);
        funMap.put("with(Math){ return ((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16;}", 341);

        // ========================================
        // 38. 更多经验和金钱计算公式 (Additional Experience and Money Formulas)
        // ========================================

        // 38.1 角色等级相关经验 (Role Level Experience)
        funMap.put("with(Math){ return RoleLv*10;}", 388);
        funMap.put("with(Math){ return (1*RoleLv*6.66)*(random()*(1.02-0.98)+0.98);}", 425);
        funMap.put("with(Math){ return (105+random()*30)*RoleLv;}", 541);
        funMap.put("with(Math){ return (400*RoleLv*2.222)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 549);
        funMap.put("with(Math){ return (1*RoleLv*0.775*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 427);
        funMap.put("with(Math){ return (5000*RoleLv*0.1)*(random()*(1.02-0.98)+0.98);}", 584);
        funMap.put("with(Math){ return RoleLv*100*30/5;}", 586);
        funMap.put("with(Math){ return (5000*RoleLv*0.05*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 569);

        // 38.2 时间相关 (Time Related)
        funMap.put("with(Math){ return 1600*RoleLv*0.067*(0.74+0.02*Time)*(random()*(1.02-0.98)+0.98);}", 395);
        funMap.put("with(Math){ return 400*RoleLv*1*(0.74+0.02*Time)*(random()*(1.02-0.98)+0.98);}", 499);

        // 38.3 环数相关 (Ring Related)
        funMap.put("with(Math){ return 5800+floor(RoleLv/10)*440+(200+floor(RoleLv/10)*40*(Ring-1));}", 563);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,94)/10)*440+(200+floor(min(RoleLv,94)/10)*40*10))*2;}", 558);
        funMap.put("with(Math){ return (400*RoleLv*0.278*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 464);

        // 38.4 团队相关 (Team Related)
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*5;}", 439);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*140;}", 359);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*3;}", 455);
        funMap.put("with(Math){ return (500*TeamLv*0.15*(0.78+0.04*Ring)*(1-IsDbPoint)+400*TeamLv*1.02*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 552);
        funMap.put("with(Math){ return (400*TeamLv*0.3*(1-IsDbPoint)+400*TeamLv*1.244*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 526);

        // 38.5 复本相关 (Instance Related)
        funMap.put("with(Math){ return (1000*min(max(RoleLv,FuBenLv),FuBenLv+9)*0.1*(0.7+0.1*Ring))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 354);
        funMap.put("with(Math){ return 1000*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.35*(0.5+0.1*Saveid)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 416);
        funMap.put("with(Math){ return 400*min(RoleLv,79)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 513);
        funMap.put("with(Math){ return (400*min(max(RoleLv,FuBenLv),FuBenLv+9)*2*(0.7+0.1*Ring))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 539);

        // 38.6 答题相关 (Answer Related)
        funMap.put("with(Math){ return (400*RoleLv*0.25*(0.79+0.02*AnswerCnt))*(random()*(1.02-0.98)+0.98);}", 522);
        funMap.put("with(Math){ return (1000*RoleLv*0.05*(0.79+0.02*AnswerCnt))*(random()*(1.02-0.98)+0.98);}", 414);

        // 38.7 标准经验计算 (Standard Experience Calculations)
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*6.7/15);}", 470);

        // 38.8 金钱计算 (Money Calculations)
        funMap.put("with(Math){ return (StdMoney*3.15/8*((Ring-1)*0.3+1))*(random()*(1.05-0.95)+0.95);}", 389);
        funMap.put("with(Math){ return (StdMoney*6/28.1*((Ring-1)*0.09+1))*(random()*(1.05-0.95)+0.95);}", 400);

        // ========================================
        // 39. 更多状态检查公式 (Additional Status Check Formulas)
        // ========================================

        // 39.1-39.4 系列状态检查 - 已移至第8节统一管理

        // 39.5 效果点数判断 (Effect Point Conditions)
        funMap.put("with(Math){ return effectpointa>=0;}", 639);
        funMap.put("with(Math){ return effectpointa<1;}", 659);

        // ========================================
        // 40. 更多Buff状态检查公式 (Additional Buff Status Check Formulas)
        // ========================================

        // Buff状态检查已移至第9节统一管理

        // ========================================
        // 41. 更多基础属性计算公式 (Additional Basic Attribute Formulas)
        // ========================================

        // 41.1 等级相关 (Level Related)
        funMap.put("with(Math){ return gradea*0.075;}", 216);
        funMap.put("with(Math){ return gradea*0.05;}", 212);
        funMap.put("with(Math){ return gradea*0.16;}", 181);
        funMap.put("with(Math){ return gradea*3;}", 226);
        funMap.put("with(Math){ return gradea*1.22;}", 193);
        funMap.put("with(Math){ return gradea*0.9;}", 158);
        funMap.put("with(Math){ return gradea*0.175;}", 188);

        // 41.2 技能等级相关 (Skill Level Related)
        funMap.put("with(Math){ return skilllevela>=3;}", 268);
        funMap.put("with(Math){ return skilllevela>=70;}", 29);

        // 41.3 品质相关 (Quality Related)
        funMap.put("with(Math){ return (quality*5+100)*(havebuffa(508042)?(1.2):(1));}", 11);
        funMap.put("with(Math){ return 300*(havebuffa(508042)?(1.2):(1));}", 17);
        funMap.put("with(Math){ return 250*(havebuffa(508042)?(1.2):(1));}", 18);

        // 41.4 PVE判断 (PVE Conditions)
        funMap.put("with(Math){ return pve;}", 14);

        // 41.5 生命值比例判断 (HP Ratio Conditions)
        funMap.put("with(Math){ return (curhpa/maxhpa)>=0.5;}", 31);

        // ========================================
        // 42. 更多生命值相关公式 (Additional HP Related Formulas)
        // ========================================

        funMap.put("with(Math){ return maxhpb*1;}", 114);
        funMap.put("with(Math){ return -curhpb*0.7;}", 125);

        // ========================================
        // 43. 更多伤害相关公式 (Additional Damage Related Formulas)
        // ========================================

        funMap.put("with(Math){ return -maindamage;}", 175);

        // ========================================
        // 44. 更多伤害计算公式 (Additional Damage Calculation Formulas)
        // ========================================

        // 44.1 物理攻击伤害 (Physical Attack Damage)
        funMap.put("with(Math){ return -(phyattacka*1.45-defendb+1*skilllevela)*2.5;}", 147);
        funMap.put("with(Math){ return -(phyattacka*2.5-defendb+1*gradea);}", 278);
        funMap.put("with(Math){ return -(phyattacka*0.45-defendb+1*gradea);}", 167);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea)*0.9;}", 297);
        funMap.put("with(Math){ return -(phyattacka*0.85-defendb+1*skilllevela);}", 48);
        funMap.put("with(Math){ return -(phyattacka*1.25-min(defendb,magicdefb)+1*skilllevela);}", 307);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea);}", 1);

        // 44.2 魔法攻击伤害 (Magic Attack Damage)
        funMap.put("with(Math){ return magicattacka*10;}", 289);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*0.1;}", 329);
        funMap.put("with(Math){ return -magicattacka*1.7;}", 287);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*(0.5+0.05*(3-preaimcount));}", 129);

        // ========================================
        // 45. 更多封印相关公式 (Additional Seal Related Formulas)
        // ========================================

        funMap.put("with(Math){ return ((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))*0.3;}", 133);
        funMap.put("with(Math){ return (((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16)*0.2;}", 346);

        // ========================================
        // 46. 更多经验和金钱计算公式 (Additional Experience and Money Formulas)
        // ========================================

        // 46.1 怪物等级相关 (Monster Level Related)
        funMap.put("with(Math){ return (MonsterLv-30)*0.2+4;}", 437);
        funMap.put("with(Math){ return (5000*MonsterLv*0.017*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*(1-IsDbPoint)+5000*MonsterLv*0.02*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*IsDbPoint)*(random()*(1.02-0.98)+0.98);}", 583);

        // 46.2 角色等级相关经验 (Role Level Experience)
        funMap.put("with(Math){ return (400*RoleLv*0.678*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1))*0.5)*(random()*(1.02-0.98)+0.98);}", 468);
        funMap.put("with(Math){ return (400*RoleLv*0.017)*(random()*(1.02-0.98)+0.98);}", 535);
        funMap.put("with(Math){ return (175+random()*50)*RoleLv;}", 475);
        funMap.put("with(Math){ return (400*RoleLv*0.667)*(random()*(1.02-0.98)+0.98)*0.5;}", 512);
        funMap.put("with(Math){ return 3.4*RoleLv*(random()*(1.02-0.98)+0.98);}", 459);
        funMap.put("with(Math){ return (1*RoleLv*0.717*2*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 434);

        // 46.3 复本相关 (Instance Related)
        funMap.put("with(Math){ return 2200+floor(min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)/10)*440+(200+floor(min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)/10)*40*Saveid);}", 560);
        funMap.put("with(Math){ return 400*min(RoleLv,89)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 474);
        funMap.put("with(Math){ return 400*min(RoleLv,74)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 500);

        // 46.4 团队相关 (Team Related)
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*100;}", 370);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*1;}", 445);
        funMap.put("with(Math){ return 1*TeamNum+3;}", 941);

        // 46.5 环数相关 (Ring Related)
        funMap.put("with(Math){ return (1000*RoleLv*0.1*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 380);

        // 46.6 答题相关 (Answer Related)
        funMap.put("with(Math){ return (400*RoleLv*0.25*(0.89+0.02*AnswerCnt))*(random()*(1.02-0.98)+0.98);}", 497);
        funMap.put("with(Math){ return (1000*RoleLv*0.1*(0.89+0.02*AnswerCnt))*(random()*(1.02-0.98)+0.98);}", 384);

        // 46.7 金钱计算 (Money Calculations)
        funMap.put("with(Math){ return (1+14*IsDbPoint+IsSerMul)*(StdMoney*1.5/74*(14*0.09+1))*(random()*(1.2-0.8)+0.8);}", 407);

        // ========================================
        // 47. 更多状态检查公式 (Additional Status Check Formulas)
        // ========================================

        // 47.1-47.5 系列状态检查 - 已移至第8节统一管理

        // ========================================
        // 48. 更多Buff状态检查公式 (Additional Buff Status Check Formulas)
        // ========================================

        // Buff状态检查已移至第9节统一管理

        // ========================================
        // 49. 最终批次 - 剩余公式整理 (Final Batch - Remaining Formulas)
        // ========================================

        // 49.1 更多治疗和医疗公式 (Additional Healing and Medical Formulas)
        funMap.put("with(Math){ return medicala+1.2*skilllevela;}", 95);
        funMap.put("with(Math){ return 100+1.2*skilllevela;}", 100);

        // 49.2 更多基础属性计算公式 (Additional Basic Attribute Formulas)
        funMap.put("with(Math){ return gradea*3.25;}", 227);
        funMap.put("with(Math){ return 3*gradea;}", 161);
        funMap.put("with(Math){ return gradea*0.189;}", 205);
        funMap.put("with(Math){ return gradea*0.85;}", 220);
        funMap.put("with(Math){ return skilllevela>=50;}", 63);

        // 49.3 更多品质相关 (Additional Quality Related)
        funMap.put("with(Math){ return quality*60+2000;}", 2);
        funMap.put("with(Math){ return quality*0.6+10;}", 3);
        funMap.put("with(Math){ return quality*3*(havebuffa(508042)?(1.2):(1));}", 10);
        funMap.put("with(Math){ return -quality*3;}", 13);

        // 49.4 更多生命值相关公式 (Additional HP Related Formulas)
        funMap.put("with(Math){ return (maxhpa*0.2)*0.4;}", 282);

        // 49.5 更多伤害相关公式 (Additional Damage Related Formulas)
        funMap.put("with(Math){ return abs(maindamage)*0.20;}", 180);

        // 49.6 更多伤害计算公式 (Additional Damage Calculation Formulas)
        funMap.put("with(Math){ return -(phyattacka*0.8-defendb+1*gradea);}", 249);
        funMap.put("with(Math){ return phyattacka*2-defendb+1*gradea;}", 279);
        funMap.put("with(Math){ return -(phyattacka*0.7-defendb+1*skilllevela);}", 110);
        funMap.put("with(Math){ return -(phyattacka*1.6-min(defendb,magicdefb)+1*skilllevela);}", 255);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea)*(0.45+0.05*skilllevela);}", 39);
        funMap.put("with(Math){ return -(phyattacka*0.95-defendb+1*skilllevela+max(0,speeda-speedb)*0.5);}", 319);
        funMap.put("with(Math){ return -(phyattacka*1.40-defendb+1*skilllevela)*2.5;}", 146);
        funMap.put("with(Math){ return -(phyattacka*1.05-min(defendb,magicdefb)+1*skilllevela);}", 305);
        funMap.put("with(Math){ return -(phyattacka*1-min(defendb,magicdefb)+1*skilllevela);}", 308);
        funMap.put("with(Math){ return -magicattacka*0.7;}", 262);

        // 49.7 更多封印相关公式 (Additional Seal Related Formulas)
        funMap.put("with(Math){ return (((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16)*0.4;}", 344);
        funMap.put("with(Math){ return (((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16)*0.6;}", 342);

        // 49.8 更多经验和金钱计算公式 (Additional Experience and Money Formulas)
        funMap.put("with(Math){ return RoleLv*200;}", 498);
        funMap.put("with(Math){ return (5000*RoleLv*0.1)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 572);
        funMap.put("with(Math){ return (1000*RoleLv*0.1*(0.79+0.02*AnswerCnt))*0.5*(random()*(1.02-0.98)+0.98);}", 406);
        funMap.put("with(Math){ return SwXs*1;}", 568);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,59)/10)*440+(200+floor(min(RoleLv,59)/10)*40*10))*2;}", 564);
        funMap.put("with(Math){ return (1000*RoleLv*0.232*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 351);
        funMap.put("with(Math){ return 2.5*RoleLv*(random()*(1.02-0.98)+0.98);}", 461);
        funMap.put("with(Math){ return 9.5*RoleLv;}", 369);
        funMap.put("with(Math){ return (400*RoleLv*1.099*0.6);}", 533);
        funMap.put("with(Math){ return (400*RoleLv*1.099*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 476);
        funMap.put("with(Math){ return 2200+floor(RoleLv/10)*440+(200+floor(RoleLv/10)*40*(Ring-1));}", 559);
        funMap.put("with(Math){ return (400*RoleLv*0.5*(0.79+0.02*AnswerCnt))*0.5*(random()*(1.02-0.98)+0.98);}", 485);
        funMap.put("with(Math){ return RoleLv*100;}", 519);
        funMap.put("with(Math){ return 400*RoleLv*0.875*(0.91+0.02*((Ring-1)%8+1))*(0.58+0.04*(floor((Ring-1)/8)+1));}", 469);
        funMap.put("with(Math){ return (1000*RoleLv*0.215*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 355);
        funMap.put("with(Math){ return (400*RoleLv*0.678*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 505);
        funMap.put("with(Math){ return (1000*RoleLv*0.2)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 360);

        // 49.9 标准经验和团队相关 (Standard Experience and Team Related)
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*2.14*7/8*((Ring-1)*0.3+1));}", 487);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+7*IsDbPoint+IsSerMul)*(StdExp*10/63*((Ring-1)*0.05+1));}", 550);
        funMap.put("with(Math){ return (1000*TeamLv*0.15*(0.78+0.04*Ring)*(1-IsDbPoint)+400*TeamLv*1.02*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 514);
        funMap.put("with(Math){ return (1+IsSerMul)*(StdExp*5);}", 543);
        funMap.put("with(Math){ return 1*TeamNum+5;}", 943);

        // 49.10 复本相关 (Instance Related)
        funMap.put("with(Math){ return (400*min(max(RoleLv,FuBenLv),FuBenLv+9)*1*(0.7+0.1*Ring))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 551);

        // 49.11 服务器相关 (Server Related)
        funMap.put("with(Math){ return min(max(rolenum*0.001,2),4)+(ServerLv-50)*0.2;}", 597);

        // 49.12 技能等级阶段计算 (Skill Level Stage Calculations)
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),3);}", 273);

        // 49.13 更多状态检查公式 (Additional Status Check Formulas)
        funMap.put("with(Math){ return _96016_<1&&_96018_<1;}", 733);
        funMap.put("with(Math){ return _95035_>=1;}", 676);
        funMap.put("with(Math){ return _95078_<1;}", 713);
        // 94系列已移至8.1统一管理
        funMap.put("with(Math){ return _97004_>=4;}", 783);
        funMap.put("with(Math){ return _95003_==9;}", 847);
        funMap.put("with(Math){ return _96354_<1;}", 777);
        funMap.put("with(Math){ return _96351_>=1&&_96366_<1;}", 776);
        funMap.put("with(Math){ return _95081_<1;}", 716);
        funMap.put("with(Math){ return _95018_<=1;}", 669);
        funMap.put("with(Math){ return _99060_<1;}", 875);
        funMap.put("with(Math){ return _95020_==1;}", 670);
        funMap.put("with(Math){ return _99043_>=1;}", 850);
        funMap.put("with(Math){ return _99059_<1;}", 874);
        funMap.put("with(Math){ return _96103_>=1;}", 736);
        // 94系列已移至8.1统一管理
        funMap.put("with(Math){ return _96001_/_96002_<0.1;}", 877);
        funMap.put("with(Math){ return _99001_<1&&_99012_>=1;}", 814);
        funMap.put("with(Math){ return _99001_<1&&_99004_==1;}", 806);
        funMap.put("with(Math){ return _96202_>=4;}", 787);
        funMap.put("with(Math){ return _96352_>=1;}", 766);
        // 94系列已移至8.1统一管理
        funMap.put("with(Math){ return _95037_>=2||_95040_>=1;}", 680);
        funMap.put("with(Math){ return _95003_==7;}", 845);

        // 49.14 更多Buff状态检查公式 (Additional Buff Status Check Formulas)
        // Buff状态检查已移至第9节统一管理

        // ========================================
        // 50. 整理完成总结 (Reorganization Complete Summary)
        // ========================================

        /*
         * InitFunMap 重新整理完成！
         *
         * 总计分类：
         * 1-10:   基础属性、伤害计算、生命值、治疗、概率、封印、经验金钱、状态检查、Buff检查、防御
         * 11-20:  更多伤害计算、基础属性、生命值、治疗、伤害、概率、封印、经验金钱、状态检查、Buff检查
         * 21-30:  更多治疗、生命值、基础属性、伤害、概率、封印、经验金钱、状态检查、Buff检查
         * 31-40:  更多基础属性、生命值、治疗、伤害、概率、封印、经验金钱、状态检查、Buff检查
         * 41-50:  最终整理的剩余公式
         *
         * 优化效果：
         * ✅ 按功能类型清晰分类
         * ✅ 中英文注释对照
         * ✅ 逻辑顺序排列
         * ✅ 便于快速查找和维护
         * ✅ 消除重复条目
         *
         * 总公式数量：保持原有数量不变
         * 分类数量：50个主要分类
         * 可维护性：大幅提升
         */
        // 94系列已移至8.1统一管理
        funMap.put("with(Math){ return _96015_==1;}", 731);
        funMap.put("with(Math){ return maindamage;}", 75);
        funMap.put("with(Math){ return _99003_>=1&&_96124_<=0.01;}", 807);
        // 94系列已移至8.1统一管理
        funMap.put("with(Math){ return _96207_>=4;}", 792);
        funMap.put("with(Math){ return -curhpb*0.8;}", 135);
        funMap.put("with(Math){ return _95003_<=9;}", 835);
        funMap.put("with(Math){ return (5000*RoleLv*0.05)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 574);
        funMap.put("with(Math){ return _99056_>=1;}", 862);
        funMap.put("with(Math){ return _94005_>=1;}", 623);
        funMap.put("with(Math){ return quality*12+150;}", 19);
        funMap.put("with(Math){ return -(phyattacka*0.65-defendb+1*skilllevela);}", 30);
        funMap.put("with(Math){ return _94007_>=2&&_94011_>=1;}", 625);
        funMap.put("with(Math){ return _97005_==1;}", 784);
        funMap.put("with(Math){ return min(-2.4*gradea+1.2*skilllevela,0);}", 35);
        funMap.put("with(Math){ return -curhpb*0.05;}", 120);
        funMap.put("with(Math){ return maxhpb*0.5;}", 131);
        funMap.put("with(Math){ return gradea*0.65;}", 219);
        funMap.put("with(Math){ return _95017_==1;}", 668);
        funMap.put("with(Math){ return _95051_>=2;}", 691);
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),5);}", 275);
        funMap.put("with(Math){ return _96301_>=1&&_96309_<1;}", 756);
        funMap.put("with(Math){ return 1.4*skilllevela;}", 86);
        funMap.put("with(Math){ return -curhpb;}", 257);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela);}", 339);
        funMap.put("with(Math){ return -phyattacka;}", 286);
        // Buff状态检查已移至第9节统一管理
        funMap.put("with(Math){ return 2.22*RoleLv*(random()*(1.02-0.98)+0.98);}", 460);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*0.25;}", 328);
        funMap.put("with(Math){ return (400*RoleLv*1.4)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 531);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,49)/10)*440+(200+floor(min(RoleLv,49)/10)*40*10))*2;}", 562);
        // Buff状态检查已移至第9节统一管理
        funMap.put("with(Math){ return maxhpa*1;}", 127);
        funMap.put("with(Math){ return _96351_>=1&&_96364_<1;}", 774);
        funMap.put("with(Math){ return _94035_>=1;}", 608);
        funMap.put("with(Math){ return _96111_>=1;}", 743);
        funMap.put("with(Math){ return _95077_>=2;}", 712);
        funMap.put("with(Math){ return (StdMoney*2/15)*(random()*(1.05-0.95)+0.95);}", 397);
        funMap.put("with(Math){ return -0.05+0.1*skilllevela;}", 108);
        funMap.put("with(Math){ return _95009_==1;}", 664);
        funMap.put("with(Math){ return ((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))*0.5;}", 52);
        funMap.put("with(Math){ return _94013_>=1;}", 630);
        funMap.put("with(Math){ return 20*skilllevela;}", 93);
        funMap.put("with(Math){ return (1000*RoleLv*0.1*(0.79+0.02*AnswerCnt))*(random()*(1.02-0.98)+0.98);}", 398);
        funMap.put("with(Math){ return gradea*0.87;}", 191);
        funMap.put("with(Math){ return _94044_>=1&&_94057_>=1;}", 655);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,79)/10)*440+(200+floor(min(RoleLv,79)/10)*40*10))*2;}", 567);
        funMap.put("with(Math){ return _96218_>=1;}", 802);
        funMap.put("with(Math){ return _94046_<1&&_94045_>=3;}", 606);
        funMap.put("with(Math){ return _95003_==5;}", 843);
        funMap.put("with(Math){ return _99048_>=1;}", 854);
        funMap.put("with(Math){ return -(magicattacka*0.8-magicdefb+2*gradea)*(0.5+0.05*(2-preaimcount));}", 177);
        funMap.put("with(Math){ return _96108_>=1;}", 737);
        funMap.put("with(Math){ return -(phyattacka*1.2-defendb+1*gradea);}", 231);
        funMap.put("with(Math){ return RoleLv*50;}", 409);
        funMap.put("with(Math){ return _95003_<=7;}", 833);
        funMap.put("with(Math){ return !_501402_;}", 934);
        funMap.put("with(Math){ return StdMoney*0.4*((Ring-1)*0.09+1);}", 372);
        funMap.put("with(Math){ return _96351_>=1&&_96363_<1;}", 773);
        funMap.put("with(Math){ return RoleLv*71.7+200;}", 393);
        // Buff状态检查已移至第9节统一管理
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea)*(0.25+0.05*skilllevela);}", 44);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,69)/10)*440+(200+floor(min(RoleLv,69)/10)*40*10))*2;}", 566);
        funMap.put("with(Math){ return 0.15+0.1*skilllevela;}", 88);
        funMap.put("with(Math){ return _94021_>=1;}", 644);
        funMap.put("with(Math){ return !_510139_;}", 921);
        funMap.put("with(Math){ return _96301_>=1&&_96366_<1;}", 763);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*gradea)*0.5*(1.2+(0.6+0.2*skilllevela)*(1-curmpb/maxmpb));}", 83);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,89)/10)*440+(200+floor(min(RoleLv,89)/10)*40*10))*2;}", 555);
        funMap.put("with(Math){ return _96004_>=4;}", 728);
        funMap.put("with(Math){ return (1*RoleLv*0.717*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 436);
        funMap.put("with(Math){ return _99039_>=1;}", 826);
        funMap.put("with(Math){ return round((pow(1.02,skilllevela)-1)*1000);}", 264);
        funMap.put("with(Math){ return _95063_==1;}", 704);
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),1);}", 271);
        funMap.put("with(Math){ return RoleLv*71.7+500;}", 377);
        funMap.put("with(Math){ return (1000*RoleLv*0.2*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 374);
        funMap.put("with(Math){ return 400*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.0392*(0.4+0.1*Saveid);}", 546);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+1.5*IsDbPoint+IsSerMul)*(5000*RoleLv*1.25/50);}", 576);
        funMap.put("with(Math){ return _94014_>=1;}", 632);
        funMap.put("with(Math){ return _97001_/_97002_>=0.2;}", 780);
        funMap.put("with(Math){ return -(phyattacka*0.65-defendb+1*gradea);}", 248);
        funMap.put("with(Math){ return _99042_>=3;}", 849);
        funMap.put("with(Math){ return gradea*0.378;}", 209);
        funMap.put("with(Math){ return -(magicattacka*1.2-magicdefb+2*skilllevela)*(0.5+0.05*(4-preaimcount));}", 53);
        funMap.put("with(Math){ return StdExp*0.83/14.5*((Ring-1)*0.1+1);}", 529);
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb+1*skilllevela);}", 62);
        funMap.put("with(Math){ return gradea*0.875;}", 210);
        funMap.put("with(Math){ return gradea*0.32;}", 189);
        funMap.put("with(Math){ return _503001_;}", 938);
        funMap.put("with(Math){ return (1*RoleLv*0.717*2*2)*(random()*(1.02-0.98)+0.98);}", 420);
        funMap.put("with(Math){ return 5000*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.058*(0.75+0.05*Saveid)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 570);
        funMap.put("with(Math){ return -(phyattacka*0.5+defenda*0.6-defendb+1*skilllevela);}", 112);
        funMap.put("with(Math){ return 3000+5000*random();}", 404);
        funMap.put("with(Math){ return min(max(rolenum*0.004,8),16);}", 596);
        funMap.put("with(Math){ return maxhpb*0.09+600;}", 240);
        funMap.put("with(Math){ return _96204_>=1;}", 789);
        funMap.put("with(Math){ return !_508002_;}", 935);
        funMap.put("with(Math){ return (400*RoleLv*1.099*3)*(random()*(1.02-0.98)+0.98);}", 486);
        funMap.put("with(Math){ return _99034_>=1;}", 811);
        funMap.put("with(Math){ return (5000*RoleLv*0.075)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 580);
        funMap.put("with(Math){ return _96122_<1;}", 749);
        funMap.put("with(Math){ return _95062_==1;}", 702);
        funMap.put("with(Math){ return _94019_<1;}", 638);
        funMap.put("with(Math){ return _96206_>=4;}", 791);
        funMap.put("with(Math){ return maxhpb*0.06+400;}", 239);
        funMap.put("with(Math){ return _96301_>=1&&_96308_<1;}", 755);
        funMap.put("with(Math){ return -max(curhpa-maxhpa*0.1,0);}", 234);
        funMap.put("with(Math){ return 400*min(RoleLv,84)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 482);
        funMap.put("with(Math){ return 16*skilllevela;}", 71);
        funMap.put("with(Math){ return (400*RoleLv*0.5)*(random()*(1.02-0.98)+0.98);}", 471);
        funMap.put("with(Math){ return _95003_==3;}", 839);
        funMap.put("with(Math){ return -(magicattacka*2.2-magicdefb+2*gradea+max((magicattacka-magicattackb)*0.3,0));}", 290);
        funMap.put("with(Math){ return gradea*0.4;}", 157);
        funMap.put("with(Math){ return -1.4*skilllevela;}", 56);
        funMap.put("with(Math){ return (400*RoleLv*1.8)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 483);
        funMap.put("with(Math){ return _95057_<1;}", 705);
        funMap.put("with(Math){ return ((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)));}", 58);
        funMap.put("with(Math){ return (1*TeamLv*0.196*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 447);
        funMap.put("with(Math){ return gradea*0.125;}", 184);
        funMap.put("with(Math){ return _94022_>=1;}", 645);
        funMap.put("with(Math){ return -(phyattacka*0.75-defendb+1*skilllevela);}", 28);
        funMap.put("with(Math){ return -magicattacka*0.1;}", 293);
        funMap.put("with(Math){ return _96301_>=1&&_96311_<1;}", 758);
        funMap.put("with(Math){ return _99040_>=2;}", 841);
        funMap.put("with(Math){ return (1+14*IsDbPoint+IsSerMul)*(StdMoney*1.5/74*((Ring-1)*0.09+1))*(random()*(1.2-0.8)+0.8);}", 412);
        funMap.put("with(Math){ return gradea*1.2;}", 203);
        funMap.put("with(Math){ return _95003_<=5;}", 831);
        funMap.put("with(Math){ return _94030_>=3;}", 650);
        funMap.put("with(Math){ return ((medicala+3*skilllevela)*0.25+abs(maindamage)*0.25)*(1+healrevisea)*(1+medicaljiashena/1000);}", 315);
        funMap.put("with(Math){ return _96209_>=1;}", 794);
        funMap.put("with(Math){ return _96351_>=1&&_96365_<1;}", 775);
        funMap.put("with(Math){ return 0.15*maxhpb;}", 37);
        funMap.put("with(Math){ return _96104_>=1;}", 739);
        funMap.put("with(Math){ return -(phyattacka*0.55-defendb+1*skilllevela);}", 34);
        funMap.put("with(Math){ return _96212_>=1;}", 797);
        funMap.put("with(Math){ return (quality*12+150)*(havebuffa(508042)?(1.2):(1));}", 8);
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb*0.9+1*skilllevela);}", 64);
        funMap.put("with(Math){ return speeda;}", 260);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+1.5*gradea)*1.3;}", 280);
        funMap.put("with(Math){ return _96205_>=1;}", 790);
        funMap.put("with(Math){ return -(phyattacka*1.05-defendb+1*skilllevela);}", 25);
        funMap.put("with(Math){ return !_504002_;}", 896);
        funMap.put("with(Math){ return (quality*5+50)*(havebuffa(508042)?(1.2):(1));}", 9);
        funMap.put("with(Math){ return _96213_>=3;}", 798);
        funMap.put("with(Math){ return -(magicattacka*2.5-magicdefb+2*skilllevela)*0.5*(havebuffa(506109)?((random()*(1.20-1.1))+1.1):((random()*(1.20-0.9))+0.9))*2;}", 332);
        funMap.put("with(Math){ return _95059_>=1;}", 697);
        funMap.put("with(Math){ return 0.25+0.001*(skilllevela-gradeb);}", 312);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*160;}", 364);
        funMap.put("with(Math){ return -maxhpb*0.6;}", 141);
        funMap.put("with(Math){ return (StdMoney*2)*(random()*(1.05-0.95)+0.95);}", 391);
        funMap.put("with(Math){ return _94033_>=2;}", 604);
        funMap.put("with(Math){ return (1000*RoleLv*0.232*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1))*0.5)*(random()*(1.02-0.98)+0.98);}", 356);
        funMap.put("with(Math){ return 400*min(RoleLv,69)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 496);
        funMap.put("with(Math){ return 9*skilllevela;}", 49);
        // 技能公式已移至第10节统一管理
        funMap.put("with(Math){ return _94026_>=1;}", 616);
        funMap.put("with(Math){ return _99002_<1;}", 751);
        funMap.put("with(Math){ return gradea*0.45;}", 223);
        funMap.put("with(Math){ return 0.3*skilllevela;}", 259);
        funMap.put("with(Math){ return abs(maindamage*0.5);}", 340);
        funMap.put("with(Math){ return -speeda*0.5;}", 292);
        funMap.put("with(Math){ return 0.4*maxhpb;}", 235);
        funMap.put("with(Math){ return 0.7*maxhpb;}", 99);
        funMap.put("with(Math){ return floor((min(max(floor((ServerLv-40)*0.2),0),4)*3+6)*min(max(rolenum*0.0005,1),2));}", 600);
        funMap.put("with(Math){ return null;}", 598);
        funMap.put("with(Math){ return _95058_>=1;}", 696);
        funMap.put("with(Math){ return _96215_>=4;}", 799);
        funMap.put("with(Math){ return _96102_>=1;}", 745);
        funMap.put("with(Math){ return !_13_;}", 893);
        funMap.put("with(Math){ return _95039_<1;}", 678);
        funMap.put("with(Math){ return -speeda*2;}", 294);
        funMap.put("with(Math){ return _95064_>=2;}", 706);
        funMap.put("with(Math){ return gradea*0.2;}", 183);
        funMap.put("with(Math){ return (5000*RoleLv*0.049*1.5)*(random()*(1.02-0.98)+0.98);}", 593);
        funMap.put("with(Math){ return _95003_<=3;}", 829);
        funMap.put("with(Math){ return gradea*3.75;}", 229);
        funMap.put("with(Math){ return _95046_<=1;}", 711);
        funMap.put("with(Math){ return RoleLv*0.71+40;}", 442);
        funMap.put("with(Math){ return 10+1.4*skilllevela;}", 104);
        funMap.put("with(Math){ return effectpointa>=5;}", 642);
        funMap.put("with(Math){ return _99049_>=1;}", 856);
        funMap.put("with(Math){ return (1*RoleLv*1.333)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 454);
        funMap.put("with(Math){ return (400*RoleLv*0.678*2)*(random()*(1.02-0.98)+0.98);}", 466);
        funMap.put("with(Math){ return -(magicattacka*2.5-magicdefb+2*skilllevela)*0.5*(havebuffa(506109)?((random()*(1.20-1.1))+1.1):((random()*(1.20-0.9))+0.9))*(1+(0.4+0.1*skilllevela)*(1-curhpa/maxhpa));}", 92);
        funMap.put("with(Math){ return (curhpa/maxhpa)<=0.4;}", 42);
        funMap.put("with(Math){ return maxhpa*0.12;}", 281);
        funMap.put("with(Math){ return -(phyattacka*1.10-defendb+1*skilllevela)*2.5;}", 151);
        funMap.put("with(Math){ return -min(0.1*curhpb,10*skilllevela)-3*skilllevela;}", 73);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)/10)*440+(200+floor(min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)/10)*40*Saveid))*2;}", 556);
        funMap.put("with(Math){ return _99057_<1;}", 872);
        funMap.put("with(Math){ return (((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16)*(0.3+0.05*skilllevela)*0.5;}", 60);
        funMap.put("with(Math){ return _99065_>=3;}", 889);
        funMap.put("with(Math){ return effectpointa<2;}", 66);
        funMap.put("with(Math){ return -curhpa*0.8;}", 134);
        funMap.put("with(Math){ return _96351_>=1&&_96357_<1;}", 767);
        funMap.put("with(Math){ return (1000*RoleLv*0.215*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1))*0.5)*(random()*(1.02-0.98)+0.98);}", 361);
        funMap.put("with(Math){ return _94004_>=1;}", 620);
        funMap.put("with(Math){ return -magicattacka*3;}", 153);
        funMap.put("with(Math){ return quality*0.4+10;}", 6);
        funMap.put("with(Math){ return (1000*RoleLv*0.377*3)*(random()*(1.02-0.98)+0.98);}", 362);
        funMap.put("with(Math){ return -curmpa;}", 245);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*6);}", 491);
        funMap.put("with(Math){ return _99045_<1;}", 852);
        funMap.put("with(Math){ return _96123_>=1;}", 750);
        funMap.put("with(Math){ return gradea*0.000875;}", 196);
        funMap.put("with(Math){ return 400*min(RoleLv,49)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 503);
        funMap.put("with(Math){ return _99001_<1&&_99018_>=1;}", 820);
        funMap.put("with(Math){ return gradea>=60;}", 164);
        funMap.put("with(Math){ return _99031_>=1;}", 808);
        funMap.put("with(Math){ return _504003_;}", 914);
        funMap.put("with(Math){ return (StdMoney*2/10)*(random()*(1.05-0.95)+0.95);}", 383);
        funMap.put("with(Math){ return quality*32+1000;}", 4);
        funMap.put("with(Math){ return _95082_>=2;}", 717);
        funMap.put("with(Math){ return MonsterLv+20;}", 438);
        funMap.put("with(Math){ return _96301_>=1&&_96307_<1;}", 754);
        funMap.put("with(Math){ return _95086_>=1&&_95087_<1;}", 722);
        funMap.put("with(Math){ return -(phyattacka*0.95-defendb+1*skilllevela);}", 68);
        funMap.put("with(Math){ return _97003_>=1;}", 782);
        funMap.put("with(Math){ return gradea*0.24;}", 185);
        funMap.put("with(Math){ return (1000*RoleLv*0.3)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 375);
        funMap.put("with(Math){ return _99001_<1&&_99013_>=1;}", 815);
        funMap.put("with(Math){ return (1000*RoleLv*0.5)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 371);
        funMap.put("with(Math){ return skilllevela*50;}", 116);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,74)/10)*440+(200+floor(min(RoleLv,74)/10)*40*10))*2;}", 557);
        funMap.put("with(Math){ return _96113_<1;}", 746);
        funMap.put("with(Math){ return (400*RoleLv*1)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 489);
        funMap.put("with(Math){ return _94012_>=2;}", 629);
        funMap.put("with(Math){ return _99068_>=1;}", 884);
        funMap.put("with(Math){ return max(floor((Ring-4)/2),0);}", 463);
        funMap.put("with(Math){ return (1+IsSerMul)*(StdExp*0.83/14.5*((Ring-1)*0.1+1));}", 518);
        funMap.put("with(Math){ return _95088_<=0;}", 725);
        funMap.put("with(Math){ return -maxhpb*0.5;}", 130);
        funMap.put("with(Math){ return -(phyattacka*1.55-defendb+1*skilllevela)*2.5;}", 149);
        funMap.put("with(Math){ return gradea*0.8;}", 156);
        funMap.put("with(Math){ return skilllevela*8;}", 117);
        funMap.put("with(Math){ return gradea*1.6;}", 160);
        funMap.put("with(Math){ return _95042_>=1;}", 682);
        funMap.put("with(Math){ return 400*min(RoleLv,59)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 520);
        funMap.put("with(Math){ return -(phyattacka*2-defendb+1*gradea);}", 170);
        funMap.put("with(Math){ return _95079_<1;}", 715);
        funMap.put("with(Math){ return _95074_==1;}", 708);
        funMap.put("with(Math){ return _95003_<=1;}", 667);
        funMap.put("with(Math){ return effectpointa>=3;}", 640);
        funMap.put("with(Math){ return (400*RoleLv*1.215*1.5)*(random()*(1.02-0.98)+0.98);}", 544);
        funMap.put("with(Math){ return _505005_||_504011_;}", 904);
        funMap.put("with(Math){ return !_509951_;}", 932);
        funMap.put("with(Math){ return _502003_;}", 892);
        funMap.put("with(Math){ return (curhpa/maxhpa)<=0.6;}", 41);
        funMap.put("with(Math){ return -(phyattacka*2-defendb+1*skilllevela);}", 285);
        funMap.put("with(Math){ return _96107_>=1;}", 735);
        funMap.put("with(Math){ return (1*RoleLv*11)*(random()*(1.02-0.98)+0.98);}", 450);
        funMap.put("with(Math){ return -(phyattacka*1.15-min(defendb,magicdefb)+1*skilllevela);}", 306);
        funMap.put("with(Math){ return (StdMoney*6/10)*(random()*(1.05-0.95)+0.95);}", 417);
        funMap.put("with(Math){ return _99001_<1&&_99011_>=1;}", 813);
        funMap.put("with(Math){ return phyattacka*1.25-defendb+1*gradea;}", 171);
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb+1*gradea);}", 277);
        funMap.put("with(Math){ return -min(0.25*curhpb,50*skilllevela);}", 311);
        funMap.put("with(Math){ return _96208_<3;}", 793);
        funMap.put("with(Math){ return gradea*0.0007;}", 195);
        funMap.put("with(Math){ return gradea*0.15;}", 186);
        funMap.put("with(Math){ return _94003_>=1&&_94006_>=3;}", 636);
        funMap.put("with(Math){ return _96124_<=0.01;}", 730);
        funMap.put("with(Math){ return (400*RoleLv*1.215*2)*(random()*(1.02-0.98)+0.98);}", 534);
        funMap.put("with(Math){ return -(magicattacka*2.5-magicdefb+2*gradea)*0.5;}", 350);
        funMap.put("with(Math){ return -magicattacka*0.2;}", 82);
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb*0.9+1*skilllevela+max(0,speeda-speedb)*0.5);}", 317);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,84)/10)*440+(200+floor(min(RoleLv,84)/10)*40*10))*2;}", 565);
        funMap.put("with(Math){ return _504013_;}", 917);
        funMap.put("with(Math){ return _94020_>=1;}", 643);
        funMap.put("with(Math){ return _95021_==1;}", 671);
        funMap.put("with(Math){ return (1+IsSerMul)*(5000*RoleLv*0.5/20);}", 590);
        funMap.put("with(Math){ return _96109_>=1;}", 741);
        funMap.put("with(Math){ return _99001_<1&&_99030_==1;}", 805);
        funMap.put("with(Math){ return _95045_>=1;}", 685);
        funMap.put("with(Math){ return 5000*RoleLv*0.1*(0.74+0.02*Time)*(random()*(1.02-0.98)+0.98);}", 585);
        funMap.put("with(Math){ return (400*RoleLv*1.215*1)*(random()*(1.02-0.98)+0.98);}", 545);
        funMap.put("with(Math){ return maxhpb*0.03+200;}", 238);
        funMap.put("with(Math){ return _13_;}", 916);
        funMap.put("with(Math){ return _99047_>=1;}", 853);
        funMap.put("with(Math){ return -magicattacka*1.2;}", 283);
        funMap.put("with(Math){ return _94006_<3&&_94005_>=1;}", 635);
        funMap.put("with(Math){ return gradea*0.6;}", 225);
        funMap.put("with(Math){ return (IsDbPoint*(Ring-1)*1+6);}", 443);
        funMap.put("with(Math){ return (((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16)*(0.3+0.05*skilllevela)*0.5/(1-(((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16)*(0.3+0.05*skilllevela)*0.5);}", 61);
        funMap.put("with(Math){ return 1000*RoleLv*0.194*(0.85+0.03*((Time-1)%9+1))*(0.9+0.1*(floor((Time-1)/9)+1))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 394);
        funMap.put("with(Math){ return min(maxhpb*0.25,gradeb*18);}", 243);
        funMap.put("with(Math){ return -maxhpb*0.3;}", 139);
        funMap.put("with(Math){ return (1000*RoleLv*0.215*2)*(random()*(1.02-0.98)+0.98);}", 366);
        funMap.put("with(Math){ return _95003_<=10;}", 836);
        funMap.put("with(Math){ return gradea*1.4;}", 194);
        funMap.put("with(Math){ return effectpointa>=1;}", 338);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*6.7/5);}", 490);
        funMap.put("with(Math){ return _95052_>=2;}", 692);
        funMap.put("with(Math){ return (curhpa/maxhpa)<=0.8;}", 40);
        funMap.put("with(Math){ return _94034_<1;}", 605);
        funMap.put("with(Math){ return -curhpb*0.2;}", 122);
        funMap.put("with(Math){ return (1000*min(max(RoleLv,FuBenLv),FuBenLv+9)*0.2*(0.7+0.1*Ring))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 399);
        funMap.put("with(Math){ return (1*RoleLv*10)*(random()*(1.02-0.98)+0.98);}", 432);
        funMap.put("with(Math){ return (400*RoleLv*0.5*(0.79+0.02*AnswerCnt))*(random()*(1.02-0.98)+0.98);}", 478);
        funMap.put("with(Math){ return skilllevela>=2;}", 267);
        funMap.put("with(Math){ return -min((random()*(0.13-0.07)+0.07)*maxhpa,curhpa-1);}", 36);
        funMap.put("with(Math){ return -(phyattacka*0.75-defendb+1*gradea);}", 166);
        funMap.put("with(Math){ return RoleLv*5;}", 433);
        funMap.put("with(Math){ return _94001_>=1;}", 617);
        funMap.put("with(Math){ return (1000*RoleLv*0.5)*(random()*(1.02-0.98)+0.98);}", 373);
        funMap.put("with(Math){ return (1*RoleLv*1.256*2*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 428);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*gradea)*(0.5+0.05*(2-preaimcount));}", 163);
        funMap.put("with(Math){ return _94003_>=1&&_94006_<3;}", 637);
        funMap.put("with(Math){ return _99058_<1;}", 873);
        funMap.put("with(Math){ return 2*skilllevela+50;}", 97);
        funMap.put("with(Math){ return _96120_>=1;}", 747);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea)*0.8;}", 298);
        funMap.put("with(Math){ return (1000*RoleLv*0.194*1)*(random()*(1.02-0.98)+0.98);}", 418);
        funMap.put("with(Math){ return ((random()*(5-3))+3);}", 173);
        funMap.put("with(Math){ return _94054_>=1;}", 615);
        funMap.put("with(Math){ return _99062_>=3;}", 880);
        funMap.put("with(Math){ return (5000*RoleLv*0.049*2)*(random()*(1.02-0.98)+0.98);}", 592);
        funMap.put("with(Math){ return maindamage*((random()*(1.05-0.95))+0.95);}", 233);
        funMap.put("with(Math){ return _95028_==1;}", 673);
        funMap.put("with(Math){ return _95076_==1;}", 710);
        funMap.put("with(Math){ return _95044_>=1;}", 684);
        funMap.put("with(Math){ return _94025_>=1&&_94030_==1;}", 649);
        funMap.put("with(Math){ return _96351_>=1&&_96361_<1;}", 771);
        funMap.put("with(Math){ return (400*RoleLv*0.678*0.6);}", 538);
        funMap.put("with(Math){ return (1000*RoleLv*0.694*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 510);
        funMap.put("with(Math){ return _99061_<1;}", 876);
        // Buff状态检查已移至第9节统一管理
        funMap.put("with(Math){ return maxhpa*0.1;}", 102);
        funMap.put("with(Math){ return _99066_>=1;}", 881);
        funMap.put("with(Math){ return -(phyattacka*0.75-defendb+1*skilllevela+max(0,speeda-speedb)*0.5);}", 321);
        funMap.put("with(Math){ return (StdMoney*2/5)*(random()*(1.05-0.95)+0.95);}", 392);
        funMap.put("with(Math){ return _99001_<1&&_99015_>=1;}", 817);
        funMap.put("with(Math){ return 1*TeamNum;}", 946);
        funMap.put("with(Math){ return maindamage*0.2*((random()*(1.05-0.95))+0.95);}", 174);
        funMap.put("with(Math){ return gradea*0.28;}", 187);
        funMap.put("with(Math){ return (1*RoleLv*0.717*2)*(random()*(1.02-0.98)+0.98);}", 422);
        funMap.put("with(Math){ return 5000*RoleLv*0.049*(0.85+0.03*((Time-1)%9+1))*(0.9+0.1*(floor((Time-1)/9)+1))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 582);
        funMap.put("with(Math){ return -magicattacka*2.2;}", 154);
        funMap.put("with(Math){ return _99060_>=1;}", 866);
        funMap.put("with(Math){ return (5000*RoleLv*0.049*1)*(random()*(1.02-0.98)+0.98);}", 581);
        funMap.put("with(Math){ return _99054_<1;}", 869);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*4;}", 441);
        funMap.put("with(Math){ return _94039_>=1;}", 612);
        funMap.put("with(Math){ return _95075_==1;}", 709);
        funMap.put("with(Math){ return 2.5*skilllevela;}", 22);
        funMap.put("with(Math){ return _99067_>=1;}", 882);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,99)/10)*440+(200+floor(min(RoleLv,99)/10)*40*10))*2;}", 561);
        funMap.put("with(Math){ return _99061_>=1;}", 867);
        funMap.put("with(Math){ return maxmpb*0.1+150;}", 241);
        funMap.put("with(Math){ return !_506101_;}", 920);
        funMap.put("with(Math){ return _99001_<1&&_99014_>=1;}", 816);
        funMap.put("with(Math){ return -maxhpb*0.1;}", 137);
        funMap.put("with(Math){ return !_508236_;}", 930);
        funMap.put("with(Math){ return _96351_>=1&&_96358_<1;}", 768);
        funMap.put("with(Math){ return -(phyattacka*0.85-defendb+1*skilllevela+max(0,speeda-speedb)*0.5);}", 320);
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),6);}", 276);
        funMap.put("with(Math){ return (400*RoleLv*0.25*(0.89+0.02*AnswerCnt))*0.5*(random()*(1.02-0.98)+0.98);}", 492);
        funMap.put("with(Math){ return 5+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),1);}", 331);
        funMap.put("with(Math){ return (400*RoleLv*0.628*0.6);}", 502);
        funMap.put("with(Math){ return skilllevela>=4;}", 269);
        funMap.put("with(Math){ return _99044_<1;}", 851);
        funMap.put("with(Math){ return (1000*RoleLv*0.1)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 387);
        funMap.put("with(Math){ return (1000*RoleLv*0.4)*(random()*(1.02-0.98)+0.98);}", 381);
        funMap.put("with(Math){ return _96121_>=1;}", 748);
        funMap.put("with(Math){ return !_501010_;}", 918);
        funMap.put("with(Math){ return _99062_>=1;}", 878);
        funMap.put("with(Math){ return _96232_>=1;}", 837);
        funMap.put("with(Math){ return gradea*0.08;}", 200);
        funMap.put("with(Math){ return 100*1;}", 358);
        funMap.put("with(Math){ return (((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16)*0.3;}", 345);
        funMap.put("with(Math){ return _94005_>=5;}", 621);
        funMap.put("with(Math){ return -min(0.15*curhpb,15*skilllevela)-3*skilllevela;}", 313);
        funMap.put("with(Math){ return (curhpa/maxhpa)<=0.2;}", 43);
        funMap.put("with(Math){ return (medicala+3*skilllevela)*0.4;}", 85);
        funMap.put("with(Math){ return _99001_<1&&_99019_>=1;}", 821);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+7*IsDbPoint+IsSerMul)*(StdExp*10/63*(14*0.05+1));}", 480);
        funMap.put("with(Math){ return 5000*RoleLv;}", 378);
        funMap.put("with(Math){ return (5000*TeamLv*0.05*(1-IsDbPoint)+5000*TeamLv*0.124*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 579);
        funMap.put("with(Math){ return -(magicattacka*0.5-magicdefb+2*gradea)*(0.5+0.05*(2-preaimcount));}", 176);
        funMap.put("with(Math){ return -magicattacka*10;}", 288);
        funMap.put("with(Math){ return _95003_==8;}", 846);
        funMap.put("with(Math){ return (5000*RoleLv*0.125)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 595);
        funMap.put("with(Math){ return -3*skilllevela;}", 309);
        funMap.put("with(Math){ return _509082_||_509083_||_506201_;}", 907);
        funMap.put("with(Math){ return (1*RoleLv*1.256*2)*(random()*(1.02-0.98)+0.98);}", 457);
        funMap.put("with(Math){ return -curmpa*0.8;}", 136);
        funMap.put("with(Math){ return (1+IsSerMul)*(StdExp*0.2);}", 501);
        funMap.put("with(Math){ return -(phyattacka*1.60-defendb+1*skilllevela)*2.5;}", 150);
        funMap.put("with(Math){ return (1000*RoleLv*0.194*1.5)*(random()*(1.02-0.98)+0.98);}", 413);
        funMap.put("with(Math){ return _94003_>=1;}", 619);
        // Buff状态检查已移至第9节统一管理
        funMap.put("with(Math){ return _99001_<1&&_99017_>=1;}", 819);
        funMap.put("with(Math){ return gradea*0.0625;}", 214);
        funMap.put("with(Math){ return _94056_>=3;}", 855);
        funMap.put("with(Math){ return _95025_==1;}", 674);
        funMap.put("with(Math){ return _99001_<1;}", 804);
        funMap.put("with(Math){ return (5000*min(max(RoleLv,FuBenLv),FuBenLv+9)*0.1*(0.7+0.1*Ring))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 589);
        funMap.put("with(Math){ return _95041_>=3;}", 694);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*2;}", 452);
        funMap.put("with(Math){ return -magicattacka*1.6;}", 155);
        funMap.put("with(Math){ return _96301_>=1&&_96310_<1;}", 757);
        funMap.put("with(Math){ return _96301_>=1&&_96363_<1;}", 760);
        funMap.put("with(Math){ return (1000*RoleLv*0.05*(0.79+0.02*AnswerCnt))*0.5*(random()*(1.02-0.98)+0.98);}", 376);
        funMap.put("with(Math){ return (1000*RoleLv*0.194*2)*(random()*(1.02-0.98)+0.98);}", 408);
        funMap.put("with(Math){ return gradea*2;}", 65);
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb+1*skilllevela+max(0,speeda-speedb)*0.5);}", 316);
        funMap.put("with(Math){ return _94002_>=1;}", 618);
        funMap.put("with(Math){ return _99001_<1&&_99016_>=1;}", 818);
        funMap.put("with(Math){ return gradea*0.2835;}", 207);
        funMap.put("with(Math){ return (1000*RoleLv*0.377*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 352);
        funMap.put("with(Math){ return -1*skilllevela;}", 335);
        funMap.put("with(Math){ return _94038_>=2||_94028_>=1;}", 613);
        funMap.put("with(Math){ return _94005_>=3;}", 622);
        funMap.put("with(Math){ return _506003_;}", 900);
        funMap.put("with(Math){ return (400*RoleLv*0.628*2)*(random()*(1.02-0.98)+0.98);}", 506);
        funMap.put("with(Math){ return (StdExp*13.333/28.867*((Ring-1)*0.08+1)+RoleLv*50-1000);}", 465);
        funMap.put("with(Math){ return _96234_>=1;}", 838);
        funMap.put("with(Math){ return -0.1-(0.1+0.05*skilllevela)*(1-curmpb/maxmpb);}", 77);

        // ========================================
        // 🎉 InitFunMap 重新整理完成！(Reorganization Complete!)
        // ========================================
        /*
         * ✅ 整理成果总结 (Summary of Reorganization Results):
         *
         * 📊 统计信息 (Statistics):
         * - 总公式数量: 保持原有数量不变 (Total formulas: Original count maintained)
         * - 分类数量: 50个主要分类 (Categories: 50 main categories)
         * - 整理批次: 5个批次完成 (Batches: Completed in 5 batches)
         *
         * 🏗️ 分类结构 (Category Structure):
         * 1-10:   基础属性、伤害计算、生命值、治疗、概率、封印、经验金钱、状态检查、Buff检查、防御
         * 11-20:  更多伤害计算、基础属性、生命值、治疗、伤害、概率、封印、经验金钱、状态检查、Buff检查
         * 21-30:  更多治疗、生命值、基础属性、伤害、概率、封印、经验金钱、状态检查、Buff检查
         * 31-40:  更多基础属性、生命值、治疗、伤害、概率、封印、经验金钱、状态检查、Buff检查
         * 41-50:  最终整理的剩余公式
         *
         * 🎯 优化效果 (Optimization Results):
         * ✅ 按功能类型清晰分类 (Clear categorization by function type)
         * ✅ 中英文注释对照 (Chinese-English comment comparison)
         * ✅ 逻辑顺序排列 (Logical order arrangement)
         * ✅ 便于快速查找和维护 (Easy to find and maintain)
         * ✅ 消除重复条目 (Eliminated duplicate entries)
         * ✅ 提升代码可读性 (Improved code readability)
         *
         * 🔍 主要分类包含 (Main Categories Include):
         * - 基础属性计算 (Basic Attribute Calculations)
         * - 伤害计算公式 (Damage Calculation Formulas)
         * - 生命值/魔法值 (HP/MP Related)
         * - 治疗和医疗 (Healing and Medical)
         * - 概率和随机数 (Probability and Random)
         * - 封印相关 (Seal Related)
         * - 经验和金钱 (Experience and Money)
         * - 状态检查 (Status Checks)
         * - Buff状态检查 (Buff Status Checks)
         * - 防御相关 (Defense Related)
         *
         * 📈 维护性提升 (Maintainability Improvement):
         * - 新增公式时可快速定位到相应分类
         * - 修改公式时可快速找到相关公式
         * - 调试时可按功能模块查看
         * - 代码审查时结构清晰明了
         *
         * 🚀 后续建议 (Future Recommendations):
         * - 新增公式时请按照现有分类结构添加
         * - 保持注释的中英文对照格式
         * - 定期检查是否需要新增分类
         * - 考虑将复杂公式进一步细分
         */
    }

    public static Object JsFunbyID(IJavaScriptEngine iJavaScriptEngine, Fighter fighter, Fighter fighter2, int i) {
        float f;
        switch (i) {
            case 0:
                double max = Math.max(((fighter.getEffectRole().getAttrById(130) * 1.0f) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue()), fighter.getEffectRole().getAttrById(130) * 0.1d);
                if (fighter.getBuffAgent().existBuff(509300)) {
                    f = Math.min(Math.max(fighter2.getEffectRole().getAttrById(140) - fighter2.getEffectRole().getAttrById(130), ((Boolean) iJavaScriptEngine.get("pve")).booleanValue() ? 2 * iJavaScriptEngine.getDouble("gradea").intValue() : 10), 4 * iJavaScriptEngine.getDouble("gradea").intValue());
                } else {
                    f = 0.0f;
                }
                return Double.valueOf(-(max + f));
            case 1:
                return Float.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.0f) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue())));
            case 2:
                return Integer.valueOf((iJavaScriptEngine.getDouble("quality").intValue() * 60) + 2000);
            case 3:
                return Double.valueOf((iJavaScriptEngine.getDouble("quality").intValue() * 0.6d) + 10.0d);
            case 4:
                return Integer.valueOf((iJavaScriptEngine.getDouble("quality").intValue() * 32) + 1000);
            case 5:
                return Integer.valueOf(iJavaScriptEngine.getDouble("quality").intValue());
            case 6:
                return Double.valueOf((iJavaScriptEngine.getDouble("quality").intValue() * 0.4d) + 10.0d);
            case 7:
                return Double.valueOf(400.0d * (fighter.getBuffAgent().existBuff(508042) ? 1.2d : 1.0d));
            case 8:
                return Double.valueOf(((iJavaScriptEngine.getDouble("quality").intValue() * 12) + 150) * (fighter.getBuffAgent().existBuff(508042) ? 1.2d : 1.0d));
            case 9:
                return Double.valueOf(((iJavaScriptEngine.getDouble("quality").intValue() * 5) + 50) * (fighter.getBuffAgent().existBuff(508042) ? 1.2d : 1.0d));
            case 10:
                return Double.valueOf(iJavaScriptEngine.getDouble("quality").intValue() * 3 * (fighter.getBuffAgent().existBuff(508042) ? 1.2d : 1.0d));
            case 11:
                return Double.valueOf(((iJavaScriptEngine.getDouble("quality").intValue() * 5) + 100) * (fighter.getBuffAgent().existBuff(508042) ? 1.2d : 1.0d));
            case 12:
                return Double.valueOf(100.0d * (fighter.getBuffAgent().existBuff(508042) ? 1.2d : 1.0d));
            case 13:
                return Integer.valueOf((-iJavaScriptEngine.getDouble("quality").intValue()) * 3);
            case 14:
                return Boolean.valueOf(((Boolean) iJavaScriptEngine.get("pve")).booleanValue());
            case 15:
                return Double.valueOf(200.0d * (fighter.getBuffAgent().existBuff(508042) ? 1.2d : 1.0d));
            case 16:
                return Double.valueOf(150.0d * (fighter.getBuffAgent().existBuff(508042) ? 1.2d : 1.0d));
            case 17:
                return Double.valueOf(300.0d * (fighter.getBuffAgent().existBuff(508042) ? 1.2d : 1.0d));
            case 18:
                return Double.valueOf(250.0d * (fighter.getBuffAgent().existBuff(508042) ? 1.2d : 1.0d));
            case 19:
                return Integer.valueOf((iJavaScriptEngine.getDouble("quality").intValue() * 12) + 150);
            case 20:
                return Integer.valueOf((iJavaScriptEngine.getDouble("quality").intValue() * 5) + 50);
            case 21:
                return Integer.valueOf(iJavaScriptEngine.getDouble("skilllevel").intValue() * 10);
            case 22:
                return Double.valueOf(2.5d * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 23:
                return Integer.valueOf(2 * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 24:
                return Double.valueOf(10.0d + (1.2d * iJavaScriptEngine.getDouble("skilllevel").intValue()));
            case 25:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.05d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 26:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.9d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 27:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("skilllevel").intValue() >= 40);
            case 28:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.75d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 29:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("skilllevel").intValue() >= 70);
            case 30:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.65d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 31:
                return Boolean.valueOf(((double) (fighter.getEffectRole().getAttrById(80) / fighter.getEffectRole().getAttrById(60))) >= 0.5d);
            case 32:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.15d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 33:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.25d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 34:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.55d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 35:
                return Double.valueOf(Math.min(((-2.4d) * iJavaScriptEngine.getDouble("gradea").intValue()) + (1.2d * iJavaScriptEngine.getDouble("skilllevel").intValue()), 0.0d));
            case 36:
                return Double.valueOf(-Math.min(((Math.random() * 0.06d) + 0.07d) * fighter.getEffectRole().getAttrById(60), fighter.getEffectRole().getAttrById(80) - 1.0f));
            case 37:
                return Double.valueOf(0.15d * fighter2.getEffectRole().getAttrById(60));
            case 38:
                return Integer.valueOf(1 * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 39:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(130) * 1.0f) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue()))) * (0.45d + (0.05d * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 40:
                return Boolean.valueOf(((double) (fighter.getEffectRole().getAttrById(80) / fighter.getEffectRole().getAttrById(60))) <= 0.8d);
            case 41:
                return Boolean.valueOf(((double) (fighter.getEffectRole().getAttrById(80) / fighter.getEffectRole().getAttrById(60))) <= 0.6d);
            case 42:
                return Boolean.valueOf(((double) (fighter.getEffectRole().getAttrById(80) / fighter.getEffectRole().getAttrById(60))) <= 0.4d);
            case 43:
                return Boolean.valueOf(((double) (fighter.getEffectRole().getAttrById(80) / fighter.getEffectRole().getAttrById(60))) <= 0.2d);
            case 44:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(130) * 1.0f) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue()))) * (0.25d + (0.05d * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 45:
                return Double.valueOf(0.5d * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 46:
                return Integer.valueOf(10 * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 47:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.45d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 48:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.85d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 49:
                return Integer.valueOf(9 * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 50:
                return Integer.valueOf(3 * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 51:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 1.5d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * (0.5d + (0.05d * (4 - iJavaScriptEngine.getDouble("preaimcount").intValue()))));
            case 52:
                return Double.valueOf((fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? (0.98d - (0.32d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) : (0.66d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f))) * 0.5d);
            case 53:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 1.2d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * (0.5d + (0.05d * (4 - iJavaScriptEngine.getDouble("preaimcount").intValue()))));
            case 54:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("skilllevel").intValue() >= 60);
            case 55:
                return Double.valueOf((fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? (0.98d - (0.32d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) : (0.66d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f))) + 0.05d);
            case 56:
                return Double.valueOf((-1.4d) * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 57:
                return Double.valueOf(Math.min(((-1) * iJavaScriptEngine.getDouble("gradea").intValue()) + (0.5d * iJavaScriptEngine.getDouble("skilllevel").intValue()), 0.0d));
            case 58:
                return Double.valueOf(fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? (0.98d - (0.32d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) : (0.66d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)));
            case 59:
                return Integer.valueOf((-2) * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 60:
                return Double.valueOf(((fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? ((0.98d - (0.32d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) : ((0.66d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f))) - 0.16d) * (0.3d + (0.05d * iJavaScriptEngine.getDouble("skilllevel").intValue())) * 0.5d);
            case 61:
                return Double.valueOf(((((fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? ((0.98d - (0.32d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) : ((0.66d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f))) - 0.16d) * (0.3d + (0.05d * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * 0.5d) / (1.0d - ((((fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? ((0.98d - (0.32d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) : ((0.66d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f))) - 0.16d) * (0.3d + (0.05d * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * 0.5d)));
            case 62:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.1d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 63:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("skilllevel").intValue() >= 50);
            case 64:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.1d) - (fighter2.getEffectRole().getAttrById(140) * 0.9d)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 65:
                return Integer.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 2);
            case 66:
                return Boolean.valueOf(((Float) fighter.getFighterBean().getInitattrs().get(1010)).floatValue() < 2.0f);
            case 67:
                return Boolean.valueOf(((Float) fighter.getFighterBean().getInitattrs().get(1010)).floatValue() >= 2.0f);
            case 68:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.95d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 69:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("skilllevel").intValue() >= 90);
            case 70:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.45d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 71:
                return Integer.valueOf(16 * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 72:
                return Integer.valueOf(10 + (2 * iJavaScriptEngine.getDouble("skilllevel").intValue()));
            case 73:
                return Double.valueOf((-Math.min(0.1d * fighter2.getEffectRole().getAttrById(80), 10 * iJavaScriptEngine.getDouble("skilllevel").intValue())) - (3 * iJavaScriptEngine.getDouble("skilllevel").intValue()));
            case 74:
                return Double.valueOf(((fighter.getEffectRole().getAttrById(170) * 0.5d) + (iJavaScriptEngine.getDouble("skilllevel").intValue() * 1.4d) + (Math.abs(iJavaScriptEngine.getDouble("maindamage").intValue()) * 0.1d)) * (1.0f + fighter.getEffectRole().getAttrById(790)) * (1.0f + (fighter.getEffectRole().getAttrById(990) / 1000.0f)));
            case 75:
                return Integer.valueOf(iJavaScriptEngine.getDouble("maindamage").intValue());
            case 76:
                return Double.valueOf(((fighter.getEffectRole().getAttrById(170) * 0.2d) + (iJavaScriptEngine.getDouble("skilllevel").intValue() * 1.2d) + (Math.abs(iJavaScriptEngine.getDouble("maindamage").intValue()) * 0.08d)) * (1.0f + fighter.getEffectRole().getAttrById(790)) * (1.0f + (fighter.getEffectRole().getAttrById(990) / 1000.0f)));
            case 77:
                return Double.valueOf((-0.1d) - ((0.1d + (0.05d * iJavaScriptEngine.getDouble("skilllevel").intValue())) * (1.0f - (fighter2.getEffectRole().getAttrById(100) / fighter2.getEffectRole().getAttrById(90)))));
            case 78:
                return Double.valueOf(10.0d + (2.4d * iJavaScriptEngine.getDouble("skilllevel").intValue()));
            case 79:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 2.5d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * 0.5d);
            case 80:
                return Double.valueOf(Math.abs(iJavaScriptEngine.getDouble("maindamage").intValue()) * 0.35d);
            case 81:
                return Integer.valueOf(Math.abs(iJavaScriptEngine.getDouble("maindamage").intValue()) * 1);
            case 82:
                return Double.valueOf((-fighter.getEffectRole().getAttrById(150)) * 0.2d);
            case 83:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 1.5d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("gradea").intValue()))) * 0.5d * (1.2d + ((0.6d + (0.2d * iJavaScriptEngine.getDouble("skilllevel").intValue())) * (1.0f - (fighter2.getEffectRole().getAttrById(100) / fighter2.getEffectRole().getAttrById(90))))));
            case 84:
                return Float.valueOf(fighter.getEffectRole().getAttrById(170) + (3 * iJavaScriptEngine.getDouble("skilllevel").intValue()));
            case 85:
                return Double.valueOf((fighter.getEffectRole().getAttrById(170) + (3 * iJavaScriptEngine.getDouble("skilllevel").intValue())) * 0.4d);
            case 86:
                return Double.valueOf(1.4d * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 87:
                return Float.valueOf(fighter.getEffectRole().getAttrById(170) + (3 * iJavaScriptEngine.getDouble("skilllevel").intValue() * 2));
            case 88:
                return Double.valueOf(0.15d + (0.1d * iJavaScriptEngine.getDouble("skilllevel").intValue()));
            case 89:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 1.5d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * (0.5d + (0.05d * (3 - iJavaScriptEngine.getDouble("preaimcount").intValue()))) * (fighter.getBuffAgent().existBuff(506109) ? (Math.random() * 0.09999999999999987d) + 1.1d : (Math.random() * 0.29999999999999993d) + 0.9d));
            case 90:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 2.5d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * 0.5d * (fighter.getBuffAgent().existBuff(506109) ? (Math.random() * 0.09999999999999987d) + 1.1d : (Math.random() * 0.29999999999999993d) + 0.9d));
            case 91:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 1.0f) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * 0.5d);
            case 92:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 2.5d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * 0.5d * (fighter.getBuffAgent().existBuff(506109) ? (Math.random() * 0.09999999999999987d) + 1.1d : (Math.random() * 0.29999999999999993d) + 0.9d) * (1.0d + ((0.4d + (0.1d * iJavaScriptEngine.getDouble("skilllevel").intValue())) * (1.0f - (fighter.getEffectRole().getAttrById(80) / fighter.getEffectRole().getAttrById(60))))));
            case 93:
                return Integer.valueOf(20 * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 94:
                return Double.valueOf(2.1d * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 95:
                return Double.valueOf(fighter.getEffectRole().getAttrById(170) + (1.2d * iJavaScriptEngine.getDouble("skilllevel").intValue()));
            case 96:
                return Double.valueOf((fighter.getEffectRole().getAttrById(170) + (1.2d * iJavaScriptEngine.getDouble("skilllevel").intValue())) * 0.4d);
            case 97:
                return Integer.valueOf((2 * iJavaScriptEngine.getDouble("skilllevel").intValue()) + 50);
            case 98:
                return Integer.valueOf(randint(1, 2));
            case 99:
                return Double.valueOf(0.7d * fighter2.getEffectRole().getAttrById(60));
            case 100:
                return Double.valueOf(100.0d + (1.2d * iJavaScriptEngine.getDouble("skilllevel").intValue()));
            case 101:
                return Integer.valueOf(14 * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 102:
                return Double.valueOf(fighter.getEffectRole().getAttrById(60) * 0.1d);
            case 103:
                return Float.valueOf((fighter.getEffectRole().getAttrById(170) * 2.0f) + (iJavaScriptEngine.getDouble("skilllevel").intValue() * 10));
            case 104:
                return Double.valueOf(10.0d + (1.4d * iJavaScriptEngine.getDouble("skilllevel").intValue()));
            case 105:
                return Float.valueOf(fighter.getEffectRole().getAttrById(170) + (iJavaScriptEngine.getDouble("skilllevel").intValue() * 10));
            case 106:
                return Double.valueOf(4.0d + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 60), 1.0d) + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 90), 1.0d));
            case 107:
                return Double.valueOf(-((((fighter.getEffectRole().getAttrById(130) * 0.4d) + (fighter.getEffectRole().getAttrById(140) * 0.5d)) - fighter2.getEffectRole().getAttrById(140)) + (2 * iJavaScriptEngine.getDouble("gradea").intValue())));
            case 108:
                return Double.valueOf((-0.05d) + (0.1d * iJavaScriptEngine.getDouble("skilllevel").intValue()));
            case 109:
                return Double.valueOf(fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? (0.98d - (0.38d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)) + (((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) * (0.3d + (0.05d * iJavaScriptEngine.getDouble("skilllevel").intValue()))) : (0.6d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)) + (((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) * (0.3d + (0.05d * iJavaScriptEngine.getDouble("skilllevel").intValue()))));
            case 110:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.7d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 111:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(150) * 0.9d) - fighter2.getEffectRole().getAttrById(160)) + (3 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 112:
                return Double.valueOf(-((((fighter.getEffectRole().getAttrById(130) * 0.5d) + (fighter.getEffectRole().getAttrById(140) * 0.6d)) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 113:
                return Float.valueOf(-(((fighter.getEffectRole().getAttrById(150) * 1.0f) - fighter2.getEffectRole().getAttrById(160)) + (3 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 114:
                return Float.valueOf(fighter2.getEffectRole().getAttrById(60) * 1.0f);
            case 115:
                return Integer.valueOf(iJavaScriptEngine.getDouble("skilllevel").intValue() * 1);
            case 116:
                return Integer.valueOf(iJavaScriptEngine.getDouble("skilllevel").intValue() * 50);
            case 117:
                return Integer.valueOf(iJavaScriptEngine.getDouble("skilllevel").intValue() * 8);
            case 118:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(150) * 1.1d) - fighter2.getEffectRole().getAttrById(160)) + (3 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 119:
                return Float.valueOf((-fighter.getEffectRole().getAttrById(80)) * 1.0f);
            case 120:
                return Double.valueOf((-fighter2.getEffectRole().getAttrById(80)) * 0.05d);
            case 121:
                return Double.valueOf((-fighter2.getEffectRole().getAttrById(80)) * 0.1d);
            case 122:
                return Double.valueOf((-fighter2.getEffectRole().getAttrById(80)) * 0.2d);
            case 123:
                return Double.valueOf((-fighter2.getEffectRole().getAttrById(80)) * 0.5d);
            case 124:
                return Double.valueOf(3.0d + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 60), 1.0d));
            case 125:
                return Double.valueOf((-fighter2.getEffectRole().getAttrById(80)) * 0.7d);
            case 126:
                return Double.valueOf((-fighter2.getEffectRole().getAttrById(60)) * 2.5d);
            case 127:
                return Float.valueOf(fighter.getEffectRole().getAttrById(60) * 1.0f);
            case 128:
                return Float.valueOf(fighter2.getEffectRole().getAttrById(60));
            case 129:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 1.5d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * (0.5d + (0.05d * (3 - iJavaScriptEngine.getDouble("preaimcount").intValue()))));
            case 130:
                return Double.valueOf((-fighter2.getEffectRole().getAttrById(60)) * 0.5d);
            case 131:
                return Double.valueOf(fighter2.getEffectRole().getAttrById(60) * 0.5d);
            case 132:
                return Double.valueOf((fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? (0.98d - (0.32d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) : (0.66d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f))) * 0.4d);
            case 133:
                return Double.valueOf((fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? (0.98d - (0.32d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) : (0.66d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f))) * 0.3d);
            case 134:
                return Double.valueOf((-fighter.getEffectRole().getAttrById(80)) * 0.8d);
            case 135:
                return Double.valueOf((-fighter2.getEffectRole().getAttrById(80)) * 0.8d);
            case 136:
                return Double.valueOf((-fighter.getEffectRole().getAttrById(100)) * 0.8d);
            case 137:
                return Double.valueOf((-fighter2.getEffectRole().getAttrById(60)) * 0.1d);
            case 138:
                return Double.valueOf((-fighter2.getEffectRole().getAttrById(60)) * 0.2d);
            case 139:
                return Double.valueOf((-fighter2.getEffectRole().getAttrById(60)) * 0.3d);
            case 140:
                return Double.valueOf((-fighter2.getEffectRole().getAttrById(60)) * 0.4d);
            case 141:
                return Double.valueOf((-fighter2.getEffectRole().getAttrById(60)) * 0.6d);
            case 142:
                return Double.valueOf((-fighter2.getEffectRole().getAttrById(60)) * 0.7d);
            case 143:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 10.0f) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue()) + Math.max((fighter.getEffectRole().getAttrById(130) - fighter2.getEffectRole().getAttrById(130)) * 0.05d, 0.0d)));
            case 144:
                return Double.valueOf(0.6d + (0.002d * (iJavaScriptEngine.getDouble("skilllevel").intValue() - fighter2.getEffectRole().getLevel())) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)));
            case 145:
                return Double.valueOf(2.0d + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 60), 1.0d));
            case 146:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(130) * 1.4d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * 2.5d);
            case 147:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(130) * 1.45d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * 2.5d);
            case 148:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(130) * 1.5d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * 2.5d);
            case 149:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(130) * 1.55d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * 2.5d);
            case 150:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(130) * 1.6d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * 2.5d);
            case 151:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(130) * 1.1d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * 2.5d);
            case 152:
                return Float.valueOf((-fighter.getEffectRole().getAttrById(150)) * 2.0f);
            case 153:
                return Float.valueOf((-fighter.getEffectRole().getAttrById(150)) * 3.0f);
            case 154:
                return Double.valueOf((-fighter.getEffectRole().getAttrById(150)) * 2.2d);
            case 155:
                return Double.valueOf((-fighter.getEffectRole().getAttrById(150)) * 1.6d);
            case 156:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.8d);
            case 157:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.4d);
            case 158:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.9d);
            case 159:
                return Integer.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 20);
            case 160:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 1.6d);
            case 161:
                return Integer.valueOf(3 * iJavaScriptEngine.getDouble("gradea").intValue());
            case 162:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 2.2d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("gradea").intValue()))) * 0.5d);
            case 163:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 1.5d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("gradea").intValue()))) * (0.5d + (0.05d * (2 - iJavaScriptEngine.getDouble("preaimcount").intValue()))));
            case 164:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() >= 60);
            case 165:
                return Float.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.0f) - fighter2.getEffectRole().getAttrById(140)) + (3 * iJavaScriptEngine.getDouble("gradea").intValue())));
            case 166:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.75d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue())));
            case 167:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.45d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue())));
            case 168:
                return Double.valueOf(iJavaScriptEngine.getDouble("maindamage").intValue() * 0.33d * ((Math.random() * 0.10000000000000009d) + 0.95d));
            case 169:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.0f) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue()) + Math.max((fighter.getEffectRole().getAttrById(130) - fighter2.getEffectRole().getAttrById(130)) * 0.25d, 0.0d)));
            case 170:
                return Float.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 2.0f) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue())));
            case 171:
                return Double.valueOf(((fighter.getEffectRole().getAttrById(130) * 1.25d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue()));
            case 172:
                return Double.valueOf((Math.random() * 1.0d) + 2.0d);
            case 173:
                return Double.valueOf((Math.random() * 2.0d) + 3.0d);
            case 174:
                return Double.valueOf(iJavaScriptEngine.getDouble("maindamage").intValue() * 0.2d * ((Math.random() * 0.10000000000000009d) + 0.95d));
            case 175:
                return Integer.valueOf(-iJavaScriptEngine.getDouble("maindamage").intValue());
            case 176:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 0.5d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("gradea").intValue()))) * (0.5d + (0.05d * (2 - iJavaScriptEngine.getDouble("preaimcount").intValue()))));
            case 177:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 0.8d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("gradea").intValue()))) * (0.5d + (0.05d * (2 - iJavaScriptEngine.getDouble("preaimcount").intValue()))));
            case 178:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.05d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue())));
            case 179:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.55d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue())));
            case 180:
                return Double.valueOf(Math.abs(iJavaScriptEngine.getDouble("maindamage").intValue()) * 0.2d);
            case 181:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.16d);
            case 182:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.1d);
            case 183:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.2d);
            case 184:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.125d);
            case 185:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.24d);
            case 186:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.15d);
            case 187:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.28d);
            case 188:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.175d);
            case 189:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.32d);
            case 190:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.7d);
            case 191:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.87d);
            case 192:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 1.05d);
            case 193:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 1.22d);
            case 194:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 1.4d);
            case 195:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 7.0E-4d);
            case 196:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 8.75E-4d);
            case 197:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.00105d);
            case 198:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.001225d);
            case 199:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.0014d);
            case 200:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.08d);
            case 201:
                return Integer.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 1);
            case 202:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.12d);
            case 203:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 1.2d);
            case 204:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.14d);
            case 205:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.189d);
            case 206:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.2362d);
            case 207:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.2835d);
            case 208:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.3375d);
            case 209:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.378d);
            case 210:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.875d);
            case 211:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 1.225d);
            case 212:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.05d);
            case 213:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.5d);
            case 214:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.0625d);
            case 215:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.625d);
            case 216:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.075d);
            case 217:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.75d);
            case 218:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.0875d);
            case 219:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.65d);
            case 220:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.85d);
            case 221:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.3d);
            case 222:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.375d);
            case 223:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.45d);
            case 224:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.525d);
            case 225:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 0.6d);
            case 226:
                return Integer.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 3);
            case 227:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 3.25d);
            case 228:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 3.5d);
            case 229:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 3.75d);
            case 230:
                return Integer.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 4);
            case 231:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.2d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue())));
            case 232:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.6d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue())));
            case 233:
                return Double.valueOf(iJavaScriptEngine.getDouble("maindamage").intValue() * ((Math.random() * 0.10000000000000009d) + 0.95d));
            case 234:
                return Double.valueOf(-Math.max(fighter.getEffectRole().getAttrById(80) - (fighter.getEffectRole().getAttrById(60) * 0.1d), 0.0d));
            case 235:
                return Double.valueOf(0.4d * fighter2.getEffectRole().getAttrById(60));
            case 236:
                return Double.valueOf(0.35d * fighter2.getEffectRole().getAttrById(60));
            case 237:
                return Double.valueOf(0.3d * fighter2.getEffectRole().getAttrById(60));
            case 238:
                return Double.valueOf((fighter2.getEffectRole().getAttrById(60) * 0.03d) + 200.0d);
            case 239:
                return Double.valueOf((fighter2.getEffectRole().getAttrById(60) * 0.06d) + 400.0d);
            case 240:
                return Double.valueOf((fighter2.getEffectRole().getAttrById(60) * 0.09d) + 600.0d);
            case 241:
                return Double.valueOf((fighter2.getEffectRole().getAttrById(90) * 0.1d) + 150.0d);
            case 242:
                return Double.valueOf((fighter2.getEffectRole().getAttrById(90) * 0.15d) + 250.0d);
            case 243:
                return Double.valueOf(Math.min(fighter2.getEffectRole().getAttrById(60) * 0.25d, fighter2.getEffectRole().getLevel() * 18));
            case 244:
                return Double.valueOf(Math.min(fighter2.getEffectRole().getAttrById(60) * 0.5d, fighter2.getEffectRole().getLevel() * 30));
            case 245:
                return Float.valueOf(-fighter.getEffectRole().getAttrById(100));
            case 246:
                return Double.valueOf(Math.min(fighter2.getEffectRole().getAttrById(60) * 0.25d, fighter2.getEffectRole().getLevel() * 12));
            case 247:
                return Double.valueOf(Math.min(fighter2.getEffectRole().getAttrById(60) * 0.15d, fighter2.getEffectRole().getLevel() * 12));
            case 248:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.65d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue())));
            case 249:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.8d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue())));
            case 250:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.5d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue())));
            case 251:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 1.5d);
            case 252:
                return Double.valueOf(10.0d + (1.2d * iJavaScriptEngine.getDouble("gradea").intValue()));
            case 253:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.6d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 254:
                return Double.valueOf(fighter.getEffectRole().getAttrById(130) * 0.1d);
            case 255:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.6d) - Math.min(fighter2.getEffectRole().getAttrById(140), fighter2.getEffectRole().getAttrById(160))) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 256:
                return Double.valueOf(0.6d * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 257:
                return Float.valueOf(-fighter2.getEffectRole().getAttrById(80));
            case 258:
                return Integer.valueOf(6 * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 259:
                return Double.valueOf(0.3d * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 260:
                return Float.valueOf(fighter.getEffectRole().getAttrById(200));
            case 261:
                return Integer.valueOf(5 * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 262:
                return Double.valueOf((-fighter.getEffectRole().getAttrById(150)) * 0.7d);
            case 263:
                return Double.valueOf(fighter.getEffectRole().getAttrById(200) * 0.5d);
            case 264:
                return Long.valueOf(Math.round((Math.pow(1.02d, iJavaScriptEngine.getDouble("skilllevel").intValue()) - 1.0d) * 1000.0d));
            case 265:
                return Long.valueOf(Math.round((1.0d - Math.pow(0.98d, iJavaScriptEngine.getDouble("skilllevel").intValue())) * 1000.0d));
            case 266:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.1d) - (fighter2.getEffectRole().getAttrById(140) * 0.9d)) + (2 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 267:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("skilllevel").intValue() >= 2);
            case 268:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("skilllevel").intValue() >= 3);
            case 269:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("skilllevel").intValue() >= 4);
            case 270:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("survivala").intValue() < iJavaScriptEngine.getDouble("survivalb").intValue());
            case 271:
                return Double.valueOf(3.0d + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 60), 1.0d) + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 90), 1.0d));
            case 272:
                return Double.valueOf(3.0d + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 60), 1.0d) + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 90), 2.0d));
            case 273:
                return Double.valueOf(3.0d + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 60), 1.0d) + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 90), 3.0d));
            case 274:
                return Double.valueOf(3.0d + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 60), 1.0d) + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 90), 4.0d));
            case 275:
                return Double.valueOf(3.0d + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 60), 1.0d) + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 90), 5.0d));
            case 276:
                return Double.valueOf(3.0d + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 60), 1.0d) + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 90), 6.0d));
            case 277:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.1d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue())));
            case 278:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 2.5d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue())));
            case 279:
                return Float.valueOf(((fighter.getEffectRole().getAttrById(130) * 2.0f) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue()));
            case 280:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 1.5d) - fighter2.getEffectRole().getAttrById(160)) + (1.5d * iJavaScriptEngine.getDouble("gradea").intValue()))) * 1.3d);
            case 281:
                return Double.valueOf(fighter.getEffectRole().getAttrById(60) * 0.12d);
            case 282:
                return Double.valueOf(fighter.getEffectRole().getAttrById(60) * 0.2d * 0.4d);
            case 283:
                return Double.valueOf((-fighter.getEffectRole().getAttrById(150)) * 1.2d);
            case 284:
                return Double.valueOf(fighter.getEffectRole().getAttrById(60) * 0.22d);
            case 285:
                return Float.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 2.0f) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 286:
                return Float.valueOf(-fighter.getEffectRole().getAttrById(130));
            case 287:
                return Double.valueOf((-fighter.getEffectRole().getAttrById(150)) * 1.7d);
            case 288:
                return Float.valueOf((-fighter.getEffectRole().getAttrById(150)) * 10.0f);
            case 289:
                return Float.valueOf(fighter.getEffectRole().getAttrById(150) * 10.0f);
            case 290:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(150) * 2.2d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("gradea").intValue()) + Math.max((fighter.getEffectRole().getAttrById(150) - fighter2.getEffectRole().getAttrById(150)) * 0.3d, 0.0d)));
            case 291:
                return Double.valueOf(Math.abs(iJavaScriptEngine.getDouble("maindamage").intValue() * 0.2d));
            case 292:
                return Double.valueOf((-fighter.getEffectRole().getAttrById(200)) * 0.5d);
            case 293:
                return Double.valueOf((-fighter.getEffectRole().getAttrById(150)) * 0.1d);
            case 294:
                return Float.valueOf((-fighter.getEffectRole().getAttrById(200)) * 2.0f);
            case 295:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 1.3d);
            case 296:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(130) * 1.0f) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue()))) * 0.5d);
            case 297:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(130) * 1.0f) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue()))) * 0.9d);
            case 298:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(130) * 1.0f) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("gradea").intValue()))) * 0.8d);
            case 299:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 1.5d) - fighter2.getEffectRole().getAttrById(160)) + (1.5d * iJavaScriptEngine.getDouble("gradea").intValue()))) * 1.2d);
            case 300:
                return Double.valueOf(0.5d + (0.001d * (iJavaScriptEngine.getDouble("skilllevel").intValue() - fighter2.getEffectRole().getLevel())));
            case 301:
                return Float.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.0f) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 302:
                return Double.valueOf(4.0d + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 60), 1.0d));
            case 303:
                return Double.valueOf(-(((0.15d + (0.001d * iJavaScriptEngine.getDouble("skilllevel").intValue())) * fighter2.getEffectRole().getAttrById(160)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 304:
                return Double.valueOf(-(((0.15d + (0.001d * iJavaScriptEngine.getDouble("skilllevel").intValue())) * fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 305:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.05d) - Math.min(fighter2.getEffectRole().getAttrById(140), fighter2.getEffectRole().getAttrById(160))) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 306:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.15d) - Math.min(fighter2.getEffectRole().getAttrById(140), fighter2.getEffectRole().getAttrById(160))) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 307:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.25d) - Math.min(fighter2.getEffectRole().getAttrById(140), fighter2.getEffectRole().getAttrById(160))) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 308:
                return Float.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.0f) - Math.min(fighter2.getEffectRole().getAttrById(140), fighter2.getEffectRole().getAttrById(160))) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 309:
                return Integer.valueOf((-3) * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 310:
                return Double.valueOf(0.6d + (0.001d * (iJavaScriptEngine.getDouble("skilllevel").intValue() - fighter2.getEffectRole().getLevel())));
            case 311:
                return Double.valueOf(-Math.min(0.25d * fighter2.getEffectRole().getAttrById(80), 50 * iJavaScriptEngine.getDouble("skilllevel").intValue()));
            case 312:
                return Double.valueOf(0.25d + (0.001d * (iJavaScriptEngine.getDouble("skilllevel").intValue() - fighter2.getEffectRole().getLevel())));
            case 313:
                return Double.valueOf((-Math.min(0.15d * fighter2.getEffectRole().getAttrById(80), 15 * iJavaScriptEngine.getDouble("skilllevel").intValue())) - (3 * iJavaScriptEngine.getDouble("skilllevel").intValue()));
            case 314:
                return Double.valueOf((((fighter.getEffectRole().getAttrById(170) + (3 * iJavaScriptEngine.getDouble("skilllevel").intValue())) * 0.5d) + (Math.abs(iJavaScriptEngine.getDouble("maindamage").intValue()) * 0.5d)) * (1.0f + fighter.getEffectRole().getAttrById(790)) * (1.0f + (fighter.getEffectRole().getAttrById(990) / 1000.0f)));
            case 315:
                return Double.valueOf((((fighter.getEffectRole().getAttrById(170) + (3 * iJavaScriptEngine.getDouble("skilllevel").intValue())) * 0.25d) + (Math.abs(iJavaScriptEngine.getDouble("maindamage").intValue()) * 0.25d)) * (1.0f + fighter.getEffectRole().getAttrById(790)) * (1.0f + (fighter.getEffectRole().getAttrById(990) / 1000.0f)));
            case 316:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.1d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue()) + (Math.max(0.0f, fighter.getEffectRole().getAttrById(200) - fighter2.getEffectRole().getAttrById(200)) * 0.5d)));
            case 317:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.1d) - (fighter2.getEffectRole().getAttrById(140) * 0.9d)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue()) + (Math.max(0.0f, fighter.getEffectRole().getAttrById(200) - fighter2.getEffectRole().getAttrById(200)) * 0.5d)));
            case 318:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 1.05d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue()) + (Math.max(0.0f, fighter.getEffectRole().getAttrById(200) - fighter2.getEffectRole().getAttrById(200)) * 0.5d)));
            case 319:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.95d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue()) + (Math.max(0.0f, fighter.getEffectRole().getAttrById(200) - fighter2.getEffectRole().getAttrById(200)) * 0.5d)));
            case 320:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.85d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue()) + (Math.max(0.0f, fighter.getEffectRole().getAttrById(200) - fighter2.getEffectRole().getAttrById(200)) * 0.5d)));
            case 321:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(130) * 0.75d) - fighter2.getEffectRole().getAttrById(140)) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue()) + (Math.max(0.0f, fighter.getEffectRole().getAttrById(200) - fighter2.getEffectRole().getAttrById(200)) * 0.5d)));
            case 322:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 2.0f) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * 0.5d);
            case 323:
                return Double.valueOf((-0.3d) * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 324:
                return Double.valueOf((fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? (0.98d - (0.32d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) : (0.66d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f))) + 0.05d + ((1.0f - (fighter.getEffectRole().getAttrById(80) / fighter.getEffectRole().getAttrById(60))) * 0.1d));
            case 325:
                return Double.valueOf((fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? (0.98d - (0.32d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) : (0.66d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f))) + ((1.0f - (fighter.getEffectRole().getAttrById(80) / fighter.getEffectRole().getAttrById(60))) * 0.1d));
            case 326:
                return Double.valueOf(0.5d + (0.001d * (iJavaScriptEngine.getDouble("skilllevel").intValue() - fighter2.getEffectRole().getLevel())) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820)));
            case 327:
                return Integer.valueOf((-8) * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 328:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 1.5d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * 0.25d);
            case 329:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 1.5d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * 0.1d);
            case 330:
                return Float.valueOf(fighter.getEffectRole().getAttrById(170) + (1 * iJavaScriptEngine.getDouble("skilllevel").intValue()));
            case 331:
                return Double.valueOf(5.0d + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 60), 1.0d) + Math.min(Math.floor(iJavaScriptEngine.getDouble("skilllevel").intValue() / 90), 1.0d));
            case 332:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 2.5d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("skilllevel").intValue()))) * 0.5d * (fighter.getBuffAgent().existBuff(506109) ? (Math.random() * 0.09999999999999987d) + 1.1d : (Math.random() * 0.29999999999999993d) + 0.9d) * 2.0d);
            case 333:
                return Double.valueOf(Math.max((((fighter.getEffectRole().getAttrById(150) * 2.5d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("skilllevel").intValue())) * 0.5d * (fighter.getBuffAgent().existBuff(506109) ? (Math.random() * 0.09999999999999987d) + 1.1d : (Math.random() * 0.29999999999999993d) + 0.9d), 3 * iJavaScriptEngine.getDouble("skilllevel").intValue()));
            case 334:
                return Double.valueOf((-0.7d) * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 335:
                return Integer.valueOf((-1) * iJavaScriptEngine.getDouble("skilllevel").intValue());
            case 336:
                return Double.valueOf(-Math.min(0.1d * fighter2.getEffectRole().getAttrById(80), 20 * iJavaScriptEngine.getDouble("skilllevel").intValue()));
            case 337:
                return Double.valueOf(-Math.min(0.05d * fighter2.getEffectRole().getAttrById(100), 10 * iJavaScriptEngine.getDouble("skilllevel").intValue()));
            case 338:
                return Boolean.valueOf(((Float) fighter.getFighterBean().getInitattrs().get(1010)).floatValue() >= 1.0f);
            case 339:
                return Double.valueOf(-(((fighter.getEffectRole().getAttrById(150) * 1.5d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("skilllevel").intValue())));
            case 340:
                return Double.valueOf(Math.abs(iJavaScriptEngine.getDouble("maindamage").intValue() * 0.5d));
            case 341:
                return Double.valueOf((fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? ((0.98d - (0.32d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) : ((0.66d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f))) - 0.16d);
            case 342:
                return Double.valueOf(((fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? ((0.98d - (0.32d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) : ((0.66d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f))) - 0.16d) * 0.6d);
            case 343:
                return Double.valueOf(((fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? ((0.98d - (0.32d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) : ((0.66d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f))) - 0.16d) * 0.5d);
            case 344:
                return Double.valueOf(((fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? ((0.98d - (0.32d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) : ((0.66d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f))) - 0.16d) * 0.4d);
            case 345:
                return Double.valueOf(((fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? ((0.98d - (0.32d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) : ((0.66d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f))) - 0.16d) * 0.3d);
            case 346:
                return Double.valueOf(((fighter.getEffectRole().getAttrById(180) >= fighter2.getEffectRole().getAttrById(190) ? ((0.98d - (0.32d * Math.pow(0.95d, (fighter.getEffectRole().getAttrById(180) / 10.0f) - (fighter2.getEffectRole().getAttrById(190) / 10.0f)))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f)) : ((0.66d * Math.pow(0.9d, (fighter2.getEffectRole().getAttrById(190) / 10.0f) - (fighter.getEffectRole().getAttrById(180) / 10.0f))) + (fighter.getEffectRole().getAttrById(810) - fighter2.getEffectRole().getAttrById(820))) + ((fighter.getEffectRole().getAttrById(2130) / 1000.0f) - (fighter2.getEffectRole().getAttrById(2140) / 1000.0f))) - 0.16d) * 0.2d);
            case 347:
                return Double.valueOf(fighter.getEffectRole().getAttrById(150) * 0.1d);
            case 348:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 1.1d);
            case 349:
                return Double.valueOf(iJavaScriptEngine.getDouble("gradea").intValue() * 2.3d);
            case 350:
                return Double.valueOf((-(((fighter.getEffectRole().getAttrById(150) * 2.5d) - fighter2.getEffectRole().getAttrById(160)) + (2 * iJavaScriptEngine.getDouble("gradea").intValue()))) * 0.5d);
            case 351:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.232d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 352:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.377d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 353:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("MonsterLv").intValue() * 0.1d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 354:
                return Double.valueOf(1000 * Math.min(Math.max(iJavaScriptEngine.getDouble("RoleLv").intValue(), iJavaScriptEngine.getDouble("FuBenLv").intValue()), iJavaScriptEngine.getDouble("FuBenLv").intValue() + 9) * 0.1d * (0.7d + (0.1d * iJavaScriptEngine.getDouble("Ring").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 355:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.215d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 356:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.232d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * 0.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 357:
                return Double.valueOf(((iJavaScriptEngine.getDouble("StdMoney").intValue() * 4.2d) / 8.0d) * (((iJavaScriptEngine.getDouble("Ring").intValue() - 1) * 0.3d) + 1.0d) * ((Math.random() * 0.10000000000000009d) + 0.95d));
            case 358:
                return 100;
            case 359:
                return Integer.valueOf(15 + ((iJavaScriptEngine.getDouble("TeamNum").intValue() - 1) * 15) + (iJavaScriptEngine.getDouble("RoleLv").intValue() * 140));
            case 360:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.2d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 361:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.215d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * 0.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 362:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.377d * 3.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 363:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("MonsterLv").intValue() * 0.019d * ((iJavaScriptEngine.getDouble("MonsterNum").intValue() * 0.083d) + (iJavaScriptEngine.getDouble("MasterNum").intValue() * 0.1245d)) * Math.min(Math.max(1.0d - (0.2d * Math.floor(Math.abs(iJavaScriptEngine.getDouble("MonsterLv").intValue() - iJavaScriptEngine.getDouble("RoleLv").intValue()) / 5)), 0.1d), 1.0d) * iJavaScriptEngine.getDouble("IsDbPoint").intValue() * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 364:
                return Integer.valueOf(15 + ((iJavaScriptEngine.getDouble("TeamNum").intValue() - 1) * 15) + (iJavaScriptEngine.getDouble("RoleLv").intValue() * 160));
            case 365:
                return Integer.valueOf(15 + ((iJavaScriptEngine.getDouble("TeamNum").intValue() - 1) * 15) + (iJavaScriptEngine.getDouble("RoleLv").intValue() * 150));
            case 366:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.215d * 2.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 367:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.232d * 2.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 368:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.067d * (0.74d + (0.02d * iJavaScriptEngine.getDouble("Time").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 369:
                return Double.valueOf(9.5d * iJavaScriptEngine.getDouble("RoleLv").intValue());
            case 370:
                return Integer.valueOf(15 + ((iJavaScriptEngine.getDouble("TeamNum").intValue() - 1) * 15) + (iJavaScriptEngine.getDouble("RoleLv").intValue() * 100));
            case 371:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.5d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 372:
                return Double.valueOf(iJavaScriptEngine.getDouble("StdMoney").intValue() * 0.4d * (((iJavaScriptEngine.getDouble("Ring").intValue() - 1) * 0.09d) + 1.0d));
            case 373:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 374:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.2d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 375:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.3d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 376:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.05d * (0.79d + (0.02d * iJavaScriptEngine.getDouble("AnswerCnt").intValue())) * 0.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 377:
                return Double.valueOf((iJavaScriptEngine.getDouble("RoleLv").intValue() * 71.7d) + 500.0d);
            case 378:
                return Integer.valueOf(5000 * iJavaScriptEngine.getDouble("RoleLv").intValue());
            case 379:
                return Double.valueOf(1000.0d * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), ((iJavaScriptEngine.getDouble("FuBenId").intValue() * 10) - 1051) - (Math.floor(iJavaScriptEngine.getDouble("FuBenId").intValue() / 113) * 5.0d)) * 0.35d * (0.75d + (0.05d * iJavaScriptEngine.getDouble("Saveid").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 380:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.1d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 381:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.4d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 382:
                return Integer.valueOf(15 + ((iJavaScriptEngine.getDouble("TeamNum").intValue() - 1) * 15) + (iJavaScriptEngine.getDouble("RoleLv").intValue() * 120));
            case 383:
                return Double.valueOf(((iJavaScriptEngine.getDouble("StdMoney").intValue() * 2) / 10) * ((Math.random() * 0.10000000000000009d) + 0.95d));
            case 384:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.1d * (0.89d + (0.02d * iJavaScriptEngine.getDouble("AnswerCnt").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 385:
                return Integer.valueOf(200 * iJavaScriptEngine.getDouble("RoleLv").intValue());
            case 386:
                return Double.valueOf(((1000 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 0.075d * (1 - iJavaScriptEngine.getDouble("IsDbPoint").intValue())) + (1000 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 0.12d * iJavaScriptEngine.getDouble("IsDbPoint").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)) * (1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())));
            case 387:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.1d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 388:
                return Integer.valueOf(iJavaScriptEngine.getDouble("RoleLv").intValue() * 10);
            case 389:
                return Double.valueOf(((iJavaScriptEngine.getDouble("StdMoney").intValue() * 3.15d) / 8.0d) * (((iJavaScriptEngine.getDouble("Ring").intValue() - 1) * 0.3d) + 1.0d) * ((Math.random() * 0.10000000000000009d) + 0.95d));
            case 390:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.377d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * 0.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 391:
                return Double.valueOf(iJavaScriptEngine.getDouble("StdMoney").intValue() * 2 * ((Math.random() * 0.10000000000000009d) + 0.95d));
            case 392:
                return Double.valueOf(((iJavaScriptEngine.getDouble("StdMoney").intValue() * 2) / 5) * ((Math.random() * 0.10000000000000009d) + 0.95d));
            case 393:
                return Double.valueOf((iJavaScriptEngine.getDouble("RoleLv").intValue() * 71.7d) + 200.0d);
            case 394:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.194d * (0.85d + (0.03d * (((iJavaScriptEngine.getDouble("Time").intValue() - 1) % 9) + 1))) * (0.9d + (0.1d * (Math.floor((iJavaScriptEngine.getDouble("Time").intValue() - 1) / 9) + 1.0d))) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 395:
                return Double.valueOf(1600 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.067d * (0.74d + (0.02d * iJavaScriptEngine.getDouble("Time").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 396:
                return Double.valueOf(((1000 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 0.038d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * (1 - iJavaScriptEngine.getDouble("IsDbPoint").intValue())) + (1000 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 0.098d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * iJavaScriptEngine.getDouble("IsDbPoint").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)) * (1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())));
            case 397:
                return Double.valueOf(((iJavaScriptEngine.getDouble("StdMoney").intValue() * 2) / 15) * ((Math.random() * 0.10000000000000009d) + 0.95d));
            case 398:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.1d * (0.79d + (0.02d * iJavaScriptEngine.getDouble("AnswerCnt").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 399:
                return Double.valueOf(1000 * Math.min(Math.max(iJavaScriptEngine.getDouble("RoleLv").intValue(), iJavaScriptEngine.getDouble("FuBenLv").intValue()), iJavaScriptEngine.getDouble("FuBenLv").intValue() + 9) * 0.2d * (0.7d + (0.1d * iJavaScriptEngine.getDouble("Ring").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 400:
                return Double.valueOf(((iJavaScriptEngine.getDouble("StdMoney").intValue() * 6) / 28.1d) * (((iJavaScriptEngine.getDouble("Ring").intValue() - 1) * 0.09d) + 1.0d) * ((Math.random() * 0.10000000000000009d) + 0.95d));
            case 401:
                return Double.valueOf(250 * iJavaScriptEngine.getDouble("RoleLv").intValue() * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 402:
                return Double.valueOf(4000.0d + (6000.0d * Math.random()));
            case 403:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.4d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 404:
                return Double.valueOf(3000.0d + (5000.0d * Math.random()));
            case 405:
                return Integer.valueOf(100 * iJavaScriptEngine.getDouble("RoleLv").intValue());
            case 406:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.1d * (0.79d + (0.02d * iJavaScriptEngine.getDouble("AnswerCnt").intValue())) * 0.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 407:
                return Double.valueOf((1 + (14 * iJavaScriptEngine.getDouble("IsDbPoint").intValue()) + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * ((iJavaScriptEngine.getDouble("StdMoney").intValue() * 1.5d) / 74.0d) * 2.26d * ((Math.random() * 0.3999999999999999d) + 0.8d));
            case 408:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.194d * 2.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 409:
                return Integer.valueOf(iJavaScriptEngine.getDouble("RoleLv").intValue() * 50);
            case 410:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.1d * ((Math.random() * 0.040000000000000036d) + 0.98d) * 0.5d);
            case 411:
                return Integer.valueOf(500 * iJavaScriptEngine.getDouble("RoleLv").intValue());
            case 412:
                return Double.valueOf((1 + (14 * iJavaScriptEngine.getDouble("IsDbPoint").intValue()) + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * ((iJavaScriptEngine.getDouble("StdMoney").intValue() * 1.5d) / 74.0d) * (((iJavaScriptEngine.getDouble("Ring").intValue() - 1) * 0.09d) + 1.0d) * ((Math.random() * 0.3999999999999999d) + 0.8d));
            case 413:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.194d * 1.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 414:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.05d * (0.79d + (0.02d * iJavaScriptEngine.getDouble("AnswerCnt").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 415:
                return Double.valueOf((15.0d + (Math.random() * 5.0d)) * iJavaScriptEngine.getDouble("RoleLv").intValue());
            case 416:
                return Double.valueOf(1000.0d * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), ((iJavaScriptEngine.getDouble("FuBenId").intValue() * 10) - 1051) - (Math.floor(iJavaScriptEngine.getDouble("FuBenId").intValue() / 113) * 5.0d)) * 0.35d * (0.5d + (0.1d * iJavaScriptEngine.getDouble("Saveid").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 417:
                return Double.valueOf(((iJavaScriptEngine.getDouble("StdMoney").intValue() * 6) / 10) * ((Math.random() * 0.10000000000000009d) + 0.95d));
            case 418:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.194d * 1.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 419:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.1d * (0.89d + (0.02d * iJavaScriptEngine.getDouble("AnswerCnt").intValue())) * 0.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 420:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.717d * 2.0d * 2.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 421:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.775d * 2.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 422:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.717d * 2.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 423:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.94d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 424:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 5 * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 425:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 6.66d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 426:
                return Double.valueOf(1 * Math.min(Math.max(iJavaScriptEngine.getDouble("RoleLv").intValue(), iJavaScriptEngine.getDouble("FuBenLv").intValue()), iJavaScriptEngine.getDouble("FuBenLv").intValue() + 9) * 5 * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 427:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.775d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 428:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.256d * 2.0d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 429:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.775d * 2.0d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 430:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.667d * 2.0d * (3.5d + (0.5d * (Math.floor((iJavaScriptEngine.getDouble("Time").intValue() - 1) / 5) + 1.0d))) * Math.floor(1.0d - ((iJavaScriptEngine.getDouble("Time").intValue() % 5) * 0.2d)) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 431:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 4 * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 432:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 10 * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 433:
                return Integer.valueOf(iJavaScriptEngine.getDouble("RoleLv").intValue() * 5);
            case 434:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.717d * 2.0d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 435:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.256d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 436:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.717d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 437:
                return Double.valueOf(((iJavaScriptEngine.getDouble("MonsterLv").intValue() - 30) * 0.2d) + 4.0d);
            case 438:
                return Integer.valueOf(iJavaScriptEngine.getDouble("MonsterLv").intValue() + 20);
            case 439:
                return Integer.valueOf(15 + ((iJavaScriptEngine.getDouble("TeamNum").intValue() - 1) * 15) + (iJavaScriptEngine.getDouble("RoleLv").intValue() * 5));
            case 440:
                return Integer.valueOf(50 + ((iJavaScriptEngine.getDouble("TeamNum").intValue() - 1) * 50) + (iJavaScriptEngine.getDouble("RoleLv").intValue() * 5));
            case 441:
                return Integer.valueOf(15 + ((iJavaScriptEngine.getDouble("TeamNum").intValue() - 1) * 15) + (iJavaScriptEngine.getDouble("RoleLv").intValue() * 4));
            case 442:
                return Double.valueOf((iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.71d) + 40.0d);
            case 443:
                return Integer.valueOf((iJavaScriptEngine.getDouble("IsDbPoint").intValue() * (iJavaScriptEngine.getDouble("Ring").intValue() - 1) * 1) + 6);
            case 444:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1 * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 445:
                return Integer.valueOf(15 + ((iJavaScriptEngine.getDouble("TeamNum").intValue() - 1) * 15) + (iJavaScriptEngine.getDouble("RoleLv").intValue() * 1));
            case 446:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.256d * 2.0d * 2.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 447:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 0.196d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * iJavaScriptEngine.getDouble("IsDbPoint").intValue() * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)) * (1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())));
            case 448:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.775d * 2.0d * 2.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 449:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 2 * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 450:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 11 * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 451:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 8 * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 452:
                return Integer.valueOf(15 + ((iJavaScriptEngine.getDouble("TeamNum").intValue() - 1) * 15) + (iJavaScriptEngine.getDouble("RoleLv").intValue() * 2));
            case 453:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.3d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 454:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.333d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 455:
                return Integer.valueOf(15 + ((iJavaScriptEngine.getDouble("TeamNum").intValue() - 1) * 15) + (iJavaScriptEngine.getDouble("RoleLv").intValue() * 3));
            case 456:
                return Double.valueOf((iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.71d) + 40.0d);
            case 457:
                return Double.valueOf(1 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.256d * 2.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 458:
                return Double.valueOf((iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.7d) + 20.0d);
            case 459:
                return Double.valueOf(3.4d * iJavaScriptEngine.getDouble("RoleLv").intValue() * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 460:
                return Double.valueOf(2.22d * iJavaScriptEngine.getDouble("RoleLv").intValue() * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 461:
                return Double.valueOf(2.5d * iJavaScriptEngine.getDouble("RoleLv").intValue() * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 462:
                return Double.valueOf(1.38d * iJavaScriptEngine.getDouble("RoleLv").intValue() * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 463:
                return Double.valueOf(Math.max(Math.floor((iJavaScriptEngine.getDouble("Ring").intValue() - 4) / 2), 0.0d));
            case 464:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.278d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 465:
                return Double.valueOf(((((iJavaScriptEngine.getDouble("StdExp").intValue() * 13.333d) / 28.867d) * (((iJavaScriptEngine.getDouble("Ring").intValue() - 1) * 0.08d) + 1.0d)) + (iJavaScriptEngine.getDouble("RoleLv").intValue() * 50)) - 1000.0d);
            case 466:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.678d * 2.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 467:
                return Double.valueOf(((400 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 0.15d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * (1 - iJavaScriptEngine.getDouble("IsDbPoint").intValue())) + (400 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 1.02d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * iJavaScriptEngine.getDouble("IsDbPoint").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)) * (1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())));
            case 468:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.678d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * 0.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 469:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.875d * (0.91d + (0.02d * (((iJavaScriptEngine.getDouble("Ring").intValue() - 1) % 8) + 1))) * (0.58d + (0.04d * (Math.floor((iJavaScriptEngine.getDouble("Ring").intValue() - 1) / 8) + 1.0d))));
            case 470:
                return Double.valueOf((1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())) * (1 + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * ((iJavaScriptEngine.getDouble("StdExp").intValue() * 6.7d) / 15.0d));
            case 471:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 472:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.099d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * 0.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 473:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.7d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 474:
                return Double.valueOf(400 * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 89) * 0.35d * 1.25d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 475:
                return Double.valueOf((175.0d + (Math.random() * 50.0d)) * iJavaScriptEngine.getDouble("RoleLv").intValue());
            case 476:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.099d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 477:
                return Double.valueOf(400.0d * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), ((iJavaScriptEngine.getDouble("FuBenId").intValue() * 10) - 1051) - (Math.floor(iJavaScriptEngine.getDouble("FuBenId").intValue() / 113) * 5.0d)) * 0.35d * (0.5d + (0.1d * iJavaScriptEngine.getDouble("Saveid").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 478:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.5d * (0.79d + (0.02d * iJavaScriptEngine.getDouble("AnswerCnt").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 479:
                return Double.valueOf(400.0d * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), ((iJavaScriptEngine.getDouble("FuBenId").intValue() * 10) - 1051) - (Math.floor(iJavaScriptEngine.getDouble("FuBenId").intValue() / 113) * 5.0d)) * 0.35d * (0.75d + (0.05d * iJavaScriptEngine.getDouble("Saveid").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 480:
                return Double.valueOf((1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())) * (1 + (7 * iJavaScriptEngine.getDouble("IsDbPoint").intValue()) + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * ((iJavaScriptEngine.getDouble("StdExp").intValue() * 10) / 63) * 1.7000000000000002d);
            case 481:
                return Double.valueOf((1 + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * (((((iJavaScriptEngine.getDouble("StdExp").intValue() * 13.333d) / 28.867d) * (((iJavaScriptEngine.getDouble("Ring").intValue() - 1) * 0.08d) + 1.0d)) + (iJavaScriptEngine.getDouble("RoleLv").intValue() * 50)) - 1000.0d));
            case 482:
                return Double.valueOf(400 * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 84) * 0.35d * 1.25d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 483:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.8d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 484:
                return Double.valueOf(800 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.215d * (0.85d + (0.03d * (((iJavaScriptEngine.getDouble("Time").intValue() - 1) % 9) + 1))) * (0.9d + (0.1d * (Math.floor((iJavaScriptEngine.getDouble("Time").intValue() - 1) / 9) + 1.0d))) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 485:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.5d * (0.79d + (0.02d * iJavaScriptEngine.getDouble("AnswerCnt").intValue())) * 0.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 486:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.099d * 3.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 487:
                return Double.valueOf((1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())) * (1 + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * (((iJavaScriptEngine.getDouble("StdExp").intValue() * 2.14d) * 7.0d) / 8.0d) * (((iJavaScriptEngine.getDouble("Ring").intValue() - 1) * 0.3d) + 1.0d));
            case 488:
                return Double.valueOf(400.0d * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), ((iJavaScriptEngine.getDouble("FuBenId").intValue() * 10) - 1051) - (Math.floor(iJavaScriptEngine.getDouble("FuBenId").intValue() / 113) * 5.0d)) * 0.35d * (0.75d + (0.05d * iJavaScriptEngine.getDouble("Saveid").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 489:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1 * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 490:
                return Double.valueOf((1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())) * (1 + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * ((iJavaScriptEngine.getDouble("StdExp").intValue() * 6.7d) / 5.0d));
            case 491:
                return Double.valueOf((1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())) * (1 + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * iJavaScriptEngine.getDouble("StdExp").intValue() * 6);
            case 492:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.25d * (0.89d + (0.02d * iJavaScriptEngine.getDouble("AnswerCnt").intValue())) * 0.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 493:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.628d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * 0.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 494:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.25d * (0.79d + (0.02d * iJavaScriptEngine.getDouble("AnswerCnt").intValue())) * 0.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 495:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.694d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 496:
                return Double.valueOf(400 * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 69) * 0.35d * 1.25d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 497:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.25d * (0.89d + (0.02d * iJavaScriptEngine.getDouble("AnswerCnt").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 498:
                return Integer.valueOf(iJavaScriptEngine.getDouble("RoleLv").intValue() * 200);
            case 499:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1 * (0.74d + (0.02d * iJavaScriptEngine.getDouble("Time").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 500:
                return Double.valueOf(400 * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 74) * 0.35d * 1.25d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 501:
                return Double.valueOf((1 + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * iJavaScriptEngine.getDouble("StdExp").intValue() * 0.2d);
            case 502:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.628d * 0.6d);
            case 503:
                return Double.valueOf(400 * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 49) * 0.35d * 1.25d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 504:
                return Double.valueOf((1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())) * (1 + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * ((iJavaScriptEngine.getDouble("StdExp").intValue() * 6.6667d) / 8.0d) * (((iJavaScriptEngine.getDouble("Ring").intValue() - 1) * 0.3d) + 1.0d));
            case 505:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.678d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 506:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.628d * 2.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 507:
                return Double.valueOf(2000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.694d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 508:
                return Double.valueOf(400 * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 94) * 0.35d * 1.25d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 509:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.628d * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPCnt").intValue() + 1))) * (0.95d + (0.05d * (iJavaScriptEngine.getDouble("PVPTargetCnt").intValue() + 1))) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 510:
                return Double.valueOf(1000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.694d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 511:
                return Double.valueOf(((iJavaScriptEngine.getDouble("StdExp").intValue() * 5) / 10) * ((Math.random() * 0.3999999999999999d) + 0.8d));
            case 512:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.667d * ((Math.random() * 0.040000000000000036d) + 0.98d) * 0.5d);
            case 513:
                return Double.valueOf(400 * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 79) * 0.35d * 1.25d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 514:
                return Double.valueOf(((1000 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 0.15d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * (1 - iJavaScriptEngine.getDouble("IsDbPoint").intValue())) + (400 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 1.02d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * iJavaScriptEngine.getDouble("IsDbPoint").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)) * (1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())));
            case 515:
                return Double.valueOf(((400 * iJavaScriptEngine.getDouble("MonsterLv").intValue() * 0.017d * ((iJavaScriptEngine.getDouble("MonsterNum").intValue() * 0.083d) + (iJavaScriptEngine.getDouble("MasterNum").intValue() * 0.1245d)) * Math.min(Math.max(1.0d - (0.2d * Math.floor(Math.abs(iJavaScriptEngine.getDouble("MonsterLv").intValue() - iJavaScriptEngine.getDouble("RoleLv").intValue()) / 5)), 0.1d), 1.0d) * (1 - iJavaScriptEngine.getDouble("IsDbPoint").intValue())) + (400 * iJavaScriptEngine.getDouble("MonsterLv").intValue() * 0.13d * ((iJavaScriptEngine.getDouble("MonsterNum").intValue() * 0.083d) + (iJavaScriptEngine.getDouble("MasterNum").intValue() * 0.1245d)) * Math.min(Math.max(1.0d - (0.2d * Math.floor(Math.abs(iJavaScriptEngine.getDouble("MonsterLv").intValue() - iJavaScriptEngine.getDouble("RoleLv").intValue()) / 5)), 0.1d), 1.0d) * iJavaScriptEngine.getDouble("IsDbPoint").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 516:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.6d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 517:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 2.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 518:
                return Double.valueOf((1 + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * ((iJavaScriptEngine.getDouble("StdExp").intValue() * 0.83d) / 14.5d) * (((iJavaScriptEngine.getDouble("Ring").intValue() - 1) * 0.1d) + 1.0d));
            case 519:
                return Integer.valueOf(iJavaScriptEngine.getDouble("RoleLv").intValue() * 100);
            case 520:
                return Double.valueOf(400 * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 59) * 0.35d * 1.25d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 521:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.833d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 522:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.25d * (0.79d + (0.02d * iJavaScriptEngine.getDouble("AnswerCnt").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 523:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1 * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 524:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.656d * (0.82d + (0.04d * (((iJavaScriptEngine.getDouble("Ring").intValue() - 1) % 8) + 1))) * (0.58d + (0.04d * (Math.floor((iJavaScriptEngine.getDouble("Ring").intValue() - 1) / 8) + 1.0d))));
            case 525:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.25d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 526:
                return Double.valueOf(((400 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 0.3d * (1 - iJavaScriptEngine.getDouble("IsDbPoint").intValue())) + (400 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 1.244d * iJavaScriptEngine.getDouble("IsDbPoint").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)) * (1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())));
            case 527:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.215d * 0.91d);
            case 528:
                return Double.valueOf(((400 * iJavaScriptEngine.getDouble("MonsterLv").intValue() * 0.011d * ((iJavaScriptEngine.getDouble("MonsterNum").intValue() * 0.083d) + (iJavaScriptEngine.getDouble("MasterNum").intValue() * 0.1245d)) * Math.min(Math.max(1.0d - (0.2d * Math.floor(Math.abs(iJavaScriptEngine.getDouble("MonsterLv").intValue() - iJavaScriptEngine.getDouble("RoleLv").intValue()) / 5)), 0.1d), 1.0d) * (1 - iJavaScriptEngine.getDouble("IsDbPoint").intValue())) + (400 * iJavaScriptEngine.getDouble("MonsterLv").intValue() * 0.13d * ((iJavaScriptEngine.getDouble("MonsterNum").intValue() * 0.083d) + (iJavaScriptEngine.getDouble("MasterNum").intValue() * 0.1245d)) * Math.min(Math.max(1.0d - (0.2d * Math.floor(Math.abs(iJavaScriptEngine.getDouble("MonsterLv").intValue() - iJavaScriptEngine.getDouble("RoleLv").intValue()) / 5)), 0.1d), 1.0d) * iJavaScriptEngine.getDouble("IsDbPoint").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 529:
                return Double.valueOf(((iJavaScriptEngine.getDouble("StdExp").intValue() * 0.83d) / 14.5d) * (((iJavaScriptEngine.getDouble("Ring").intValue() - 1) * 0.1d) + 1.0d));
            case 530:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.215d * (0.85d + (0.03d * (((iJavaScriptEngine.getDouble("Time").intValue() - 1) % 9) + 1))) * (0.9d + (0.1d * (Math.floor((iJavaScriptEngine.getDouble("Time").intValue() - 1) / 9) + 1.0d))) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 531:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.4d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 532:
                return Double.valueOf(400 * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 99) * 0.35d * 1.25d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 533:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.099d * 0.6d);
            case 534:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.215d * 2.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 535:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.017d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 536:
                return Double.valueOf((((iJavaScriptEngine.getDouble("StdExp").intValue() * 7) * 2.86d) / 168.0d) * 8.0d);
            case 537:
                return Double.valueOf(3200 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1 * (0.74d + (0.02d * iJavaScriptEngine.getDouble("Time").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 538:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.678d * 0.6d);
            case 539:
                return Double.valueOf(400 * Math.min(Math.max(iJavaScriptEngine.getDouble("RoleLv").intValue(), iJavaScriptEngine.getDouble("FuBenLv").intValue()), iJavaScriptEngine.getDouble("FuBenLv").intValue() + 9) * 2 * (0.7d + (0.1d * iJavaScriptEngine.getDouble("Ring").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)) * (1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())));
            case 540:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.2d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 541:
                return Double.valueOf((105.0d + (Math.random() * 30.0d)) * iJavaScriptEngine.getDouble("RoleLv").intValue());
            case 542:
                return Double.valueOf((1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())) * (1 + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * ((iJavaScriptEngine.getDouble("StdExp").intValue() * 6.7d) / 10.0d) * (((iJavaScriptEngine.getDouble("TeamNum").intValue() - 1) * 0.05d) + 1.0d));
            case 543:
                return Integer.valueOf((1 + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * iJavaScriptEngine.getDouble("StdExp").intValue() * 5);
            case 544:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.215d * 1.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 545:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1.215d * 1.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 546:
                return Double.valueOf(400.0d * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), ((iJavaScriptEngine.getDouble("FuBenId").intValue() * 10) - 1051) - (Math.floor(iJavaScriptEngine.getDouble("FuBenId").intValue() / 113) * 5.0d)) * 0.0392d * (0.4d + (0.1d * iJavaScriptEngine.getDouble("Saveid").intValue())));
            case 547:
                return Double.valueOf(1600 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 1 * (0.74d + (0.02d * iJavaScriptEngine.getDouble("Time").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 548:
                return Double.valueOf((1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())) * (1 + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * iJavaScriptEngine.getDouble("StdExp").intValue() * 6.7d);
            case 549:
                return Double.valueOf(400 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 2.222d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 550:
                return Double.valueOf((1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())) * (1 + (7 * iJavaScriptEngine.getDouble("IsDbPoint").intValue()) + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * ((iJavaScriptEngine.getDouble("StdExp").intValue() * 10) / 63) * (((iJavaScriptEngine.getDouble("Ring").intValue() - 1) * 0.05d) + 1.0d));
            case 551:
                return Double.valueOf(400 * Math.min(Math.max(iJavaScriptEngine.getDouble("RoleLv").intValue(), iJavaScriptEngine.getDouble("FuBenLv").intValue()), iJavaScriptEngine.getDouble("FuBenLv").intValue() + 9) * 1 * (0.7d + (0.1d * iJavaScriptEngine.getDouble("Ring").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)) * (1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())));
            case 552:
                return Double.valueOf(((500 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 0.15d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * (1 - iJavaScriptEngine.getDouble("IsDbPoint").intValue())) + (400 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 1.02d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * iJavaScriptEngine.getDouble("IsDbPoint").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)) * (1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())));
            case 553:
                return Integer.valueOf(iJavaScriptEngine.getDouble("Ring").intValue() + 3);
            case 554:
                return Integer.valueOf(iJavaScriptEngine.getDouble("Ring").intValue() + 1);
            case 555:
                return Double.valueOf((2200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 89) / 10) * 440.0d) + 200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 89) / 10) * 40.0d * 10.0d)) * 2.0d);
            case 556:
                return Double.valueOf((2200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), ((iJavaScriptEngine.getDouble("FuBenId").intValue() * 10) - 1051) - (Math.floor(iJavaScriptEngine.getDouble("FuBenId").intValue() / 113) * 5.0d)) / 10.0d) * 440.0d) + 200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), ((iJavaScriptEngine.getDouble("FuBenId").intValue() * 10) - 1051) - (Math.floor(iJavaScriptEngine.getDouble("FuBenId").intValue() / 113) * 5.0d)) / 10.0d) * 40.0d * iJavaScriptEngine.getDouble("Saveid").intValue())) * 2.0d);
            case 557:
                return Double.valueOf((2200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 74) / 10) * 440.0d) + 200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 74) / 10) * 40.0d * 10.0d)) * 2.0d);
            case 558:
                return Double.valueOf((2200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 94) / 10) * 440.0d) + 200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 94) / 10) * 40.0d * 10.0d)) * 2.0d);
            case 559:
                return Double.valueOf(2200.0d + (Math.floor(iJavaScriptEngine.getDouble("RoleLv").intValue() / 10) * 440.0d) + 200.0d + (Math.floor(iJavaScriptEngine.getDouble("RoleLv").intValue() / 10) * 40.0d * (iJavaScriptEngine.getDouble("Ring").intValue() - 1)));
            case 560:
                return Double.valueOf(2200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), ((iJavaScriptEngine.getDouble("FuBenId").intValue() * 10) - 1051) - (Math.floor(iJavaScriptEngine.getDouble("FuBenId").intValue() / 113) * 5.0d)) / 10.0d) * 440.0d) + 200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), ((iJavaScriptEngine.getDouble("FuBenId").intValue() * 10) - 1051) - (Math.floor(iJavaScriptEngine.getDouble("FuBenId").intValue() / 113) * 5.0d)) / 10.0d) * 40.0d * iJavaScriptEngine.getDouble("Saveid").intValue()));
            case 561:
                return Double.valueOf((2200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 99) / 10) * 440.0d) + 200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 99) / 10) * 40.0d * 10.0d)) * 2.0d);
            case 562:
                return Double.valueOf((2200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 49) / 10) * 440.0d) + 200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 49) / 10) * 40.0d * 10.0d)) * 2.0d);
            case 563:
                return Double.valueOf(5800.0d + (Math.floor(iJavaScriptEngine.getDouble("RoleLv").intValue() / 10) * 440.0d) + 200.0d + (Math.floor(iJavaScriptEngine.getDouble("RoleLv").intValue() / 10) * 40.0d * (iJavaScriptEngine.getDouble("Ring").intValue() - 1)));
            case 564:
                return Double.valueOf((2200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 59) / 10) * 440.0d) + 200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 59) / 10) * 40.0d * 10.0d)) * 2.0d);
            case 565:
                return Double.valueOf((2200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 84) / 10) * 440.0d) + 200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 84) / 10) * 40.0d * 10.0d)) * 2.0d);
            case 566:
                return Double.valueOf((2200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 69) / 10) * 440.0d) + 200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 69) / 10) * 40.0d * 10.0d)) * 2.0d);
            case 567:
                return Double.valueOf((2200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 79) / 10) * 440.0d) + 200.0d + (Math.floor(Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), 79) / 10) * 40.0d * 10.0d)) * 2.0d);
            case 568:
                return Integer.valueOf(iJavaScriptEngine.getDouble("SwXs").intValue() * 1);
            case 569:
                return Double.valueOf(5000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.05d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 570:
                return Double.valueOf(5000.0d * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), ((iJavaScriptEngine.getDouble("FuBenId").intValue() * 10) - 1051) - (Math.floor(iJavaScriptEngine.getDouble("FuBenId").intValue() / 113) * 5.0d)) * 0.058d * (0.75d + (0.05d * iJavaScriptEngine.getDouble("Saveid").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 571:
                return Double.valueOf(5000.0d * Math.min(iJavaScriptEngine.getDouble("RoleLv").intValue(), ((iJavaScriptEngine.getDouble("FuBenId").intValue() * 10) - 1051) - (Math.floor(iJavaScriptEngine.getDouble("FuBenId").intValue() / 113) * 5.0d)) * 0.058d * (0.5d + (0.1d * iJavaScriptEngine.getDouble("Saveid").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 572:
                return Double.valueOf(5000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.1d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 573:
                return Double.valueOf(5000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.111d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 574:
                return Double.valueOf(5000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.05d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 575:
                return Double.valueOf(((5000 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 0.025d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * (1 - iJavaScriptEngine.getDouble("IsDbPoint").intValue())) + (5000 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 0.102d * (0.78d + (0.04d * iJavaScriptEngine.getDouble("Ring").intValue())) * iJavaScriptEngine.getDouble("IsDbPoint").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)) * (1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())));
            case 576:
                return Double.valueOf((1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())) * (1.0d + (1.5d * iJavaScriptEngine.getDouble("IsDbPoint").intValue()) + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * (((5000 * iJavaScriptEngine.getDouble("RoleLv").intValue()) * 1.25d) / 50.0d));
            case 577:
                return Double.valueOf(5000 * iJavaScriptEngine.getDouble("MonsterLv").intValue() * 0.05d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 578:
                return Double.valueOf(5000 * Math.min(Math.max(iJavaScriptEngine.getDouble("RoleLv").intValue(), iJavaScriptEngine.getDouble("FuBenLv").intValue()), iJavaScriptEngine.getDouble("FuBenLv").intValue() + 9) * 0.2d * (0.7d + (0.1d * iJavaScriptEngine.getDouble("Ring").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 579:
                return Double.valueOf(((5000 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 0.05d * (1 - iJavaScriptEngine.getDouble("IsDbPoint").intValue())) + (5000 * iJavaScriptEngine.getDouble("TeamLv").intValue() * 0.124d * iJavaScriptEngine.getDouble("IsDbPoint").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)) * (1.0d + (0.05d * iJavaScriptEngine.getDouble("IsTL").intValue())));
            case 580:
                return Double.valueOf(5000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.075d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 581:
                return Double.valueOf(5000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.049d * 1.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 582:
                return Double.valueOf(5000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.049d * (0.85d + (0.03d * (((iJavaScriptEngine.getDouble("Time").intValue() - 1) % 9) + 1))) * (0.9d + (0.1d * (Math.floor((iJavaScriptEngine.getDouble("Time").intValue() - 1) / 9) + 1.0d))) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 583:
                return Double.valueOf(((5000 * iJavaScriptEngine.getDouble("MonsterLv").intValue() * 0.017d * ((iJavaScriptEngine.getDouble("MonsterNum").intValue() * 0.083d) + (iJavaScriptEngine.getDouble("MasterNum").intValue() * 0.1245d)) * Math.min(Math.max(1.0d - (0.2d * Math.floor(Math.abs(iJavaScriptEngine.getDouble("MonsterLv").intValue() - iJavaScriptEngine.getDouble("RoleLv").intValue()) / 5)), 0.1d), 1.0d) * (1 - iJavaScriptEngine.getDouble("IsDbPoint").intValue())) + (5000 * iJavaScriptEngine.getDouble("MonsterLv").intValue() * 0.02d * ((iJavaScriptEngine.getDouble("MonsterNum").intValue() * 0.083d) + (iJavaScriptEngine.getDouble("MasterNum").intValue() * 0.1245d)) * Math.min(Math.max(1.0d - (0.2d * Math.floor(Math.abs(iJavaScriptEngine.getDouble("MonsterLv").intValue() - iJavaScriptEngine.getDouble("RoleLv").intValue()) / 5)), 0.1d), 1.0d) * iJavaScriptEngine.getDouble("IsDbPoint").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 584:
                return Double.valueOf(5000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.1d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 585:
                return Double.valueOf(5000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.1d * (0.74d + (0.02d * iJavaScriptEngine.getDouble("Time").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 586:
                return Integer.valueOf(((iJavaScriptEngine.getDouble("RoleLv").intValue() * 100) * 30) / 5);
            case 587:
                return Double.valueOf(5000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.15d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 588:
                return Double.valueOf(((5000 * iJavaScriptEngine.getDouble("MonsterLv").intValue() * 0.011d * ((iJavaScriptEngine.getDouble("MonsterNum").intValue() * 0.083d) + (iJavaScriptEngine.getDouble("MasterNum").intValue() * 0.1245d)) * Math.min(Math.max(1.0d - (0.2d * Math.floor(Math.abs(iJavaScriptEngine.getDouble("MonsterLv").intValue() - iJavaScriptEngine.getDouble("RoleLv").intValue()) / 5)), 0.1d), 1.0d) * (1 - iJavaScriptEngine.getDouble("IsDbPoint").intValue())) + (5000 * iJavaScriptEngine.getDouble("MonsterLv").intValue() * 0.02d * ((iJavaScriptEngine.getDouble("MonsterNum").intValue() * 0.083d) + (iJavaScriptEngine.getDouble("MasterNum").intValue() * 0.1245d)) * Math.min(Math.max(1.0d - (0.2d * Math.floor(Math.abs(iJavaScriptEngine.getDouble("MonsterLv").intValue() - iJavaScriptEngine.getDouble("RoleLv").intValue()) / 5)), 0.1d), 1.0d) * iJavaScriptEngine.getDouble("IsDbPoint").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 589:
                return Double.valueOf(5000 * Math.min(Math.max(iJavaScriptEngine.getDouble("RoleLv").intValue(), iJavaScriptEngine.getDouble("FuBenLv").intValue()), iJavaScriptEngine.getDouble("FuBenLv").intValue() + 9) * 0.1d * (0.7d + (0.1d * iJavaScriptEngine.getDouble("Ring").intValue())) * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 590:
                return Double.valueOf((1 + iJavaScriptEngine.getDouble("IsSerMul").intValue()) * (((5000 * iJavaScriptEngine.getDouble("RoleLv").intValue()) * 0.5d) / 20.0d));
            case 591:
                return Double.valueOf(5000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.05d * ((Math.random() * 0.040000000000000036d) + 0.98d) * 0.5d);
            case 592:
                return Double.valueOf(5000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.049d * 2.0d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 593:
                return Double.valueOf(5000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.049d * 1.5d * ((Math.random() * 0.040000000000000036d) + 0.98d));
            case 594:
                return Double.valueOf(500 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.05d * ((Math.random() * 0.040000000000000036d) + 0.98d) * 0.5d);
            case 595:
                return Double.valueOf(5000 * iJavaScriptEngine.getDouble("RoleLv").intValue() * 0.125d * ((Math.random() * 0.040000000000000036d) + 0.98d) * (1.0d - (Math.min(5 - iJavaScriptEngine.getDouble("TeamNum").intValue(), 2) * 0.15d)));
            case 596:
                return Double.valueOf(Math.min(Math.max(iJavaScriptEngine.getDouble("rolenum").intValue() * 0.004d, 8.0d), 16.0d));
            case 597:
                return Double.valueOf(Math.min(Math.max(iJavaScriptEngine.getDouble("rolenum").intValue() * 0.001d, 2.0d), 4.0d) + ((iJavaScriptEngine.getDouble("ServerLv").intValue() - 50) * 0.2d));
            case 598:
                return null;
            case 599:
                return Double.valueOf(Math.floor(((Math.min(Math.max(Math.floor((iJavaScriptEngine.getDouble("ServerLv").intValue() - 50) * 0.2d), 0.0d), 3.0d) * 2.0d) + 3.0d) * Math.min(Math.max(iJavaScriptEngine.getDouble("rolenum").intValue() * 5.0E-4d, 1.0d), 2.0d)));
            case 600:
                return Double.valueOf(Math.floor(((Math.min(Math.max(Math.floor((iJavaScriptEngine.getDouble("ServerLv").intValue() - 40) * 0.2d), 0.0d), 4.0d) * 3.0d) + 6.0d) * Math.min(Math.max(iJavaScriptEngine.getDouble("rolenum").intValue() * 5.0E-4d, 1.0d), 2.0d)));
            case 601:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94023_").doubleValue() >= 1.0d);
            case 602:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94024_").doubleValue() >= 1.0d);
            case 603:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94029_").doubleValue() >= 1.0d);
            case 604:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94033_").doubleValue() >= 2.0d);
            case 605:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94034_").doubleValue() < 1.0d);
            case 606:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94046_").doubleValue() < 1.0d && iJavaScriptEngine.getDouble("_94045_").doubleValue() >= 3.0d);
            case 607:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94031_").doubleValue() >= 3.0d);
            case 608:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94035_").doubleValue() >= 1.0d);
            case 609:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94027_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_94053_").doubleValue() < 1.0d);
            case 610:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94036_").doubleValue() >= 2.0d);
            case 611:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94040_").doubleValue() < 1.0d);
            case 612:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94039_").doubleValue() >= 1.0d);
            case 613:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94038_").doubleValue() >= 2.0d || iJavaScriptEngine.getDouble("_94028_").doubleValue() >= 1.0d);
            case 614:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94056_").doubleValue() >= 2.0d);
            case 615:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94054_").doubleValue() >= 1.0d);
            case 616:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94026_").doubleValue() >= 1.0d);
            case 617:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94001_").doubleValue() >= 1.0d);
            case 618:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94002_").doubleValue() >= 1.0d);
            case 619:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94003_").doubleValue() >= 1.0d);
            case 620:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94004_").doubleValue() >= 1.0d);
            case 621:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94005_").doubleValue() >= 5.0d);
            case 622:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94005_").doubleValue() >= 3.0d);
            case 623:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94005_").doubleValue() >= 1.0d);
            case 624:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94006_").doubleValue() < 3.0d);
            case 625:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94007_").doubleValue() >= 2.0d && iJavaScriptEngine.getDouble("_94011_").doubleValue() >= 1.0d);
            case 626:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94007_").doubleValue() >= 2.0d);
            case 627:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94008_").doubleValue() >= 1.0d);
            case 628:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94009_").doubleValue() < 1.0d);
            case 629:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94012_").doubleValue() >= 2.0d);
            case 630:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94013_").doubleValue() >= 1.0d);
            case 631:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94016_").doubleValue() >= 1.0d);
            case 632:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94014_").doubleValue() >= 1.0d);
            case 633:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94015_").doubleValue() >= 1.0d);
            case 634:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94017_").doubleValue() < 1.0d);
            case 635:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94006_").doubleValue() < 3.0d && iJavaScriptEngine.getDouble("_94005_").doubleValue() >= 1.0d);
            case 636:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94003_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_94006_").doubleValue() >= 3.0d);
            case 637:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94003_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_94006_").doubleValue() < 3.0d);
            case 638:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94019_").doubleValue() < 1.0d);
            case 639:
                return Boolean.valueOf(((Float) fighter.getFighterBean().getInitattrs().get(1010)).floatValue() >= 0.0f);
            case 640:
                return Boolean.valueOf(((Float) fighter.getFighterBean().getInitattrs().get(1010)).floatValue() >= 3.0f);
            case 641:
                return Boolean.valueOf(((Float) fighter.getFighterBean().getInitattrs().get(1010)).floatValue() >= 4.0f);
            case 642:
                return Boolean.valueOf(((Float) fighter.getFighterBean().getInitattrs().get(1010)).floatValue() >= 5.0f);
            case 643:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94020_").doubleValue() >= 1.0d);
            case 644:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94021_").doubleValue() >= 1.0d);
            case 645:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94022_").doubleValue() >= 1.0d);
            case 646:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94025_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_94029_").doubleValue() >= 1.0d);
            case 647:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94025_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_94030_").doubleValue() >= 3.0d);
            case 648:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94025_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_94030_").doubleValue() == 2.0d);
            case 649:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94025_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_94030_").doubleValue() == 1.0d);
            case 650:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94030_").doubleValue() >= 3.0d);
            case 651:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94030_").doubleValue() == 2.0d && iJavaScriptEngine.getDouble("_94029_").doubleValue() >= 1.0d);
            case 652:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94030_").doubleValue() == 2.0d);
            case 653:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94030_").doubleValue() == 1.0d);
            case 654:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94044_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_94057_").doubleValue() < 1.0d);
            case 655:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94044_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_94057_").doubleValue() >= 1.0d);
            case 656:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94037_").doubleValue() >= 1.0d);
            case 657:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94038_").doubleValue() >= 2.0d);
            case 658:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94028_").doubleValue() >= 1.0d);
            case 659:
                return Boolean.valueOf(((Float) fighter.getFighterBean().getInitattrs().get(1010)).floatValue() < 1.0f);
            case 660:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99065_").doubleValue() >= 4.0d);
            case 661:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94044_").doubleValue() >= 1.0d);
            case 662:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95001_").doubleValue() >= 1.0d);
            case 663:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95002_").doubleValue() == 1.0d);
            case 664:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95009_").doubleValue() == 1.0d);
            case 665:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95013_").doubleValue() == 1.0d);
            case 666:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95010_").doubleValue() >= 1.0d);
            case 667:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() <= 1.0d);
            case 668:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95017_").doubleValue() == 1.0d);
            case 669:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95018_").doubleValue() <= 1.0d);
            case 670:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95020_").doubleValue() == 1.0d);
            case 671:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95021_").doubleValue() == 1.0d);
            case 672:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95024_").doubleValue() <= 0.0d);
            case 673:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95028_").doubleValue() == 1.0d);
            case 674:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95025_").doubleValue() == 1.0d);
            case 675:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95030_").doubleValue() >= 1.0d);
            case 676:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95035_").doubleValue() >= 1.0d);
            case 677:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95036_").doubleValue() >= 1.0d);
            case 678:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95039_").doubleValue() < 1.0d);
            case 679:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95038_").doubleValue() >= 1.0d);
            case 680:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95037_").doubleValue() >= 2.0d || iJavaScriptEngine.getDouble("_95040_").doubleValue() >= 1.0d);
            case 681:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95041_").doubleValue() >= 2.0d);
            case 682:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95042_").doubleValue() >= 1.0d);
            case 683:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95043_").doubleValue() >= 3.0d);
            case 684:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95044_").doubleValue() >= 1.0d);
            case 685:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95045_").doubleValue() >= 1.0d);
            case 686:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95046_").doubleValue() <= 2.0d);
            case 687:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95047_").doubleValue() >= 2.0d);
            case 688:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95048_").doubleValue() >= 1.0d);
            case 689:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95049_").doubleValue() >= 2.0d || iJavaScriptEngine.getDouble("_95054_").doubleValue() >= 1.0d);
            case 690:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95050_").doubleValue() >= 1.0d);
            case 691:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95051_").doubleValue() >= 2.0d);
            case 692:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95052_").doubleValue() >= 2.0d);
            case 693:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95053_").doubleValue() == 1.0d);
            case 694:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95041_").doubleValue() >= 3.0d);
            case 695:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95055_").doubleValue() >= 1.0d);
            case 696:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95058_").doubleValue() >= 1.0d);
            case 697:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95059_").doubleValue() >= 1.0d);
            case 698:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95052_").doubleValue() >= 1.0d);
            case 699:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95060_").doubleValue() >= 1.0d);
            case 700:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95018_").doubleValue() == 1.0d);
            case 701:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95057_").doubleValue() == 1.0d);
            case 702:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95062_").doubleValue() == 1.0d);
            case 703:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95073_").doubleValue() == 1.0d);
            case 704:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95063_").doubleValue() == 1.0d);
            case 705:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95057_").doubleValue() < 1.0d);
            case 706:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95064_").doubleValue() >= 2.0d);
            case 707:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95065_").doubleValue() >= 3.0d);
            case 708:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95074_").doubleValue() == 1.0d);
            case 709:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95075_").doubleValue() == 1.0d);
            case 710:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95076_").doubleValue() == 1.0d);
            case 711:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95046_").doubleValue() <= 1.0d);
            case 712:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95077_").doubleValue() >= 2.0d);
            case 713:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95078_").doubleValue() < 1.0d);
            case 714:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95080_").doubleValue() == 1.0d);
            case 715:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95079_").doubleValue() < 1.0d);
            case 716:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95081_").doubleValue() < 1.0d);
            case 717:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95082_").doubleValue() >= 2.0d);
            case 718:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95086_").doubleValue() >= 1.0d);
            case 719:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95083_").doubleValue() == 1.0d);
            case 720:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95084_").doubleValue() < 1.0d);
            case 721:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95085_").doubleValue() >= 2.0d && iJavaScriptEngine.getDouble("_95089_").doubleValue() < 1.0d);
            case 722:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95086_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_95087_").doubleValue() < 1.0d);
            case 723:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95082_").doubleValue() >= 3.0d);
            case 724:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95057_").doubleValue() >= 1.0d);
            case 725:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95088_").doubleValue() <= 0.0d);
            case 726:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96001_").doubleValue() / iJavaScriptEngine.getDouble("_96002_").doubleValue() >= 0.2d);
            case 727:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96001_").doubleValue() / iJavaScriptEngine.getDouble("_96002_").doubleValue() < 0.2d);
            case 728:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96004_").doubleValue() >= 4.0d);
            case 729:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96003_").doubleValue() >= 1.0d);
            case 730:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96124_").doubleValue() <= 0.01d);
            case 731:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96015_").doubleValue() == 1.0d);
            case 732:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96016_").doubleValue() >= 1.0d || iJavaScriptEngine.getDouble("_96018_").doubleValue() >= 1.0d);
            case 733:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96016_").doubleValue() < 1.0d && iJavaScriptEngine.getDouble("_96018_").doubleValue() < 1.0d);
            case 734:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96017_").doubleValue() < 1.0d);
            case 735:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96107_").doubleValue() >= 1.0d);
            case 736:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96103_").doubleValue() >= 1.0d);
            case 737:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96108_").doubleValue() >= 1.0d);
            case 738:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96106_").doubleValue() >= 1.0d);
            case 739:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96104_").doubleValue() >= 1.0d);
            case 740:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96105_").doubleValue() >= 1.0d);
            case 741:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96109_").doubleValue() >= 1.0d);
            case 742:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96110_").doubleValue() >= 1.0d);
            case 743:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96111_").doubleValue() >= 1.0d);
            case 744:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96101_").doubleValue() == 1.0d);
            case 745:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96102_").doubleValue() >= 1.0d);
            case 746:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96113_").doubleValue() < 1.0d);
            case 747:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96120_").doubleValue() >= 1.0d);
            case 748:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96121_").doubleValue() >= 1.0d);
            case 749:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96122_").doubleValue() < 1.0d);
            case 750:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96123_").doubleValue() >= 1.0d);
            case 751:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99002_").doubleValue() < 1.0d);
            case 752:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96301_").doubleValue() >= 1.0d);
            case 753:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96302_").doubleValue() >= 1.0d);
            case 754:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96301_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96307_").doubleValue() < 1.0d);
            case 755:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96301_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96308_").doubleValue() < 1.0d);
            case 756:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96301_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96309_").doubleValue() < 1.0d);
            case 757:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96301_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96310_").doubleValue() < 1.0d);
            case 758:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96301_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96311_").doubleValue() < 1.0d);
            case 759:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96301_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96362_").doubleValue() < 1.0d);
            case 760:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96301_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96363_").doubleValue() < 1.0d);
            case 761:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96301_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96364_").doubleValue() < 1.0d);
            case 762:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96301_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96365_").doubleValue() < 1.0d);
            case 763:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96301_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96366_").doubleValue() < 1.0d);
            case 764:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96304_").doubleValue() < 1.0d);
            case 765:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96351_").doubleValue() >= 1.0d);
            case 766:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96352_").doubleValue() >= 1.0d);
            case 767:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96351_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96357_").doubleValue() < 1.0d);
            case 768:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96351_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96358_").doubleValue() < 1.0d);
            case 769:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96351_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96359_").doubleValue() < 1.0d);
            case 770:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96351_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96360_").doubleValue() < 1.0d);
            case 771:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96351_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96361_").doubleValue() < 1.0d);
            case 772:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96351_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96362_").doubleValue() < 1.0d);
            case 773:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96351_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96363_").doubleValue() < 1.0d);
            case 774:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96351_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96364_").doubleValue() < 1.0d);
            case 775:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96351_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96365_").doubleValue() < 1.0d);
            case 776:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96351_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96366_").doubleValue() < 1.0d);
            case 777:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96354_").doubleValue() < 1.0d);
            case 778:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96401_").doubleValue() == 1.0d);
            case 779:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96402_").doubleValue() >= 1.0d || iJavaScriptEngine.getDouble("_96403_").doubleValue() >= 1.0d);
            case 780:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_97001_").doubleValue() / iJavaScriptEngine.getDouble("_97002_").doubleValue() >= 0.2d);
            case 781:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_97001_").doubleValue() / iJavaScriptEngine.getDouble("_97002_").doubleValue() < 0.2d);
            case 782:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_97003_").doubleValue() >= 1.0d);
            case 783:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_97004_").doubleValue() >= 4.0d);
            case 784:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_97005_").doubleValue() == 1.0d);
            case 785:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96200_").doubleValue() >= 4.0d);
            case 786:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96201_").doubleValue() >= 4.0d);
            case 787:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96202_").doubleValue() >= 4.0d);
            case 788:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96203_").doubleValue() >= 4.0d);
            case 789:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96204_").doubleValue() >= 1.0d);
            case 790:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96205_").doubleValue() >= 1.0d);
            case 791:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96206_").doubleValue() >= 4.0d);
            case 792:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96207_").doubleValue() >= 4.0d);
            case 793:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96208_").doubleValue() < 3.0d);
            case 794:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96209_").doubleValue() >= 1.0d);
            case 795:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96210_").doubleValue() < 3.0d);
            case 796:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96211_").doubleValue() >= 1.0d);
            case 797:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96212_").doubleValue() >= 1.0d);
            case 798:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96213_").doubleValue() >= 3.0d);
            case 799:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96215_").doubleValue() >= 4.0d);
            case 800:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96216_").doubleValue() <= 3.0d);
            case 801:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96217_").doubleValue() < 3.0d);
            case 802:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96218_").doubleValue() >= 1.0d);
            case 803:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96125_").doubleValue() > 1.0d);
            case 804:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99001_").doubleValue() < 1.0d);
            case 805:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99001_").doubleValue() < 1.0d && iJavaScriptEngine.getDouble("_99030_").doubleValue() == 1.0d);
            case 806:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99001_").doubleValue() < 1.0d && iJavaScriptEngine.getDouble("_99004_").doubleValue() == 1.0d);
            case 807:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99003_").doubleValue() >= 1.0d && iJavaScriptEngine.getDouble("_96124_").doubleValue() <= 0.01d);
            case 808:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99031_").doubleValue() >= 1.0d);
            case 809:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99032_").doubleValue() >= 1.0d);
            case 810:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99033_").doubleValue() >= 1.0d);
            case 811:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99034_").doubleValue() >= 1.0d);
            case 812:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99001_").doubleValue() < 1.0d && iJavaScriptEngine.getDouble("_99010_").doubleValue() >= 1.0d);
            case 813:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99001_").doubleValue() < 1.0d && iJavaScriptEngine.getDouble("_99011_").doubleValue() >= 1.0d);
            case 814:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99001_").doubleValue() < 1.0d && iJavaScriptEngine.getDouble("_99012_").doubleValue() >= 1.0d);
            case 815:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99001_").doubleValue() < 1.0d && iJavaScriptEngine.getDouble("_99013_").doubleValue() >= 1.0d);
            case 816:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99001_").doubleValue() < 1.0d && iJavaScriptEngine.getDouble("_99014_").doubleValue() >= 1.0d);
            case 817:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99001_").doubleValue() < 1.0d && iJavaScriptEngine.getDouble("_99015_").doubleValue() >= 1.0d);
            case 818:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99001_").doubleValue() < 1.0d && iJavaScriptEngine.getDouble("_99016_").doubleValue() >= 1.0d);
            case 819:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99001_").doubleValue() < 1.0d && iJavaScriptEngine.getDouble("_99017_").doubleValue() >= 1.0d);
            case 820:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99001_").doubleValue() < 1.0d && iJavaScriptEngine.getDouble("_99018_").doubleValue() >= 1.0d);
            case 821:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99001_").doubleValue() < 1.0d && iJavaScriptEngine.getDouble("_99019_").doubleValue() >= 1.0d);
            case 822:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94056_").doubleValue() >= 1.0d);
            case 823:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99036_").doubleValue() >= 1.0d);
            case 824:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99037_").doubleValue() >= 1.0d);
            case 825:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99038_").doubleValue() >= 1.0d);
            case 826:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99039_").doubleValue() >= 1.0d);
            case 827:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94056_").doubleValue() >= 4.0d);
            case 828:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() <= 2.0d);
            case 829:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() <= 3.0d);
            case 830:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() <= 4.0d);
            case 831:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() <= 5.0d);
            case 832:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() <= 6.0d);
            case 833:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() <= 7.0d);
            case 834:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() <= 8.0d);
            case 835:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() <= 9.0d);
            case 836:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() <= 10.0d);
            case 837:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96232_").doubleValue() >= 1.0d);
            case 838:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96234_").doubleValue() >= 1.0d);
            case 839:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() == 3.0d);
            case 840:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_97001_").doubleValue() >= 4.0d);
            case 841:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99040_").doubleValue() >= 2.0d);
            case 842:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() == 4.0d);
            case 843:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() == 5.0d);
            case 844:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() == 6.0d);
            case 845:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() == 7.0d);
            case 846:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() == 8.0d);
            case 847:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_95003_").doubleValue() == 9.0d);
            case 848:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99041_").doubleValue() >= 3.0d);
            case 849:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99042_").doubleValue() >= 3.0d);
            case 850:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99043_").doubleValue() >= 1.0d);
            case 851:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99044_").doubleValue() < 1.0d);
            case 852:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99045_").doubleValue() < 1.0d);
            case 853:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99047_").doubleValue() >= 1.0d);
            case 854:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99048_").doubleValue() >= 1.0d);
            case 855:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94056_").doubleValue() >= 3.0d);
            case 856:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99049_").doubleValue() >= 1.0d);
            case 857:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99052_").doubleValue() >= 1.0d);
            case 858:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94014_").doubleValue() >= 7.0d);
            case 859:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99053_").doubleValue() >= 1.0d);
            case 860:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99054_").doubleValue() >= 1.0d);
            case 861:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99055_").doubleValue() >= 1.0d);
            case 862:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99056_").doubleValue() >= 1.0d);
            case 863:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99057_").doubleValue() >= 1.0d);
            case 864:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99058_").doubleValue() >= 1.0d);
            case 865:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99059_").doubleValue() >= 1.0d);
            case 866:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99060_").doubleValue() >= 1.0d);
            case 867:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99061_").doubleValue() >= 1.0d);
            case 868:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99053_").doubleValue() < 1.0d);
            case 869:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99054_").doubleValue() < 1.0d);
            case 870:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99055_").doubleValue() < 1.0d);
            case 871:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99056_").doubleValue() < 1.0d);
            case 872:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99057_").doubleValue() < 1.0d);
            case 873:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99058_").doubleValue() < 1.0d);
            case 874:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99059_").doubleValue() < 1.0d);
            case 875:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99060_").doubleValue() < 1.0d);
            case 876:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99061_").doubleValue() < 1.0d);
            case 877:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_96001_").doubleValue() / iJavaScriptEngine.getDouble("_96002_").doubleValue() < 0.1d);
            case 878:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99062_").doubleValue() >= 1.0d);
            case 879:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99062_").doubleValue() >= 2.0d);
            case 880:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99062_").doubleValue() >= 3.0d);
            case 881:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99066_").doubleValue() >= 1.0d);
            case 882:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99067_").doubleValue() >= 1.0d);
            case 883:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99049_").doubleValue() >= 5.0d);
            case 884:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99068_").doubleValue() >= 1.0d);
            case 885:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99071_").doubleValue() >= 1.0d);
            case 886:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99070_").doubleValue() == 1.0d);
            case 887:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99072_").doubleValue() < 1.0d);
            case 888:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_94041_").doubleValue() >= 1.0d);
            case 889:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99065_").doubleValue() >= 3.0d);
            case 890:
                return Boolean.valueOf(iJavaScriptEngine.getDouble("_99074_").doubleValue() >= 1.0d);
            case 891:
                return (Boolean) iJavaScriptEngine.get("_502002_");
            case 892:
                return (Boolean) iJavaScriptEngine.get("_502003_");
            case 893:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_13_")).booleanValue());
            case 894:
                return Boolean.valueOf(((Boolean) iJavaScriptEngine.get("_509082_")).booleanValue() || ((Boolean) iJavaScriptEngine.get("_509083_")).booleanValue());
            case 895:
                return (Boolean) iJavaScriptEngine.get("_504002_");
            case 896:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_504002_")).booleanValue());
            case 897:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_120_")).booleanValue());
            case 898:
                return (Boolean) iJavaScriptEngine.get("_120_");
            case 899:
                return (Boolean) iJavaScriptEngine.get("_500033_");
            case 900:
                return (Boolean) iJavaScriptEngine.get("_506003_");
            case 901:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_506003_")).booleanValue());
            case 902:
                return (Boolean) iJavaScriptEngine.get("_501901_");
            case 903:
                return Boolean.valueOf(((Boolean) iJavaScriptEngine.get("_502003_")).booleanValue() || ((Boolean) iJavaScriptEngine.get("_506002_")).booleanValue());
            case 904:
                return Boolean.valueOf(((Boolean) iJavaScriptEngine.get("_505005_")).booleanValue() || ((Boolean) iJavaScriptEngine.get("_504011_")).booleanValue());
            case 905:
                return Boolean.valueOf((((Boolean) iJavaScriptEngine.get("_501010_")).booleanValue() || ((Boolean) iJavaScriptEngine.get("_13_")).booleanValue()) ? false : true);
            case 906:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_501004_")).booleanValue());
            case 907:
                return Boolean.valueOf(((Boolean) iJavaScriptEngine.get("_509082_")).booleanValue() || ((Boolean) iJavaScriptEngine.get("_509083_")).booleanValue() || ((Boolean) iJavaScriptEngine.get("_506201_")).booleanValue());
            case 908:
                return (Boolean) iJavaScriptEngine.get("_506002_");
            case 909:
                return (Boolean) iJavaScriptEngine.get("_501402_");
            case 910:
                return Boolean.valueOf((((Boolean) iJavaScriptEngine.get("_13_")).booleanValue() || ((Boolean) iJavaScriptEngine.get("_501008_")).booleanValue()) ? false : true);
            case 911:
                return Boolean.valueOf(((Boolean) iJavaScriptEngine.get("_110_")).booleanValue() || ((Boolean) iJavaScriptEngine.get("_120_")).booleanValue() || ((Boolean) iJavaScriptEngine.get("_13_")).booleanValue());
            case 912:
                return (Boolean) iJavaScriptEngine.get("_506306_");
            case 913:
                return (Boolean) iJavaScriptEngine.get("_503002_");
            case 914:
                return (Boolean) iJavaScriptEngine.get("_504003_");
            case 915:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_506109_")).booleanValue());
            case 916:
                return (Boolean) iJavaScriptEngine.get("_13_");
            case 917:
                return (Boolean) iJavaScriptEngine.get("_504013_");
            case 918:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_501010_")).booleanValue());
            case 919:
                return (Boolean) iJavaScriptEngine.get("_506109_");
            case 920:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_506101_")).booleanValue());
            case 921:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_510139_")).booleanValue());
            case 922:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_506306_")).booleanValue());
            case 923:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_506201_")).booleanValue());
            case 924:
                return (Boolean) iJavaScriptEngine.get("_501010_");
            case 925:
                return (Boolean) iJavaScriptEngine.get("_501004_");
            case 926:
                return Boolean.valueOf(((Boolean) iJavaScriptEngine.get("_509082_")).booleanValue() || ((Boolean) iJavaScriptEngine.get("_509083_")).booleanValue() || ((Boolean) iJavaScriptEngine.get("_506201_")).booleanValue() || ((Boolean) iJavaScriptEngine.get("_509068_")).booleanValue() || ((Boolean) iJavaScriptEngine.get("_509031_")).booleanValue());
            case 927:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_502002_")).booleanValue());
            case 928:
                return Boolean.valueOf(((Boolean) iJavaScriptEngine.get("_110_")).booleanValue() || ((Boolean) iJavaScriptEngine.get("_120_")).booleanValue());
            case 929:
                return (Boolean) iJavaScriptEngine.get("_509201_");
            case 930:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_508236_")).booleanValue());
            case 931:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_508237_")).booleanValue());
            case 932:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_509951_")).booleanValue());
            case 933:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_503001_")).booleanValue());
            case 934:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_501402_")).booleanValue());
            case 935:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_508002_")).booleanValue());
            case 936:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_508006_")).booleanValue());
            case 937:
                return (Boolean) iJavaScriptEngine.get("_509081_");
            case 938:
                return (Boolean) iJavaScriptEngine.get("_503001_");
            case 939:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_508014_")).booleanValue());
            case 940:
                return Boolean.valueOf(!((Boolean) iJavaScriptEngine.get("_508008_")).booleanValue());
            case 941:
                return Integer.valueOf((1 * iJavaScriptEngine.getDouble("TeamNum").intValue()) + 3);
            case 942:
                return Integer.valueOf(iJavaScriptEngine.getDouble("TeamLv").intValue());
            case 943:
                return Integer.valueOf((1 * iJavaScriptEngine.getDouble("TeamNum").intValue()) + 5);
            case 944:
                return Integer.valueOf((0 * iJavaScriptEngine.getDouble("TeamNum").intValue()) + 4);
            case 945:
                return Integer.valueOf((1 * iJavaScriptEngine.getDouble("TeamNum").intValue()) + 4);
            case 946:
                return Integer.valueOf(1 * iJavaScriptEngine.getDouble("TeamNum").intValue());
            case 947:
                return Double.valueOf((0 * iJavaScriptEngine.getDouble("TeamNum").intValue()) + 6 + (2.0d * Math.random()));
            case 948:
                return Double.valueOf((0 * iJavaScriptEngine.getDouble("TeamNum").intValue()) + 8 + (2.0d * Math.random()));
            case 949:
                return Integer.valueOf((0 * iJavaScriptEngine.getDouble("TeamNum").intValue()) + 2);
            default:
                return null;
        }
    }
}