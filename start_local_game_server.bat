@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM ==============================================================================
REM 一键启动本地游戏服务器脚本 (Windows版本)
REM 自动检查环境、设置环境、启动服务器并监控日志
REM ==============================================================================

title GSXDB 本地游戏服务器启动工具

REM 颜色定义
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "PURPLE=[95m"
set "NC=[0m"

REM 路径配置
set "PROJECT_ROOT=%~dp0"
set "PROJECT_ROOT=%PROJECT_ROOT:~0,-1%"

REM 显示标题
cls
echo %PURPLE%╔══════════════════════════════════════════════════════════════╗%NC%
echo %PURPLE%║                    GSXDB 本地游戏服务器                      ║%NC%
echo %PURPLE%║                      一键启动工具                            ║%NC%
echo %PURPLE%╚══════════════════════════════════════════════════════════════╝%NC%
echo.

REM 日志函数
goto :main

:log_info
echo %BLUE%[INFO]%NC% %~1
exit /b

:log_success
echo %GREEN%[SUCCESS]%NC% %~1
exit /b

:log_warning
echo %YELLOW%[WARNING]%NC% %~1
exit /b

:log_error
echo %RED%[ERROR]%NC% %~1
exit /b

:progress_bar
echo %CYAN%%~2%NC% [████████████████████████████████████████████████████] %GREEN%完成%NC%
exit /b

:check_dependencies
call :log_info "检查系统依赖..."

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    python3 --version >nul 2>&1
    if errorlevel 1 (
        call :log_error "Python未安装或不在PATH中"
        echo.
        echo %YELLOW%请安装Python 3.6+: https://www.python.org/downloads/%NC%
        echo %YELLOW%安装时请勾选 "Add Python to PATH"%NC%
        exit /b 1
    ) else (
        set "PYTHON_CMD=python3"
    )
) else (
    set "PYTHON_CMD=python"
)

REM 检查Java
java -version >nul 2>&1
if errorlevel 1 (
    call :log_error "Java未安装或不在PATH中"
    echo.
    echo %YELLOW%请安装Java 8+: https://adoptopenjdk.net/%NC%
    echo %YELLOW%安装后请配置JAVA_HOME和PATH环境变量%NC%
    exit /b 1
)

call :log_success "依赖检查通过"
exit /b 0

:run_environment_check
call :log_info "运行环境检查..."

if exist "%PROJECT_ROOT%\scripts\tools\check_game_environment.py" (
    %PYTHON_CMD% "%PROJECT_ROOT%\scripts\tools\check_game_environment.py"
    if errorlevel 1 (
        call :log_error "环境检查失败"
        echo.
        echo %YELLOW%请根据上述提示解决环境问题后重试%NC%
        exit /b 1
    ) else (
        call :log_success "环境检查通过"
        exit /b 0
    )
) else (
    call :log_warning "环境检查脚本不存在，跳过详细检查"
    exit /b 0
)

:setup_local_environment
call :log_info "设置本地游戏环境..."

if exist "%PROJECT_ROOT%\scripts\game\local_game_server.sh" (
    bash "%PROJECT_ROOT%\scripts\game\local_game_server.sh" setup
    if errorlevel 1 (
        call :log_error "本地环境设置失败"
        exit /b 1
    ) else (
        call :log_success "本地环境设置完成"
        exit /b 0
    )
) else (
    call :log_error "本地游戏服务器脚本不存在"
    exit /b 1
)

:start_game_server
call :log_info "启动游戏服务器..."

if exist "%PROJECT_ROOT%\scripts\game\local_game_server.sh" (
    bash "%PROJECT_ROOT%\scripts\game\local_game_server.sh" start
    if errorlevel 1 (
        call :log_error "游戏服务器启动失败"
        exit /b 1
    ) else (
        call :log_success "游戏服务器启动成功"
        exit /b 0
    )
) else (
    call :log_error "本地游戏服务器脚本不存在"
    exit /b 1
)

:start_log_monitoring
call :log_info "启动日志监控..."

if exist "%PROJECT_ROOT%\scripts\tools\game_log_monitor.py" (
    echo.
    echo %CYAN%选择监控模式:%NC%
    echo %YELLOW%1.%NC% 实时监控 (推荐)
    echo %YELLOW%2.%NC% 分析现有日志
    echo %YELLOW%3.%NC% 跳过监控
    echo.
    set /p "choice=%CYAN%请选择 [1-3]: %NC%"
    
    if "!choice!"=="1" (
        echo.
        call :log_info "启动实时日志监控..."
        echo %YELLOW%提示: 按 Ctrl+C 可停止监控%NC%
        echo.
        timeout /t 2 >nul
        %PYTHON_CMD% "%PROJECT_ROOT%\scripts\tools\game_log_monitor.py" monitor --auto-fix
    ) else if "!choice!"=="2" (
        echo.
        call :log_info "分析现有日志..."
        %PYTHON_CMD% "%PROJECT_ROOT%\scripts\tools\game_log_monitor.py" analyze
    ) else if "!choice!"=="3" (
        call :log_info "跳过日志监控"
    ) else (
        call :log_warning "无效选择，跳过监控"
    )
) else (
    call :log_warning "日志监控脚本不存在，跳过监控"
)
exit /b

:show_service_status
echo.
echo %BLUE%╔══════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                        服务状态                              ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════╝%NC%

if exist "%PROJECT_ROOT%\scripts\game\local_game_server.sh" (
    bash "%PROJECT_ROOT%\scripts\game\local_game_server.sh" status
)

echo.
echo %GREEN%🎮 游戏服务器管理命令:%NC%
echo %YELLOW%  查看状态:%NC% bash scripts/game/local_game_server.sh status
echo %YELLOW%  停止服务:%NC% bash scripts/game/local_game_server.sh stop
echo %YELLOW%  查看日志:%NC% bash scripts/game/local_game_server.sh logs
echo %YELLOW%  清理环境:%NC% bash scripts/game/local_game_server.sh clean
echo.
echo %GREEN%📊 日志监控命令:%NC%
echo %YELLOW%  实时监控:%NC% python scripts/tools/game_log_monitor.py monitor
echo %YELLOW%  分析日志:%NC% python scripts/tools/game_log_monitor.py analyze
echo.
exit /b

:show_help
echo %BLUE%GSXDB 本地游戏服务器一键启动工具%NC%
echo.
echo %YELLOW%用法:%NC%
echo   %0 [选项]
echo.
echo %YELLOW%选项:%NC%
echo   --check-only    仅运行环境检查
echo   --setup-only    仅设置本地环境
echo   --no-monitor    启动服务器但不监控日志
echo   --help          显示此帮助信息
echo.
echo %YELLOW%功能:%NC%
echo   1. 自动检查系统环境和依赖
echo   2. 设置本地游戏服务器环境
echo   3. 启动游戏服务器和相关服务
echo   4. 实时监控日志并自动诊断问题
echo.
echo %YELLOW%支持的游戏端口:%NC%
echo   - 名称服务: 22200
echo   - SDK服务: 29200
echo   - 大区1 GM端口: 41001
echo   - 网关端口: 42001
echo   - 游戏端口: 43001
echo.
exit /b

:main
REM 解析命令行参数
set "check_only=false"
set "setup_only=false"
set "no_monitor=false"

:parse_args
if "%~1"=="--check-only" (
    set "check_only=true"
    shift
    goto :parse_args
)
if "%~1"=="--setup-only" (
    set "setup_only=true"
    shift
    goto :parse_args
)
if "%~1"=="--no-monitor" (
    set "no_monitor=true"
    shift
    goto :parse_args
)
if "%~1"=="--help" (
    call :show_help
    exit /b 0
)
if not "%~1"=="" (
    call :log_error "未知参数: %~1"
    call :show_help
    exit /b 1
)

REM 检查Git Bash是否可用
bash --version >nul 2>&1
if errorlevel 1 (
    call :log_error "未找到bash环境"
    echo.
    echo %YELLOW%请安装Git for Windows以获得bash支持:%NC%
    echo %YELLOW%https://git-scm.com/download/win%NC%
    echo.
    echo %YELLOW%或者使用WSL (Windows Subsystem for Linux):%NC%
    echo %YELLOW%wsl --install%NC%
    pause
    exit /b 1
)

REM 步骤1: 检查依赖
call :check_dependencies
if errorlevel 1 exit /b 1

call :progress_bar 1 "检查系统依赖"

REM 步骤2: 运行环境检查
call :run_environment_check
if errorlevel 1 (
    if "%check_only%"=="true" exit /b 1
    
    echo.
    set /p "continue_anyway=%YELLOW%环境检查失败，是否继续? (y/N): %NC%"
    if /i not "!continue_anyway!"=="y" (
        call :log_info "用户取消操作"
        exit /b 1
    )
)

if "%check_only%"=="true" (
    call :log_success "环境检查完成"
    pause
    exit /b 0
)

call :progress_bar 2 "环境检查"

REM 步骤3: 设置本地环境
call :setup_local_environment
if errorlevel 1 (
    call :log_error "环境设置失败"
    pause
    exit /b 1
)

if "%setup_only%"=="true" (
    call :log_success "环境设置完成"
    pause
    exit /b 0
)

call :progress_bar 2 "设置本地环境"

REM 步骤4: 启动游戏服务器
call :start_game_server
if errorlevel 1 (
    call :log_error "游戏服务器启动失败"
    echo.
    echo %YELLOW%建议操作:%NC%
    echo 1. 检查日志: python scripts/tools/game_log_monitor.py analyze
    echo 2. 查看状态: bash scripts/game/local_game_server.sh status
    echo 3. 重新运行环境检查: python scripts/tools/check_game_environment.py
    pause
    exit /b 1
)

call :progress_bar 3 "启动游戏服务器"

REM 显示服务状态
call :show_service_status

REM 步骤5: 启动日志监控
if not "%no_monitor%"=="true" (
    call :start_log_monitoring
) else (
    call :log_info "跳过日志监控"
)

echo.
call :log_success "🎉 本地游戏服务器启动完成！"
echo.
echo %GREEN%🎮 现在您可以:%NC%
echo %YELLOW%  1.%NC% 连接到游戏服务器进行测试
echo %YELLOW%  2.%NC% 使用GM工具管理游戏
echo %YELLOW%  3.%NC% 查看实时日志监控
echo.

pause
