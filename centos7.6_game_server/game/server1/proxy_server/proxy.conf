[DeliverServer]
type            = tcp
port            = 44001
address         = 0.0.0.0
;aid             = 23
;zoneid          = 23088
aid             = 15
zoneid          = 1

max_player_num	= 10000
fake_max_player_num	= 10000

;default num is 100000
max_cache_num	= 500000

# buffer
so_sndbuf       = 65536
so_rcvbuf       = 65536
ibuffermax      = 1048576
obuffermax      = 1048576
accumulate      = 1048576

# misc
listen_backlog    = 10
tcp_nodelay      = 0
forbid_client_autologin	= 1
allow_nickname_contain_account = 1

support_forcelogin	= 0
forcelogin_timeout	= 30

[GameDBServer]
type			=	tcp
port			=	45001
address			=	0.0.0.0

so_sndbuf       = 65536
so_rcvbuf       = 65536
ibuffermax      = 1048576
obuffermax      = 1048576
accumulate      = 1048576

tcp_nodelay		=	0
listen_backlog		=	10


[AuAnyclient]
type            =       tcp
port            =       29200
address         =       127.0.0.1
;address         =       0.0.0.0
tcp_nodelay     =       0
;isec            =       2
;iseckey         =	oj73pulofapwoxmvkeuezuavfapstbwf
;osec            =       2
;oseckey         =       yybfjhlYuvMuiasaudykb9cmaxep8wsk
# buffer
so_sndbuf       = 	65536
so_rcvbuf       = 	65536
ibuffermax      = 	1048576
obuffermax      = 	131072
accumulate      = 	131072

plattype	= 	1
serverid	=	**********

[AuthClient]
type                    =       tcp
port                    =       29200
address                 =       127.0.0.1

tcp_nodelay             =       0

;isec                    =       2
;iseckey                 =       n1hxpxztozyxnsvk6RaycpmrCnrdds
;osec                    =       2
;oseckey                 =       rdppjtaki1MxoHnsnaltiiwfjszs9l
;shared_key              =       4khdwAAcjrg0eqfzazqcemdpgulnje

# buffer
so_sndbuf       = 65536
so_rcvbuf       = 65536
ibuffermax      = 1048576
obuffermax      = 131072
accumulate      = 131072

#if use certverify between au,1:use,0:not use,default not use
use_cert		=	0

[TradeClient]
#0-not connect tradeserver;1-connect
bl_open		=	0
type                    =       tcp
port                    =       29208
address                 =       127.0.0.1

tcp_nodelay             =       0

;isec                    =       2
;iseckey                 =       n1hxpxztozyxnsvk6RaycpmrCnrdds
;osec                    =       2
;oseckey                 =       rdppjtaki1MxoHnsnaltiiwfjszs9l
;shared_key              =       4khdwAAcjrg0eqfzazqcemdpgulnje

# buffer
so_sndbuf       = 65536
so_rcvbuf       = 65536
ibuffermax      = 1048576
obuffermax      = 131072
accumulate      = 131072

[IMClient]
#0-not connect IM server;1-connect
bl_open			=	0
type                    =       tcp
port                    =       20026
address                 =       ***********

tcp_nodelay             =       0

;isec                    =       2
;iseckey                 =       n1hxpxztozyxnsvk6RaycpmrCnrdds
;osec                    =       2
;oseckey                 =       rdppjtaki1MxoHnsnaltiiwfjszs9l
;shared_key              =       4khdwAAcjrg0eqfzazqcemdpgulnje

# buffer
so_sndbuf       = 65536
so_rcvbuf       = 65536
ibuffermax      = 1048576
obuffermax      = 131072
accumulate      = 131072

[SNSClient]
#0-not connect SNS server;1-connect
bl_open			=	0
type                    =       tcp
port                    =       10026
address                 =       ***********

tcp_nodelay             =       0

;isec                    =       2
;iseckey                 =       n1hxpxztozyxnsvk6RaycpmrCnrdds
;osec                    =       2
;oseckey                 =       rdppjtaki1MxoHnsnaltiiwfjszs9l
;shared_key              =       4khdwAAcjrg0eqfzazqcemdpgulnje

# buffer
so_sndbuf       = 65536
so_rcvbuf       = 65536
ibuffermax      = 1048576
obuffermax      = 131072
accumulate      = 131072

[CrossServer]
type            = 	tcp
port            = 	29201
address         = 	0.0.0.0
isec            =       2
iseckey         =       yybfjhlYuvMuiasaudykb9cmaxep8wsk
osec            =       2
oseckey         =	oj73pulofapwoxmvkeuezuavfapstbwf
# buffer
so_sndbuf       = 	65536
so_rcvbuf       = 	65536
ibuffermax      = 	1048576
obuffermax      = 	1048576
accumulate      = 	1048576
listen_backlog  = 	10
tcp_nodelay     = 	0

[CrossClient]
bl_open		=	0
type            =       tcp
port            =       29201
address         =       0.0.0.0
tcp_nodelay     =       0
isec            =       2
iseckey         =	oj73pulofapwoxmvkeuezuavfapstbwf
osec            =       2
oseckey         =       yybfjhlYuvMuiasaudykb9cmaxep8wsk
# buffer
so_sndbuf       = 	65536
so_rcvbuf       = 	65536
ibuffermax      = 	1048576
obuffermax      = 	131072
accumulate      = 	131072

[Intervals]
;set check forbidlogin user's map interval,unit is second
checkforbidmap_interval = 60
keepalive_interval 	= 300
cross_ticket_alive_second = 180

[SpeedLimit]
window			=	60
high			=	50

#control InstantAddCash,the same cardnum can only use "high" times in "window" time
[InstantAddCash]
window			=	300
high			=	3

#control the num of errorcode=2 && errorcode=3 at the same ip
[LockIPLimit]
window			=	60
high			=	5000

#control GetUserCoupon frequency
[CouponLimit]
window			=	300
high			=	2

#control CouponExchange frequency
[CouponExcgangeLimit]
window                  =       120
high                    =       1

#control the AU requset
[AuRequestLimit]
window                  =       600
high                    =       100

[Mysql]
;ip                  =       127.0.0.1
ip                   =       127.0.0.1
port                    =       3306
user                    =      root
passwd                    =	123456
dbname                    =     mt3
reconninterval                =     600

