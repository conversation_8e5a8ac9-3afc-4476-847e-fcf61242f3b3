#创建表
NormalTab=CREATE TABLE ITEM_NORMAL(ID BIGINT NOT NULL, FIRSTNO INT, TWONO INT, THREENO INT, UNIQUEID LONG, KEY INT, ROLEID LONG, ITEMID INT, NAME VARCHAR(500), EXTID LONG, <PERSON><PERSON><PERSON><PERSON> INT, NUMBER INT, PRICE INT, ATTENTION INT, SHOWTIME LONG, EXPIRETIME LONG)
EquipTab=CREATE TABLE ITEM_EQUIP(ID BIGINT NOT NULL, FIRSTNO INT, TWONO INT, THREENO INT, UNIQUEID LONG, KEY INT, ROLEID LONG, ITEMID INT, NAME VARCHAR(500), EXTID LONG, LEVEL INT, EFFECT INT, SKILL INT, COLOR INT, TOTALATTR INT, EQUIPSCORE INT, NUMBER INT, PRICE INT, ATTENTION INT, SHOWTIME LONG, <PERSON>X<PERSON>RE<PERSON>ME LONG)
PetTab=CREATE TABLE ITEM_PET(ID BIGINT NOT NULL, FIRSTNO INT, TWONO INT, THREENO INT, UNIQUEID LONG, KEY INT, ROLEID LONG, ITEMID INT, NAME VARCHAR(500), LEVEL INT, ATTACK INT, DEFEND INT, SPEED INT, MAGICATTACK INT, MAGCIDEF INT, MAXHP INT, ATTACKAPT INT, DEFENDAPT INT, PHYFORCEAPT INT, MAGICAPT INT, SPEEDAPT INT, DODGEAPT INT, GROWRATE FLOAT, SKILLNUMBER INT, PETSCORE INT, NUMBER INT, PRICE INT, ATTENTION INT, SHOWTIME LONG, EXPIRETIME LONG)

#创建主键
NormalTabPri=CREATE PRIMARY KEY PRIMARY_KEY_N ON ITEM_NORMAL(ID) 
EquipTabPri=CREATE PRIMARY KEY PRIMARY_KEY_E ON ITEM_EQUIP(ID)
PetTabPri=CREATE PRIMARY KEY PRIMARY_KEY_P ON ITEM_PET(ID)


#创建索引
NormalTabIdx=CREATE INDEX NFTTIDX1 ON ITEM_NORMAL(FIRSTNO, TWONO, THREENO)
EquipTabIdx=CREATE INDEX NFTTIDX2 ON ITEM_EQUIP(FIRSTNO, TWONO, THREENO)
PetTabIdx=CREATE INDEX NFTTIDX3 ON ITEM_PET(FIRSTNO, TWONO, THREENO)
PetTabItemIdIdx=CREATE INDEX PETITEMIDIDX3 ON ITEM_PET(ITEMID)
NormalTabLevelIdx=CREATE INDEX NFTTLEVELIDX1 ON ITEM_NORMAL(LEVEL)
NormalTabPriceIdx=CREATE INDEX NFTTPRICEIDX1 ON ITEM_NORMAL(PRICE)

#删除表
DropNormalTab=DROP TABLE IF EXISTS ITEM_NORMAL
DropEquipTab=DROP TABLE IF EXISTS ITEM_EQUIP
DropPetTab=DROP TABLE IF EXISTS ITEM_PET

#删除索引
DropNormalTabIdx=DROP INDEX IF EXISTS NFTTIDX1
DropEquipTabIdx=DROP INDEX IF EXISTS NFTTIDX2
DropPetTabIdx=DROP INDEX IF EXISTS NFTTIDX3
DropPetItemIdIdx=DROP INDEX IF EXISTS PETITEMIDIDX3
DropNormalTabLevelIdx=DROP INDEX IF EXISTS NFTTLEVELIDX1
DropNormalTabPriceIdx=DROP INDEX IF EXISTS NFTTPRICEIDX1

#查询表
SelectNormalTab=SELECT * FROM ITEM_NORMAL
SelectEquipTab=SELECT * FROM ITEM_EQUIP
SelectPetTab=SELECT * FROM ITEM_PET


