#!/bin/bash
# MT3游戏服务器内存优化启动脚本
# 解决内存占用过高问题

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] [INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] [WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR]${NC} $1"
}

# 检查系统内存
check_memory() {
    log_info "检查系统内存状态..."
    
    # 获取系统总内存(GB)
    if command -v free >/dev/null 2>&1; then
        TOTAL_MEM_KB=$(free | grep '^Mem:' | awk '{print $2}')
        TOTAL_MEM_GB=$((TOTAL_MEM_KB / 1024 / 1024))
    else
        # Windows环境估算
        TOTAL_MEM_GB=4
    fi
    
    log_info "系统总内存: ${TOTAL_MEM_GB}GB"
    
    # 根据系统内存调整JVM配置
    if [ $TOTAL_MEM_GB -ge 8 ]; then
        XMS="2048m"
        XMX="4096m"
        log_info "大内存系统: 使用 -Xms${XMS} -Xmx${XMX}"
    elif [ $TOTAL_MEM_GB -ge 4 ]; then
        XMS="1024m"
        XMX="2048m"
        log_info "中等内存系统: 使用 -Xms${XMS} -Xmx${XMX}"
    else
        XMS="512m"
        XMX="1024m"
        log_warn "小内存系统: 使用 -Xms${XMS} -Xmx${XMX}"
    fi
}

# 清理内存
cleanup_memory() {
    log_info "清理系统内存..."
    
    # 停止现有Java进程
    pkill -f "gsxdb.jar" 2>/dev/null || true
    
    # 清理临时文件
    rm -rf temp_build 2>/dev/null || true
    rm -f *.tmp 2>/dev/null || true
    
    # 强制垃圾回收(如果有运行中的Java进程)
    if command -v jcmd >/dev/null 2>&1; then
        for pid in $(pgrep -f "gsxdb.jar"); do
            log_info "对进程 $pid 执行垃圾回收..."
            jcmd $pid GC.run 2>/dev/null || true
        done
    fi
    
    sleep 2
}

# 启动游戏服务器
start_server() {
    log_info "启动内存优化的游戏服务器..."
    
    # JVM内存优化参数
    JVM_OPTS="-server"
    JVM_OPTS="$JVM_OPTS -Xms${XMS} -Xmx${XMX}"
    
    # 垃圾回收优化
    JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
    JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=200"
    JVM_OPTS="$JVM_OPTS -XX:G1HeapRegionSize=16m"
    JVM_OPTS="$JVM_OPTS -XX:+UseStringDeduplication"
    
    # 内存泄漏检测
    JVM_OPTS="$JVM_OPTS -XX:+HeapDumpOnOutOfMemoryError"
    JVM_OPTS="$JVM_OPTS -XX:HeapDumpPath=./logs/"
    
    # 性能监控
    JVM_OPTS="$JVM_OPTS -XX:+PrintGC"
    JVM_OPTS="$JVM_OPTS -XX:+PrintGCDetails"
    JVM_OPTS="$JVM_OPTS -XX:+PrintGCTimeStamps"
    JVM_OPTS="$JVM_OPTS -Xloggc:./logs/gc.log"
    
    # 网络优化
    JVM_OPTS="$JVM_OPTS -Djava.net.preferIPv4Stack=true"
    JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
    
    # 创建日志目录
    mkdir -p logs
    
    log_info "JVM参数: $JVM_OPTS"
    
    # 启动服务器
    nohup java $JVM_OPTS -jar gsxdb.jar -rmiport 41001 > logs/server.log 2>&1 &
    SERVER_PID=$!
    echo $SERVER_PID > server.pid
    
    log_info "游戏服务器已启动 (PID: $SERVER_PID)"
    
    # 等待启动
    sleep 5
    
    if kill -0 $SERVER_PID 2>/dev/null; then
        log_info "✅ 游戏服务器启动成功"
        return 0
    else
        log_error "❌ 游戏服务器启动失败"
        return 1
    fi
}

# 监控内存使用
monitor_memory() {
    log_info "开始监控内存使用..."
    
    while true; do
        if [ -f "server.pid" ]; then
            SERVER_PID=$(cat server.pid)
            if kill -0 $SERVER_PID 2>/dev/null; then
                # 获取Java进程内存使用
                if command -v ps >/dev/null 2>&1; then
                    MEM_USAGE=$(ps -p $SERVER_PID -o rss= 2>/dev/null)
                    if [ -n "$MEM_USAGE" ]; then
                        MEM_MB=$((MEM_USAGE / 1024))
                        log_info "游戏服务器内存使用: ${MEM_MB}MB"
                        
                        # 内存使用超过阈值时警告
                        if [ $MEM_MB -gt 1536 ]; then
                            log_warn "⚠️  内存使用过高: ${MEM_MB}MB"
                        fi
                    fi
                fi
            else
                log_error "游戏服务器进程已停止"
                break
            fi
        else
            log_error "找不到PID文件"
            break
        fi
        
        sleep 30
    done
}

# 主函数
main() {
    case "${1:-start}" in
        start)
            check_memory
            cleanup_memory
            start_server
            ;;
        monitor)
            monitor_memory
            ;;
        stop)
            log_info "停止游戏服务器..."
            if [ -f "server.pid" ]; then
                SERVER_PID=$(cat server.pid)
                if kill -0 $SERVER_PID 2>/dev/null; then
                    kill $SERVER_PID
                    log_info "游戏服务器已停止"
                fi
                rm -f server.pid
            fi
            ;;
        restart)
            $0 stop
            sleep 3
            $0 start
            ;;
        *)
            echo "使用方法: $0 {start|stop|restart|monitor}"
            echo ""
            echo "start   - 启动内存优化的游戏服务器"
            echo "stop    - 停止游戏服务器"
            echo "restart - 重启游戏服务器"
            echo "monitor - 监控内存使用"
            exit 1
            ;;
    esac
}

main "$@"
