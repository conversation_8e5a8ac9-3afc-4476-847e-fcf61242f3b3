@echo off
title=game_server

echo ================================================================
echo            游戏服务器启动脚本
echo            Game Server Startup Script
echo ================================================================
echo.

echo [INFO] 检查Java环境...
java -version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Java未安装或未在PATH中找到
    echo [INFO] 请安装JDK 1.8或更高版本
    pause
    exit /b 1
)

echo [INFO] 检查必要文件...
if not exist "gsxdb.jar" (
    echo [ERROR] gsxdb.jar 文件不存在
    pause
    exit /b 1
)

if not exist "gsx.mkdb.xml" (
    echo [ERROR] gsx.mkdb.xml 配置文件不存在
    pause
    exit /b 1
)

echo [INFO] 创建必要目录...
if not exist "logs" mkdir logs
if not exist "mkdb" mkdir mkdb
if not exist "mbackup" mkdir mbackup

echo [INFO] 清理锁定文件...
if exist "mkdb\mkdb.inuse" del /f "mkdb\mkdb.inuse"

echo [INFO] 启动游戏服务器...
echo [INFO] 服务器将监听端口: 14000 (游戏端口), 41001 (RMI端口)
echo [INFO] 日志文件: gs.log
echo [INFO] 按 Ctrl+C 可以停止服务器
echo.

REM JVM参数设置
set JAVA_OPTS=-server -Dlog4j.configurationFile="log4j2.xml" -Xms512m -Xmx1024m -Xmn256m

REM 启动游戏服务器
java %JAVA_OPTS% -jar gsxdb.jar -rmiport 41001 2>&1 | tee gs.log

echo.
echo [INFO] 游戏服务器已停止
pause
