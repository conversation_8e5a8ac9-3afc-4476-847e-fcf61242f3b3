


<MkioConf name="gs">
	<Manager bind="12" class="gnet.link.Onlines" name="Provider">
		<!-- ProviderServer -->
		<Connector inputBufferSize="131072" outputBufferSize="10485760" receiveBufferSize="16384" remoteIp="127.0.0.1" remotePort="43001" sendBufferSize="16384" tcpNoDelay="false" keepOutputBuffer="true" />
		<Coder comment="DO NOT EDIT CODER!">
		 <!--时空宝盒-->
             <Protocol class="fire.pb.game.SBeginSchoolWheel1" maxSize="1024"/>
             <Protocol class="fire.pb.game.CEndSchoolWheel1" maxSize="64"/>
             <Protocol class="fire.pb.game.CBeginSchoolWheel1" maxSize="65535"/>
          <!--时空结束-->
          <!--百宠开始-->
              <Protocol class="fire.pb.item.baichong.CWish" maxSize="65535"/>
              <Protocol class="fire.pb.item.baichong.SWishRet" maxSize="65535"/>
          <!--百宠-->
			<Protocol class="fire.pb.http.CRequestPost" maxSize="65535"/>
			<Protocol class="fire.pb.http.SResponsePost" maxSize="65535"/>
            <Protocol class="fire.pb.yichu.CYiChuGouMai" maxSize="65535"/>
            <Protocol class="fire.pb.yichu.CYiChuShiYong" maxSize="65535"/>
            <Protocol class="fire.pb.yichu.CYiChuYongYou" maxSize="65535"/>
            <Protocol class="fire.pb.yichu.SChangeYiChu" maxSize="65535"/>
            <Protocol class="fire.pb.yichu.SYiChuYongYou" maxSize="65535"/>
		   <Protocol class="fire.pb.item.CHuiShouItem" maxSize="65535"/>          
			<Protocol class="fire.pb.school.change.CChangeEquipEx" maxSize="65535"/>	
            <Protocol class="fire.pb.team.CAnswerforGetLeader" maxSize="65535"/>
			<Protocol class="fire.pb.team.CGetTeamLeader" maxSize="65535"/>
			<Protocol class="fire.pb.team.SAskforGetLeader" maxSize="65535"/>
			<Protocol class="fire.pb.item.CAllGemHeCheng" maxSize="128"/>				
			<Protocol class="fire.pb.pet.Coutpetequip" maxSize="65535"/>
			<Protocol class="fire.pb.school.change.CChangeGem33" maxSize="65535"/>
			<Protocol class="fire.pb.school.change.SChangeGem33" maxSize="65535"/>
			<Protocol class="fire.pb.team.CSetTeamLeader1" maxSize="65535"/>
			<Protocol class="fire.pb.item.CQiangHuaEquipItemAttr" maxSize="128"/>
			<Protocol class="fire.msp.BeanImport" maxSize="32"/>
			<Protocol class="fire.msp.GEnterWorld" maxSize="65535"/>
			<Protocol class="fire.pb.item.CAllEquipGemFenJie" maxSize="128"/>
			<Protocol class="fire.msp.GGetRolesByConditions" maxSize="65535"/>
			<Protocol class="fire.msp.GRefreshRoleEquip" maxSize="65535"/>
			<Protocol class="fire.msp.GRoleOfflineGs" maxSize="65535"/>
			<Protocol class="fire.msp.GSendAll" maxSize="65535"/>
			<Protocol class="fire.msp.GSendAllByCondition" maxSize="65535"/>
			<Protocol class="fire.msp.GSendAllByScene" maxSize="65535"/>
			<Protocol class="fire.msp.GSendAllBySchool" maxSize="65535"/>
			<Protocol class="fire.msp.GSendAroundByPos" maxSize="65535"/>
			<Protocol class="fire.msp.GSendToSceneByRole" maxSize="65535"/>
			<Protocol class="fire.msp.MEnterScene" maxSize="65535"/>
			<Protocol class="fire.msp.MGetRolesByConditions" maxSize="65535"/>
			<Protocol class="fire.msp.MRoleOfflineScene" maxSize="65535"/>
			<Protocol class="fire.msp.battle.GAddShowBattleLoser" maxSize="65535"/>
			<Protocol class="fire.msp.battle.GFortyThievesBattle" maxSize="65535"/>
			<Protocol class="fire.msp.battle.GRemoveMonstershow" maxSize="65535"/>
			<Protocol class="fire.msp.battle.MFortyThievesBattle" maxSize="65535"/>
			<Protocol class="fire.msp.battle.MStartShowNpcBattle" maxSize="65535"/>
			<Protocol class="fire.msp.circletask.GCreateNpcForCatchIt" maxSize="65535"/>
			<Protocol class="fire.msp.circletask.GCreatePosForCTGoto" maxSize="65535"/>
			<Protocol class="fire.msp.circletask.GFindShoolQuestDemonsNpc" maxSize="65535"/>
			<Protocol class="fire.msp.circletask.MCreateNpcForCatchIt" maxSize="65535"/>
			<Protocol class="fire.msp.circletask.MCreatePosForCTGoto" maxSize="65535"/>
			<Protocol class="fire.msp.move.GAddPowerRestore" maxSize="65535"/>
			<Protocol class="fire.msp.move.GCreateDynamicScene" maxSize="65535"/>
			<Protocol class="fire.msp.move.GCreateNpc" maxSize="65535"/>
			<Protocol class="fire.msp.move.GDestroyDynamicSceneById" maxSize="65535"/>
			<Protocol class="fire.msp.move.GDestroyDynamicSceneByMapOwner" maxSize="65535"/>
			<Protocol class="fire.msp.move.GEnterDynamicScene" maxSize="65535"/>
			<Protocol class="fire.pb.item.CCleanMainPack" maxSize="16"/>
			<Protocol class="fire.pb.item.COffAllEquip" maxSize="16"/>
			<Protocol class="fire.msp.move.GEnterDynamicSceneById" maxSize="65535"/>
			<Protocol class="fire.msp.move.GGoto" maxSize="65535"/>
			<Protocol class="fire.msp.move.GGotoRandom" maxSize="65535"/>
			<Protocol class="fire.msp.move.GGotoRandomInARegion" maxSize="65535"/>
			<Protocol class="fire.msp.move.GJumpDrawback" maxSize="65535"/>
			<Protocol class="fire.msp.move.GLeaveDynamicScene" maxSize="65535"/>
			<Protocol class="fire.msp.move.GMoveTest" maxSize="65535"/>
			<Protocol class="fire.msp.move.GNotifyMapPetInfo" maxSize="65535"/>
			<Protocol class="fire.msp.move.GRemoveNpc" maxSize="65535"/>
			<Protocol class="fire.msp.move.GRoleCampChange" maxSize="65535"/>
			<Protocol class="fire.msp.move.GRoleEquipChange" maxSize="65535"/>
			<Protocol class="fire.msp.move.GSetEnchouJump" maxSize="65535"/>
			<Protocol class="fire.msp.move.GUpdateDynamicScene" maxSize="65535"/>
			<Protocol class="fire.msp.move.GWeatherChange" maxSize="65535"/>
			<Protocol class="fire.msp.move.MAfterEnterDragonScene" maxSize="65535"/>
			<Protocol class="fire.msp.move.MCancelProgressBar" maxSize="65535"/>
			<Protocol class="fire.msp.move.MCreateDynamicScene" maxSize="65535"/>
			<Protocol class="fire.msp.move.MCreateWeather" maxSize="65535"/>
			<Protocol class="fire.msp.move.MDestroyDynamicScene" maxSize="65535"/>
			<Protocol class="fire.msp.move.MGotoAfterBattle" maxSize="65535"/>
			<Protocol class="fire.msp.move.MMoveCheckHideBattle" maxSize="65535"/>
			<Protocol class="fire.msp.move.MRoleGotoNotify" maxSize="65535"/>
			<Protocol class="fire.msp.move.MRoleJump" maxSize="65535"/>
			<Protocol class="fire.msp.move.MUpdateUnitPos" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GAddNpcToMiniMap" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GAddRobberNpc" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GChangeNpcShape" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GChangeShouxiShape" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GCheckCanPlayPK" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GCheckCanPlayPKView" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GCheckGathering" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GCheckNpcMove" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GCountNpcSize" maxSize="128"/>
			<Protocol class="fire.msp.npc.GCreateNPCByGridScale" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GCreateNPCByMap" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GCreateNPCByPos" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GCreateNPCWithRoleids" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GCreateNpcInRegion" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GCreateNpcUniversalReq" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GCreateYaoQianShuNpc" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GEnterOrOutSafeAear" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GFindNpcWithNpcId" maxSize="128"/>
			<Protocol class="fire.msp.npc.GGatherEnd" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GHideOrShowNpc" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GRemoveNpcFromMiniMap" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GRemoveNpcFromScene" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GSetNpcBattleState" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GSetNpcShape" maxSize="65535"/>
			<Protocol class="fire.msp.npc.GStartNpcAI" maxSize="65535"/>
			<Protocol class="fire.msp.npc.MAddRobberNpc" maxSize="65535"/>
			<Protocol class="fire.msp.npc.MCheckCanPlayPK" maxSize="65535"/>
			<Protocol class="fire.msp.npc.MCheckCanPlayPKView" maxSize="65535"/>
			<Protocol class="fire.msp.npc.MCountNpcSize" maxSize="128"/>
			<Protocol class="fire.msp.npc.MCreateNpcUniversalRet" maxSize="65535"/>
			<Protocol class="fire.msp.npc.MCreateYaoQianShuNpc" maxSize="65535"/>
			<Protocol class="fire.msp.npc.MNpcAIOver" maxSize="65535"/>
			<Protocol class="fire.msp.npc.MNpcMoveTimerControl" maxSize="65535"/>
			<Protocol class="fire.msp.npc.MRemoveNpcFromGS" maxSize="65535"/>
			<Protocol class="fire.msp.npc.MRemoveNpcsFromGS" maxSize="65535"/>
			<Protocol class="fire.msp.npc.MStartGather" maxSize="65535"/>
			<Protocol class="fire.msp.npc.MUpdateNpcInfo" maxSize="65535"/>
			<Protocol class="fire.msp.role.GAddBuffs" maxSize="65535"/>
			<Protocol class="fire.msp.role.GAddSceneState" maxSize="65535"/>
			<Protocol class="fire.msp.role.GAddUserScreen" maxSize="65535"/>
			<Protocol class="fire.msp.role.GChangeEquipEffect" maxSize="64"/>
			<Protocol class="fire.msp.role.GRemoveBuffs" maxSize="65535"/>
			<Protocol class="fire.msp.role.GRemoveRoleFromScene" maxSize="20"/>
			<Protocol class="fire.msp.role.GRemoveSceneState" maxSize="65535"/>
			<Protocol class="fire.msp.role.GRoleChangeShape" maxSize="64"/>
			<Protocol class="fire.msp.role.GRoleModifyName" maxSize="64"/>
			<Protocol class="fire.msp.role.GSendAround" maxSize="65535"/>
			<Protocol class="fire.msp.role.GSendAroundByCondition" maxSize="65535"/>
			<Protocol class="fire.msp.role.GSendAroundExceptMe" maxSize="65535"/>
			<Protocol class="fire.msp.role.GSetMaxScreenShowNum" maxSize="65535"/>
			<Protocol class="fire.msp.role.GSetNoPayDayState" maxSize="65535"/>
			<Protocol class="fire.msp.role.GSetNpcBattleInfo" maxSize="64"/>
			<Protocol class="fire.msp.role.GSetPlayCGState" maxSize="65535"/>
			<Protocol class="fire.msp.role.GSetProgressbarInfo" maxSize="65535"/>
			<Protocol class="fire.msp.role.GSetRoleBattleInfo" maxSize="65535"/>
			<Protocol class="fire.msp.role.GSetRoleHideOrShow" maxSize="65535"/>
			<Protocol class="fire.msp.role.GUpdateRoleLevel" maxSize="65535"/>
			<Protocol class="fire.msp.task.GChangeShape" maxSize="65535"/>
			<Protocol class="fire.msp.task.GGenTreasuremapPos" maxSize="65535"/>
			<Protocol class="fire.msp.task.GScenarioQuestUseItemVerifyPos" maxSize="65535"/>
			<Protocol class="fire.msp.task.GScenarioQuestVerifyPos" maxSize="65535"/>
			<Protocol class="fire.msp.task.GSpecailQuestUseItemVerifyPos" maxSize="65535"/>
			<Protocol class="fire.msp.task.MGenTreasuremapPos" maxSize="65535"/>
			<Protocol class="fire.msp.task.MScenarioQuestUseItemVerifyPosFail" maxSize="65535"/>
			<Protocol class="fire.msp.task.MScenarioQuestUseItemVerifyPosSucc" maxSize="65535"/>
			<Protocol class="fire.msp.task.MScenarioQuestVerifyPosFail" maxSize="65535"/>
			<Protocol class="fire.msp.task.MScenarioQuestVerifyPosSucc" maxSize="65535"/>
			<Protocol class="fire.msp.task.MSpecailQuestUseItemVerifyPosFail" maxSize="65535"/>
			<Protocol class="fire.msp.task.MSpecailQuestUseItemVerifyPosSucc" maxSize="65535"/>
			<Protocol class="fire.msp.team.GCreateTeam" maxSize="65535"/>
			<Protocol class="fire.msp.team.GDismissTeam" maxSize="65535"/>
			<Protocol class="fire.msp.team.GNotifyTeamChange" maxSize="65535"/>
			<Protocol class="fire.msp.team.GRefreshTeamHugs" maxSize="65535"/>
			<Protocol class="fire.msp.team.GRequestSingleRoles" maxSize="65535"/>
			<Protocol class="fire.msp.team.GRequestTeamList" maxSize="65535"/>
			<Protocol class="fire.msp.team.GUpdateTeam" maxSize="65535"/>
			<Protocol class="fire.msp.team.MRespondTeamList" maxSize="65535"/>
			<Protocol class="fire.msp.title.GNotifyTitle" maxSize="65535"/>
			<Protocol class="fire.pb.BeanImport" maxSize="32"/>
			<Protocol class="fire.pb.CAddPointToAttr" maxSize="65535"/>
			<Protocol class="fire.pb.CAfterEnterWorld" maxSize="65535"/>
			<Protocol class="fire.pb.CBeginnerTip" maxSize="65535"/>
			<Protocol class="fire.pb.CBeginnerTipShowed" maxSize="65535"/>
			<Protocol class="fire.pb.CBindTel" maxSize="256"/>
			<Protocol class="fire.pb.CCancelForceDelPassword" maxSize="256"/>
			<Protocol class="fire.pb.CChangePointScheme" maxSize="65535"/>
			<Protocol class="fire.pb.CCloseGoodLocks" maxSize="256"/>
			<Protocol class="fire.pb.CCreateRole" maxSize="65535"/>
			<Protocol class="fire.pb.CDelPassword" maxSize="256"/>
			<Protocol class="fire.pb.CEndPlayCG" maxSize="65535"/>
			<Protocol class="fire.pb.CEnterWorld" maxSize="65535"/>
			<Protocol class="fire.pb.CForceDelPassword" maxSize="256"/>
			<Protocol class="fire.pb.CForceUnlockTimeExpire" maxSize="256"/>
			<Protocol class="fire.pb.CGameTime" maxSize="65535"/>
			<Protocol class="fire.pb.CGetBindTel" maxSize="256"/>
			<Protocol class="fire.pb.CGetBindTelAward" maxSize="256"/>
			<Protocol class="fire.pb.CGetCheckCode" maxSize="256"/>
			<Protocol class="fire.pb.CGetGoodLocksInfo" maxSize="256"/>
			<Protocol class="fire.pb.CGetSysConfig" maxSize="65535"/>
			<Protocol class="fire.pb.CGoodUnLock" maxSize="256"/>
			<Protocol class="fire.pb.CKick" maxSize="65535"/>
			<Protocol class="fire.pb.CModifyRoleName" maxSize="65535"/>
			<Protocol class="fire.pb.CNotifyDeviceInfo" maxSize="65535"/>
			<Protocol class="fire.pb.COpenGoodLocks" maxSize="256"/>
			<Protocol class="fire.pb.CPingStat" maxSize="65535"/>
			<Protocol class="fire.pb.CReceiveCheckCode" maxSize="256"/>
			<Protocol class="fire.pb.CReqChargeMoney" maxSize="64"/>
			<Protocol class="fire.pb.CReqColorRoomView" maxSize="65535"/>
			<Protocol class="fire.pb.CReqDelColor" maxSize="65535"/>
			<Protocol class="fire.pb.CReqHelpCountView" maxSize="65535"/>
			<Protocol class="fire.pb.CReqOldName" maxSize="65535"/>
			<Protocol class="fire.pb.CReqPointSchemeTime" maxSize="65535"/>
			<Protocol class="fire.pb.CReqRoleInfo" maxSize="64"/>
			<Protocol class="fire.pb.CReqRoleProp" maxSize="65535"/>
			<Protocol class="fire.pb.CReqRoleTeamState" maxSize="65535"/>
			<Protocol class="fire.pb.CReqUseColor" maxSize="65535"/>
			<Protocol class="fire.pb.CReqUsePetColor" maxSize="65535"/>
			<Protocol class="fire.pb.CRequestName" maxSize="65535"/>
			<Protocol class="fire.pb.CRequestRolePos" maxSize="1024"/>
			<Protocol class="fire.pb.CResetPassword" maxSize="256"/>
			<Protocol class="fire.pb.CResetSysConfig" maxSize="65535"/>
			<Protocol class="fire.pb.CReturnRoleList" maxSize="32"/>
			<Protocol class="fire.pb.CReturnToLogin" maxSize="32"/>
			<Protocol class="fire.pb.CRoleList" maxSize="32"/>
			<Protocol class="fire.pb.CRoleOffline" maxSize="65535"/>
			<Protocol class="fire.pb.CSendCBGCheckCode" maxSize="256"/>
			<Protocol class="fire.pb.CSendHelpSW" maxSize="65535"/>
			<Protocol class="fire.pb.CSendSn" maxSize="64"/>
			<Protocol class="fire.pb.CSetLineConfig" maxSize="256"/>
			<Protocol class="fire.pb.CSetMaxScreenShowNum" maxSize="65535"/>
			<Protocol class="fire.pb.CSetPassword" maxSize="256"/>
			<Protocol class="fire.pb.CSetPilotType" maxSize="10"/>
			<Protocol class="fire.pb.CTeamVoteAgree" maxSize="65535"/>
			<Protocol class="fire.pb.CUnBindTel" maxSize="256"/>
			<Protocol class="fire.pb.CUserOffline" maxSize="65535"/>
			<Protocol class="fire.pb.LuaBeanImport" maxSize="32"/>
			<Protocol class="fire.pb.LuaBeanImport3" maxSize="32"/>
			<Protocol class="fire.pb.SAddPointAttrData" maxSize="65535"/>
			<Protocol class="fire.pb.SAnswerRoleTeamState" maxSize="65535"/>
			<Protocol class="fire.pb.SBeginnerTip" maxSize="65535"/>
			<Protocol class="fire.pb.SBindTel" maxSize="256"/>
			<Protocol class="fire.pb.SBindTelAgain" maxSize="256"/>
			<Protocol class="fire.pb.SCBGUpCheckCode" maxSize="256"/>
			<Protocol class="fire.pb.SCancelForceDelPassword" maxSize="256"/>
			<Protocol class="fire.pb.SChangeGoodLockState" maxSize="256"/>
			<Protocol class="fire.pb.SCheckCodeFinishTime" maxSize="256"/>
			<Protocol class="fire.pb.SCheckCodeFinishTimePoint" maxSize="256"/>
			<Protocol class="fire.pb.SCloseGoodLocks" maxSize="256"/>
			<Protocol class="fire.pb.SCreateRole" maxSize="65535"/>
			<Protocol class="fire.pb.SCreateRoleError" maxSize="65535"/>
			<Protocol class="fire.pb.SDelPassword" maxSize="256"/>
			<Protocol class="fire.pb.SEnterWorld" maxSize="65535"/>
			<Protocol class="fire.pb.SError" maxSize="65535"/>
			<Protocol class="fire.pb.SFirstDayExitGame" maxSize="65535"/>
			<Protocol class="fire.pb.SForceDelPassword" maxSize="256"/>
			<Protocol class="fire.pb.SForceUnlockTimeExpire" maxSize="256"/>
			<Protocol class="fire.pb.SGACDKickoutMsg" maxSize="65535"/>
			<Protocol class="fire.pb.SGACDKickoutMsg1" maxSize="65535"/>
			<Protocol class="fire.pb.SGameTime" maxSize="65535"/>
			<Protocol class="fire.pb.SGetBindTel" maxSize="256"/>
			<Protocol class="fire.pb.SGetBindTelAward" maxSize="256"/>
			<Protocol class="fire.pb.SGetGoodLocksInfo" maxSize="256"/>
			<Protocol class="fire.pb.SGetSysConfig" maxSize="65535"/>
			<Protocol class="fire.pb.SGiveName" maxSize="65535"/>
			<Protocol class="fire.pb.SGoodUnLock" maxSize="256"/>
			<Protocol class="fire.pb.SModifyRoleName" maxSize="65535"/>
			<Protocol class="fire.pb.SNotifyDeviceInfo" maxSize="65535"/>
			<Protocol class="fire.pb.SNotifyShieldState" maxSize="65535"/>
			<Protocol class="fire.pb.SOpenGoodLocks" maxSize="256"/>
			<Protocol class="fire.pb.SRecommendsNames" maxSize="65535"/>
			<Protocol class="fire.pb.SRefSmoney" maxSize="65535"/>
			<Protocol class="fire.pb.SRefreshHp" maxSize="65535"/>
			<Protocol class="fire.pb.SRefreshUserExp" maxSize="65535"/>
			<Protocol class="fire.pb.SReqColorRoomView" maxSize="65535"/>
			<Protocol class="fire.pb.SReqHelpCountView" maxSize="65535"/>
			<Protocol class="fire.pb.SReqPointSchemeTime" maxSize="65535"/>
			<Protocol class="fire.pb.SReqUseColor" maxSize="65535"/>
			<Protocol class="fire.pb.SReqUsePetColor" maxSize="65535"/>
			<Protocol class="fire.pb.SRequestUsedNameData" maxSize="65535"/>
			<Protocol class="fire.pb.SResetPassword" maxSize="256"/>
			<Protocol class="fire.pb.SResetSysConfig" maxSize="65535"/>
			<Protocol class="fire.pb.SRetRoleProp" maxSize="65535"/>
			<Protocol class="fire.pb.SReturnLogin" maxSize="32"/>
			<Protocol class="fire.pb.SReturnRoleList" maxSize="4096"/>
			<Protocol class="fire.pb.SRoleList" maxSize="4096"/>
			<Protocol class="fire.pb.SRoleOffline" maxSize="65535"/>
			<Protocol class="fire.pb.SRspRoleInfo" maxSize="65535"/>
			<Protocol class="fire.pb.SSendHelpSW" maxSize="65535"/>
			<Protocol class="fire.pb.SSendLoginIp" maxSize="1024"/>
			<Protocol class="fire.pb.SSendQueueInfo" maxSize="65535"/>
			<Protocol class="fire.pb.SSendServerMultiExp" maxSize="8"/>
			<Protocol class="fire.pb.SSendSlowQueueInfo" maxSize="65535"/>
			<Protocol class="fire.pb.SServerIDResponse" maxSize="65535"/>
			<Protocol class="fire.pb.SServerLevel" maxSize="128"/>
			<Protocol class="fire.pb.SSetFunOpenClose" maxSize="65535"/>
			<Protocol class="fire.pb.SSetLineConfig" maxSize="256"/>
			<Protocol class="fire.pb.SSetPassword" maxSize="256"/>
			<Protocol class="fire.pb.SShowedBeginnerTips" maxSize="65535"/>
			<Protocol class="fire.pb.SStartPlayCG" maxSize="65535"/>
			<Protocol class="fire.pb.STeamVote" maxSize="65535"/>
			<Protocol class="fire.pb.SUnBindTel" maxSize="256"/>
			<Protocol class="fire.pb.SUserNeedActive" maxSize="65535"/>
			<Protocol class="fire.pb.activity.exchangecode.CExchangeCode" maxSize="65535"/>
			<Protocol class="fire.pb.activity.exchangecode.CQQExchangeCodeStatus" maxSize="32"/>
			<Protocol class="fire.pb.activity.exchangecode.SExchangeCode" maxSize="65535"/>
			<Protocol class="fire.pb.activity.exchangecode.SQQExchangeCodeStatus" maxSize="32"/>
			<Protocol class="fire.pb.activity.festival.CGetFestivalReward" maxSize="32"/>
			<Protocol class="fire.pb.activity.festival.CQueryFestivalData" maxSize="32"/>
			<Protocol class="fire.pb.activity.festival.SQueryFestivalData" maxSize="65535"/>
			<Protocol class="fire.pb.activity.reg.CQueryRegData" maxSize="32"/>
			<Protocol class="fire.pb.activity.reg.CReg" maxSize="32"/>
			<Protocol class="fire.pb.activity.reg.SQueryRegData" maxSize="2048"/>
			<Protocol class="fire.pb.attr.CApplyYingFuExprience" maxSize="65535"/>
			<Protocol class="fire.pb.attr.SApplyYingFuExprience" maxSize="65535"/>
			<Protocol class="fire.pb.attr.SRefreshPetData" maxSize="65535"/>
			<Protocol class="fire.pb.attr.SRefreshPointType" maxSize="65535"/>
			<Protocol class="fire.pb.attr.SRefreshRoleCurrency" maxSize="65535"/>
			<Protocol class="fire.pb.attr.SRefreshRoleData" maxSize="65535"/>
			<Protocol class="fire.pb.attr.SRefreshRoleScore" maxSize="65535"/>
			<Protocol class="fire.pb.battle.CEndWatchBattle" maxSize="65535"/>
			<Protocol class="fire.pb.battle.CInvitationPlayPK" maxSize="65535"/>
			<Protocol class="fire.pb.battle.CInvitationPlayPKResult" maxSize="65535"/>
			<Protocol class="fire.pb.battle.CPlayPKFightView" maxSize="65535"/>
			<Protocol class="fire.pb.battle.CReqCameraUrl" maxSize="65535"/>
			<Protocol class="fire.pb.battle.CReqRePlay" maxSize="65535"/>
			<Protocol class="fire.pb.battle.CSendAction" maxSize="65535"/>
			<Protocol class="fire.pb.battle.CSendInvitePlayPK" maxSize="65535"/>
			<Protocol class="fire.pb.battle.CSendRoundPlayEnd" maxSize="65535"/>
			<Protocol class="fire.pb.battle.CSendWatchBattle" maxSize="65535"/>
			<Protocol class="fire.pb.battle.CStopRePlay" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SDeadLess20" maxSize="1024"/>
			<Protocol class="fire.pb.battle.SInvitationPlayPK" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SInvitationPlayPKResult" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SPlayPKFightView" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SRemoveWatcher" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SReqRePlay" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SSendAddFighters" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SSendAlreadyUseItem" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SSendBattleEnd" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SSendBattleStart" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SSendBattlerOperateState" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SSendCameraUrl" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SSendPetInitAttrs" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SSendRoleInitAttrs" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SSendRoundPlayEnd" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SSendRoundScript" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SSendRoundStart" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SSendWatchBattleStart" maxSize="65535"/>
			<Protocol class="fire.pb.battle.SSynchroBossHp" maxSize="65535"/>
			<Protocol class="fire.pb.battle.battleflag.CModifyBattleFlag" maxSize="65535"/>
			<Protocol class="fire.pb.battle.battleflag.CReqBattleFlag" maxSize="10"/>
			<Protocol class="fire.pb.battle.battleflag.CSetBattleFlag" maxSize="65535"/>
			<Protocol class="fire.pb.battle.battleflag.CSetCommander" maxSize="65535"/>
			<Protocol class="fire.pb.battle.battleflag.SModifyBattleFlag" maxSize="65535"/>
			<Protocol class="fire.pb.battle.battleflag.SSendBattleFlag" maxSize="65535"/>
			<Protocol class="fire.pb.battle.battleflag.SSetBattleFlag" maxSize="65535"/>
			<Protocol class="fire.pb.battle.battleflag.SSetCommander" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.CAcceptInvitationLiveDieBattle" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.CAcceptLiveDieBattle" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.CAcceptLiveDieBattleFirst" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.CInvitationLiveDieBattle" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.CInvitationLiveDieBattleOK" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.CLiveDieBattleGiveRose" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.CLiveDieBattleRankView" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.CLiveDieBattleWatchFight" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.CLiveDieBattleWatchVideo" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.CLiveDieBattleWatchView" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.SAcceptInvitationLiveDieBattle" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.SAcceptLiveDieBattleFirst" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.SInvitationLiveDieBattle" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.SInvitationLiveDieBattleOK" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.SLiveDieBattleGiveRose" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.SLiveDieBattleRankView" maxSize="65535"/>
			<Protocol class="fire.pb.battle.livedie.SLiveDieBattleWatchView" maxSize="65535"/>
			<Protocol class="fire.pb.battle.newhand.CReqNewHandBattle" maxSize="128"/>
			<Protocol class="fire.pb.battle.pvp1.CPvP1MyInfo" maxSize="65535"/>
			<Protocol class="fire.pb.battle.pvp1.CPvP1OpenBox" maxSize="1024"/>
			<Protocol class="fire.pb.battle.pvp1.CPvP1RankingList" maxSize="1024"/>
			<Protocol class="fire.pb.battle.pvp1.CPvP1ReadyFight" maxSize="1024"/>
			<Protocol class="fire.pb.battle.pvp1.SPvP1BattleInfo" maxSize="65535"/>
			<Protocol class="fire.pb.battle.pvp1.SPvP1MatchResult" maxSize="65535"/>
			<Protocol class="fire.pb.battle.pvp1.SPvP1MyInfo" maxSize="65535"/>
			<Protocol class="fire.pb.battle.pvp1.SPvP1OpenBoxState" maxSize="1024"/>
			<Protocol class="fire.pb.battle.pvp1.SPvP1RankingList" maxSize="65535"/>
			<Protocol class="fire.pb.battle.pvp1.SPvP1ReadyFight" maxSize="1024"/>
			<Protocol class="fire.pb.battle.pvp3.CPvP3MyInfo" maxSize="65535"/>
			<Protocol class="fire.pb.battle.pvp3.CPvP3OpenBox" maxSize="1024"/>
			<Protocol class="fire.pb.battle.pvp3.CPvP3RankingList" maxSize="1024"/>
			<Protocol class="fire.pb.battle.pvp3.CPvP3ReadyFight" maxSize="1024"/>
			<Protocol class="fire.pb.battle.pvp3.SPvP3BattleInfo" maxSize="65535"/>
			<Protocol class="fire.pb.battle.pvp3.SPvP3MatchResult" maxSize="65535"/>
			<Protocol class="fire.pb.battle.pvp3.SPvP3MyInfo" maxSize="65535"/>
			<Protocol class="fire.pb.battle.pvp3.SPvP3OpenBoxState" maxSize="1024"/>
			<Protocol class="fire.pb.battle.pvp3.SPvP3RankingList" maxSize="65535"/>
			<Protocol class="fire.pb.battle.pvp3.SPvP3ReadyFight" maxSize="1024"/>
			<Protocol class="fire.pb.battle.pvp5.CPvP5MyInfo" maxSize="65535"/>
			<Protocol class="fire.pb.battle.pvp5.CPvP5OpenBox" maxSize="1024"/>
			<Protocol class="fire.pb.battle.pvp5.CPvP5RankingList" maxSize="1024"/>
			<Protocol class="fire.pb.battle.pvp5.SPvP5BattleInfo" maxSize="65535"/>
			<Protocol class="fire.pb.battle.pvp5.SPvP5MatchResult" maxSize="65535"/>
			<Protocol class="fire.pb.battle.pvp5.SPvP5MyInfo" maxSize="65535"/>
			<Protocol class="fire.pb.battle.pvp5.SPvP5OpenBoxState" maxSize="1024"/>
			<Protocol class="fire.pb.battle.pvp5.SPvP5RankingList" maxSize="65535"/>
			<Protocol class="fire.pb.battle.pvp5.SPvP5ReadyFight" maxSize="65535"/>
			<Protocol class="fire.pb.buff.SBuffChangeResult" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.CAbandonQuest" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.CQueryCircleTaskState" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.CQuestionnaireResult" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.CRenXingCircleTask" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.CReqGotoPatrol" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.SQueryCircleTaskState" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.SQuestionnaireTitleAndExp" maxSize="655350"/>
			<Protocol class="fire.pb.circletask.SRefreshActiveQuest" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.SRefreshQuestData" maxSize="655350"/>
			<Protocol class="fire.pb.circletask.SRefreshSpecialQuest" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.SRefreshSpecialQuestState" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.SRenXingCircleTask" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.SSendActiveQuestList" maxSize="655350"/>
			<Protocol class="fire.pb.circletask.anye.CAbandonAnYe" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.anye.CLengendAnYetask" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.anye.CRenXingAnYeTask" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.anye.CSetAnYeJoinTime" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.anye.CSubmitThings" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.anye.SLengendAnYetask" maxSize="65535"/>
			<Protocol class="fire.pb.circletask.anye.SRefreshAnYeData" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CAcceptOrRefuseApply" maxSize="64"/>
			<Protocol class="fire.pb.clan.CAcceptOrRefuseInvitation" maxSize="64"/>
			<Protocol class="fire.pb.clan.CApplyClan" maxSize="64"/>
			<Protocol class="fire.pb.clan.CBannedtalk" maxSize="256"/>
			<Protocol class="fire.pb.clan.CBonusQuery" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CBuyMedic" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CCancelApplyClan" maxSize="256"/>
			<Protocol class="fire.pb.clan.CChangeClanAim" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CChangeClanInst" maxSize="256"/>
			<Protocol class="fire.pb.clan.CChangeClanName" maxSize="256"/>
			<Protocol class="fire.pb.clan.CChangePosition" maxSize="64"/>
			<Protocol class="fire.pb.clan.CClanInvitation" maxSize="64"/>
			<Protocol class="fire.pb.clan.CClanInvitationView" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CClanMessage" maxSize="256"/>
			<Protocol class="fire.pb.clan.CClearApplyList" maxSize="64"/>
			<Protocol class="fire.pb.clan.CCreateClan" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CEnterClanMap" maxSize="256"/>
			<Protocol class="fire.pb.clan.CFireMember" maxSize="64"/>
			<Protocol class="fire.pb.clan.CGrabBonus" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CLeaveClan" maxSize="64"/>
			<Protocol class="fire.pb.clan.COneKeyApplyClan" maxSize="256"/>
			<Protocol class="fire.pb.clan.COpenAutoJoinClan" maxSize="65535"/>
			<Protocol class="fire.pb.clan.COpenClan" maxSize="64"/>
			<Protocol class="fire.pb.clan.COpenClanList" maxSize="64"/>
			<Protocol class="fire.pb.clan.COpenClanMedic" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CRefreshMemberList" maxSize="256"/>
			<Protocol class="fire.pb.clan.CRefreshRoleClan" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CRequestApplyList" maxSize="64"/>
			<Protocol class="fire.pb.clan.CRequestClanAim" maxSize="64"/>
			<Protocol class="fire.pb.clan.CRequestClanLevelup" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CRequestEventInfo" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CRequestImpeachMentView" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CRequestRoleInfo" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CRequestRuneCount" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CRequestRuneInfo" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CRequestSearchRole" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CRequestSelectType" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CRuneGive" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CRuneRequest" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CRuneRequestView" maxSize="65535"/>
			<Protocol class="fire.pb.clan.CSearchClan" maxSize="256"/>
			<Protocol class="fire.pb.clan.SAcceptApply" maxSize="512"/>
			<Protocol class="fire.pb.clan.SApplyClanList" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SBannedtalk" maxSize="256"/>
			<Protocol class="fire.pb.clan.SBonusQuery" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SBuyMedic" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SCancelApplyClan" maxSize="256"/>
			<Protocol class="fire.pb.clan.SChangeClanAim" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SChangeClanInst" maxSize="256"/>
			<Protocol class="fire.pb.clan.SChangeClanName" maxSize="256"/>
			<Protocol class="fire.pb.clan.SClanAim" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SClanInvitation" maxSize="1024"/>
			<Protocol class="fire.pb.clan.SClanInvitationView" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SClanLevelup" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SClanRedTip" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SFireMember" maxSize="64"/>
			<Protocol class="fire.pb.clan.SGrabBonus" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SLeaveClan" maxSize="64"/>
			<Protocol class="fire.pb.clan.SOpenAutoJoinClan" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SOpenClan" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SOpenClanList" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SOpenClanMedic" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SRefreshContribution" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SRefreshMemberList" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SRefreshPosition" maxSize="128"/>
			<Protocol class="fire.pb.clan.SRefreshRoleClan" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SRefuseApply" maxSize="128"/>
			<Protocol class="fire.pb.clan.SRequestApply" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SRequestEventInfo" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SRequestImpeachMentView" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SRequestRoleInfo" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SRequestRuneCount" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SRequestRuneInfo" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SRequestSearchRole" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SRequestSelectType" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SRuneGive" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SRuneRequest" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SRuneRequestView" maxSize="65535"/>
			<Protocol class="fire.pb.clan.SSearchClan" maxSize="256"/>
			<Protocol class="fire.pb.clan.fight.CBattleFieldRankList" maxSize="65535"/>
			<Protocol class="fire.pb.clan.fight.CBattleFieldScore" maxSize="65535"/>
			<Protocol class="fire.pb.clan.fight.CGetClanFightList" maxSize="65535"/>
			<Protocol class="fire.pb.clan.fight.CGetClearTime" maxSize="65536"/>
			<Protocol class="fire.pb.clan.fight.CRequestRoleIsEnemy" maxSize="65535"/>
			<Protocol class="fire.pb.clan.fight.CStartClanFightBattle" maxSize="65535"/>
			<Protocol class="fire.pb.clan.fight.SBattleFieldAct" maxSize="65535"/>
			<Protocol class="fire.pb.clan.fight.SBattleFieldInfo" maxSize="65535"/>
			<Protocol class="fire.pb.clan.fight.SBattleFieldRankList" maxSize="65535"/>
			<Protocol class="fire.pb.clan.fight.SBattleFieldScore" maxSize="65535"/>
			<Protocol class="fire.pb.clan.fight.SClanFightOver" maxSize="65535"/>
			<Protocol class="fire.pb.clan.fight.SGetClanFightList" maxSize="65535"/>
			<Protocol class="fire.pb.clan.fight.SGetClearTime" maxSize="65536"/>
			<Protocol class="fire.pb.clan.fight.SLeaveBattleField" maxSize="65536"/>
			<Protocol class="fire.pb.clan.fight.SRequestRoleIsEnemy" maxSize="65535"/>
			<Protocol class="fire.pb.cross.CSendAllServerMsg" maxSize="1024"/>
			<Protocol class="fire.pb.cross.FinishCopyRole" maxSize="1048576"/>
			<Protocol class="fire.pb.cross.SBeginCorssServer" maxSize="65535"/>
			<Protocol class="fire.pb.cross.SendRoleData" maxSize="1048576"/>
			<Protocol class="fire.pb.cross.SendRoleInfo" maxSize="1024"/>
			<Protocol class="fire.pb.cross.SendRoleInfo_Rep" maxSize="1024"/>
			<Protocol class="fire.pb.cross.SendWordMsg" maxSize="128"/>
			<Protocol class="fire.pb.friends.CBreakOffRelation" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CCampPK" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CChangeBaseConfig" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CGetRecruitAward" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CGetRolesLevel" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CGetSpaceInfo" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CGetXshSpaceInfo" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CGiveGift" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CGiveInfoList" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CGiveItem" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CRecommendFriend" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CReqJionCamp" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CRequestAddFriend" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CRequestSearchFriend" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CRequestSpaceRoleInfo" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CRequestUpdateRoleInfo" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CSendMessageToRole" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CSetSign" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CSetSpaceGift" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CStepSpace" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CXshGiveGift" maxSize="65535"/>
			<Protocol class="fire.pb.friends.CXshSpace" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SAddFriend" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SBreakOffRelation" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SChangeBaseConfig" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SFriendMessageToRole" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SFriendsInfoInit" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SFriendsOnline" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SGetRecruitAward" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SGetRolesLevel" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SGetSpaceInfo" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SGetXshSpaceInfo" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SGiveGift" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SGiveInfoList" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SGiveItem" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SJionCamp" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SOffLineMsgMessageToRole" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SRecommendFriend" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SRequestSpaceRoleInfo" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SRequestUpdateRoleInfo" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SSearchFriend" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SSendSystemMessageToRole" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SSetSpaceGift" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SSignList" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SStepSpace" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SStrangerMessageToRole" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SUpdateFriendLevel" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SUpdateFriendState" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SXshGiveGift" maxSize="65535"/>
			<Protocol class="fire.pb.friends.SXshSpace" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.CConfirmCharge" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.CGetChargeRefunds" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.CGetFirstPayReward" maxSize="255"/>
			<Protocol class="fire.pb.fushi.CGrabChargeReturnProfitReward" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.CReqCharge" maxSize="255"/>
			<Protocol class="fire.pb.fushi.CReqChargeRefundsInfo" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.CReqFushiInfo" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.CReqServerId" maxSize="256"/>
			<Protocol class="fire.pb.fushi.CRequestChargeReturnProfit" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.CRequestVipInfo" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.CRequestVipJiangli" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.CUpYingYongBaoInfo" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.SConfirmCharge" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.SGetChargeRefunds" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.SGrabChargeReturnProfitReward" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.SRefreshChargeState" maxSize="255"/>
			<Protocol class="fire.pb.fushi.SReqCharge" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.SReqChargeRefundsInfo" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.SReqFushiInfo" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.SReqFushiNum" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.SRequestChargeReturnProfit" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.SRspServerId" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.SSendVipInfo" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.monthcard.CBuyMonthCard" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.monthcard.CGrabMonthCardReward" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.monthcard.CGrabMonthCardRewardAll" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.monthcard.CRequestMonthCard" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.monthcard.SMonthCard" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.payday.CQueryConsumeDayPay" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.payday.CQuerySubscribeInfo" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.payday.SConsumeDayPay" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.payday.SHaveDayPay" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.payday.SPayServerType" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.payday.SQueryConsumeDayPay" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.payday.SQuerySubscribeInfo" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.redpack.CGetRedPack" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.redpack.CSendRedPack" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.redpack.CSendRedPackHisView" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.redpack.CSendRedPackRoleRecordView" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.redpack.CSendRedPackView" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.redpack.SGetRedPack" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.redpack.SNoticeRedPack" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.redpack.SNoticeRedPackList" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.redpack.SSendRedPack" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.redpack.SSendRedPackHisView" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.redpack.SSendRedPackRoleRecordView" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.redpack.SSendRedPackView" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.spotcheck.CBuySpotCard" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.spotcheck.CCancelTrading" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.spotcheck.CRoleTradingRecordView" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.spotcheck.CRoleTradingView" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.spotcheck.CSellSpotCard" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.spotcheck.CTradingOpenState" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.spotcheck.CTradingSpotCardView" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.spotcheck.SBuySpotCard" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.spotcheck.SCancelTrading" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.spotcheck.SRoleTradingRecordView" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.spotcheck.SRoleTradingView" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.spotcheck.SSellSpotCard" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.spotcheck.STradingOpenState" maxSize="65535"/>
			<Protocol class="fire.pb.fushi.spotcheck.STradingSpotCardView" maxSize="65535"/>
			<Protocol class="fire.pb.game.CBeginSchoolWheel" maxSize="64"/>
			<Protocol class="fire.pb.game.CBenefitCodeExchange" maxSize="128"/>
			<Protocol class="fire.pb.game.CGetRecharge" maxSize="64"/>
			<Protocol class="fire.pb.game.CReceiveReward" maxSize="64"/>
			<Protocol class="fire.pb.game.CRefreshDayAward" maxSize="64"/>
			<Protocol class="fire.pb.game.CBeginXueYueWheel" maxSize="64"/>
			<Protocol class="fire.pb.game.CClientTime" maxSize="64"/>
			<Protocol class="fire.pb.game.CEndSchoolWheel" maxSize="64"/>
			<Protocol class="fire.pb.game.CEndXueYueWheel" maxSize="64"/>
			<Protocol class="fire.pb.game.CLogPushToken" maxSize="64"/>
			<Protocol class="fire.pb.game.CNoOperationKick" maxSize="64"/>
			<Protocol class="fire.pb.game.CRoleAccusationCheck" maxSize="64"/>
			<Protocol class="fire.pb.game.CUseXueYueKey" maxSize="64"/>
			<Protocol class="fire.pb.game.SBeginSchoolWheel" maxSize="1024"/>
			<Protocol class="fire.pb.game.SBeginXueYueWheel" maxSize="64"/>
			<Protocol class="fire.pb.game.SRoleAccusation" maxSize="64"/>
			<Protocol class="fire.pb.game.SRoleAccusationCheck" maxSize="64"/>
			<Protocol class="fire.pb.game.SOutReawardResult" maxSize="64"/>
			<Protocol class="fire.pb.game.SOutRecharge" maxSize="1024"/>
			<Protocol class="fire.pb.game.SUseXueYueKey" maxSize="1024"/>
			<Protocol class="fire.pb.gm.CCheckGM" maxSize="65535"/>
			<Protocol class="fire.pb.gm.CGMCheckRoleID" maxSize="65535"/>
			<Protocol class="fire.pb.gm.CSendCommand" maxSize="65535"/>
			<Protocol class="fire.pb.gm.SCheckGM" maxSize="65535"/>
			<Protocol class="fire.pb.gm.SGMCheckRoleID" maxSize="65535"/>
			<Protocol class="fire.pb.hook.CBuyDPoint" maxSize="65535"/>
			<Protocol class="fire.pb.hook.CClientLockScreen" maxSize="10"/>
			<Protocol class="fire.pb.hook.CFreezeDPoint" maxSize="65535"/>
			<Protocol class="fire.pb.hook.CGetDPoint" maxSize="65535"/>
			<Protocol class="fire.pb.hook.CGetRoleFightAI" maxSize="128"/>
			<Protocol class="fire.pb.hook.CSetAutoBattle" maxSize="65535"/>
			<Protocol class="fire.pb.hook.CSetCharOpt" maxSize="65535"/>
			<Protocol class="fire.pb.hook.CSetPetOpt" maxSize="65535"/>
			<Protocol class="fire.pb.hook.CSetRoleFightAI" maxSize="128"/>
			<Protocol class="fire.pb.hook.SFlushRoleFightAI" maxSize="128"/>
			<Protocol class="fire.pb.hook.SRefreshRoleHookBattleData" maxSize="65535"/>
			<Protocol class="fire.pb.hook.SRefreshRoleHookData" maxSize="65535"/>
			<Protocol class="fire.pb.hook.SRefreshRoleHookExpData" maxSize="65535"/>
			<Protocol class="fire.pb.huoban.CActiveHuoBan" maxSize="1024"/>
			<Protocol class="fire.pb.huoban.CGetHuoBanList" maxSize="64"/>
			<Protocol class="fire.pb.huoban.CGetHuobanDetailInfo" maxSize="128"/>
			<Protocol class="fire.pb.huoban.CSwitchZhenfa" maxSize="128"/>
			<Protocol class="fire.pb.huoban.CSwitchZhenrong" maxSize="128"/>
			<Protocol class="fire.pb.huoban.CZhenrongMember" maxSize="128"/>
			<Protocol class="fire.pb.huoban.SActiveHuoBan" maxSize="128"/>
			<Protocol class="fire.pb.huoban.SChangeZhenrong" maxSize="1024"/>
			<Protocol class="fire.pb.huoban.SHuobanDetail" maxSize="65535"/>
			<Protocol class="fire.pb.huoban.SHuobanList" maxSize="65535"/>
			<Protocol class="fire.pb.huoban.SSwitchZhenfa" maxSize="128"/>
			<Protocol class="fire.pb.huoban.SZhenrongInfo" maxSize="65535"/>
			<Protocol class="fire.pb.instancezone.SGongHuiFuBenLastTime" maxSize="1024"/>
			<Protocol class="fire.pb.instancezone.bingfeng.CBattletoBingFeng" maxSize="1024"/>
			<Protocol class="fire.pb.instancezone.bingfeng.CCanEnterBingFeng" maxSize="128"/>
			<Protocol class="fire.pb.instancezone.bingfeng.CEnterBingFengLand" maxSize="1024"/>
			<Protocol class="fire.pb.instancezone.bingfeng.CGetBingFengDetail" maxSize="1024"/>
			<Protocol class="fire.pb.instancezone.bingfeng.CLeaveBingFengLand" maxSize="1024"/>
			<Protocol class="fire.pb.instancezone.bingfeng.CReqBingFengRank" maxSize="1024"/>
			<Protocol class="fire.pb.instancezone.bingfeng.SBingFengLandInfo" maxSize="1024"/>
			<Protocol class="fire.pb.instancezone.bingfeng.SCanEnterBingFeng" maxSize="1024"/>
			<Protocol class="fire.pb.instancezone.bingfeng.SEnterBingFengLand" maxSize="1024"/>
			<Protocol class="fire.pb.instancezone.bingfeng.SGetBingFengDetail" maxSize="4096"/>
			<Protocol class="fire.pb.item.CAppendItem" maxSize="128"/>
			<Protocol class="fire.pb.item.CAttachGem" maxSize="128"/>
			<Protocol class="fire.pb.item.CExpChangeItem" maxSize="128"/>
			<Protocol class="fire.pb.item.CBuyPackMoney" maxSize="8"/>
			<Protocol class="fire.pb.item.CCleanTempPack" maxSize="16"/>
			<Protocol class="fire.pb.item.CComposeGem" maxSize="128"/>
			<Protocol class="fire.pb.item.CDelGem" maxSize="10"/>
			<Protocol class="fire.pb.item.CDropItem" maxSize="128"/>
			<Protocol class="fire.pb.item.CExtPackSize" maxSize="16"/>
			<Protocol class="fire.pb.item.CGetDepotInfo" maxSize="128"/>
			<Protocol class="fire.pb.item.CGetEquipTips" maxSize="128"/>
			<Protocol class="fire.pb.item.CGetItemTips" maxSize="128"/>
			<Protocol class="fire.pb.item.CGetMulDayLoginGift" maxSize="16"/>
			<Protocol class="fire.pb.item.CGetPackInfo" maxSize="128"/>
			<Protocol class="fire.pb.item.CGetRoleEquip" maxSize="128"/>
			<Protocol class="fire.pb.item.CGetRoleInfo" maxSize="65535"/>
			<Protocol class="fire.pb.item.CGetRolePetInfo" maxSize="128"/>
			<Protocol class="fire.pb.item.CGetTimeAward" maxSize="16"/>
			<Protocol class="fire.pb.item.CHeChengItem" maxSize="128"/>
			<Protocol class="fire.pb.item.CItemLoseEffect" maxSize="128"/>
			<Protocol class="fire.pb.item.CItemRecover" maxSize="65535"/>
			<Protocol class="fire.pb.item.CItemRecoverList" maxSize="65535"/>
			<Protocol class="fire.pb.item.CListDepot" maxSize="16"/>
			<Protocol class="fire.pb.item.CListPack" maxSize="128"/>
			<Protocol class="fire.pb.item.CMailGetAward" maxSize="65535"/>
			<Protocol class="fire.pb.item.CMailRead" maxSize="65535"/>
			<Protocol class="fire.pb.item.CModifyDepotName" maxSize="1024"/>
			<Protocol class="fire.pb.item.COneKeyMoveTempToBag" maxSize="65535"/>
			<Protocol class="fire.pb.item.COpenItemBag" maxSize="16"/>
			<Protocol class="fire.pb.item.COpenPack" maxSize="16"/>
			<Protocol class="fire.pb.item.COtherItemTips" maxSize="1024"/>
			<Protocol class="fire.pb.item.CPutOnEquip" maxSize="128"/>
			<Protocol class="fire.pb.item.CRecoverItemInfo" maxSize="65535"/>
			<Protocol class="fire.pb.item.CReplaceGem" maxSize="16"/>
			<Protocol class="fire.pb.item.CReplaceGemFromEquip" maxSize="128"/>
			<Protocol class="fire.pb.item.CReqAllNaiJiu" maxSize="16"/>
			<Protocol class="fire.pb.item.CTakeOffEquip" maxSize="128"/>
			<Protocol class="fire.pb.item.CTransItem" maxSize="1024"/>
			<Protocol class="fire.pb.item.CXiuLiEquipItem" maxSize="65535"/>
			<Protocol class="fire.pb.item.CRongLianEquipItem" maxSize="128"/>
			<Protocol class="fire.pb.item.SAddItem" maxSize="65535"/>
			<Protocol class="fire.pb.item.SAllEquipScore" maxSize="16"/>
			<Protocol class="fire.pb.item.SBuyPackMoney" maxSize="16"/>
			<Protocol class="fire.pb.item.SCleanTempPack" maxSize="16"/>
			<Protocol class="fire.pb.item.SDelItem" maxSize="128"/>
			<Protocol class="fire.pb.item.SFreshRepairData" maxSize="16"/>
			<Protocol class="fire.pb.item.SGetDepotInfo" maxSize="65535"/>
			<Protocol class="fire.pb.item.SGetEquipTips" maxSize="65535"/>
			<Protocol class="fire.pb.item.SGetItemTips" maxSize="65535"/>
			<Protocol class="fire.pb.item.SGetPackInfo" maxSize="65535"/>
			<Protocol class="fire.pb.item.SGetRoleEquip" maxSize="65535"/>
			<Protocol class="fire.pb.item.SGetRoleInfo" maxSize="65535"/>
			<Protocol class="fire.pb.item.SGetTimeAward" maxSize="128"/>
			<Protocol class="fire.pb.item.SHeChengItem" maxSize="128"/>
			<Protocol class="fire.pb.item.SHeChengRet" maxSize="16"/>
			<Protocol class="fire.pb.item.SItemAdded" maxSize="256"/>
			<Protocol class="fire.pb.item.SItemNumChange" maxSize="128"/>
			<Protocol class="fire.pb.item.SItemPosChange" maxSize="128"/>
			<Protocol class="fire.pb.item.SItemRecover" maxSize="65535"/>
			<Protocol class="fire.pb.item.SItemRecoverList" maxSize="65535"/>
			<Protocol class="fire.pb.item.SItemSign" maxSize="128"/>
			<Protocol class="fire.pb.item.SMailInfo" maxSize="65535"/>
			<Protocol class="fire.pb.item.SMailList" maxSize="65535"/>
			<Protocol class="fire.pb.item.SMailState" maxSize="65535"/>
			<Protocol class="fire.pb.item.SModifyDepotName" maxSize="128"/>
			<Protocol class="fire.pb.item.SMulDayLogin" maxSize="256"/>
			<Protocol class="fire.pb.item.SNoticeRoleGetInfo" maxSize="65535"/>
			<Protocol class="fire.pb.item.SOpenPack" maxSize="16"/>
			<Protocol class="fire.pb.item.SOtherItemTips" maxSize="1024"/>
			<Protocol class="fire.pb.item.SRecoverItemInfo" maxSize="65535"/>
			<Protocol class="fire.pb.item.SRefreshCurrency" maxSize="65535"/>
			<Protocol class="fire.pb.item.SRefreshItemFlag" maxSize="128"/>
			<Protocol class="fire.pb.item.SRefreshItemTimeout" maxSize="128"/>
			<Protocol class="fire.pb.item.SRefreshMaxNaiJiu" maxSize="128"/>
			<Protocol class="fire.pb.item.SRefreshNaiJiu" maxSize="1024"/>
			<Protocol class="fire.pb.item.SRefreshPackSize" maxSize="128"/>
			<Protocol class="fire.pb.item.SRepairResult" maxSize="16"/>
			<Protocol class="fire.pb.item.SReplaceGem" maxSize="16"/>
			<Protocol class="fire.pb.item.SRideUpdate" maxSize="128"/>
			<Protocol class="fire.pb.item.SUseEnhancementItem" maxSize="16"/>
			<Protocol class="fire.pb.item.SXiuLiFailTimes" maxSize="128"/>
			<Protocol class="fire.pb.item.CAttunement" maxSize="65535"/>
			<Protocol class="fire.pb.item.CChongZhuEquipItem" maxSize="128"/>
			<Protocol class="fire.pb.item.fumo.CEquipFuMoOpen" maxSize="128"/>
			<Protocol class="fire.pb.item.CEquipFuMo" maxSize="1024"/>
			<Protocol class="fire.pb.lock.CReqAddLock" maxSize="65535"/>
			<Protocol class="fire.pb.lock.CReqCancelLock" maxSize="65535"/>
			<Protocol class="fire.pb.lock.CReqChangePassword" maxSize="65535"/>
			<Protocol class="fire.pb.lock.CReqForceUnlock" maxSize="65535"/>
			<Protocol class="fire.pb.lock.CReqLockInfo" maxSize="65535"/>
			<Protocol class="fire.pb.lock.CReqUnlock" maxSize="65535"/>
			<Protocol class="fire.pb.lock.SAddLockSuc" maxSize="65535"/>
			<Protocol class="fire.pb.lock.SCancelLockSuc" maxSize="65535"/>
			<Protocol class="fire.pb.lock.SChangePasswordSuc" maxSize="65535"/>
			<Protocol class="fire.pb.lock.SForceUnlockSuc" maxSize="65535"/>
			<Protocol class="fire.pb.lock.SLockInfo" maxSize="65535"/>
			<Protocol class="fire.pb.lock.SNeedUnlock" maxSize="65535"/>
			<Protocol class="fire.pb.lock.SUnlockSuc" maxSize="65535"/>
			<Protocol class="fire.pb.lock.SUpdateLockInfo" maxSize="65535"/>
			<Protocol class="fire.pb.master.CAppMaster" maxSize="128"/>
			<Protocol class="fire.pb.master.CCanAcceptPrentice" maxSize="65535"/>
			<Protocol class="fire.pb.master.CConfirmRegMaster" maxSize="65535"/>
			<Protocol class="fire.pb.master.CDismissApprentce" maxSize="128"/>
			<Protocol class="fire.pb.master.CDismissMaster" maxSize="128"/>
			<Protocol class="fire.pb.master.CDissolveRelation" maxSize="65535"/>
			<Protocol class="fire.pb.master.CEvaluate" maxSize="128"/>
			<Protocol class="fire.pb.master.CEvaluateMasterResult" maxSize="65535"/>
			<Protocol class="fire.pb.master.CMasterRequestResult" maxSize="65535"/>
			<Protocol class="fire.pb.master.CPrenticeRequestResult" maxSize="65535"/>
			<Protocol class="fire.pb.master.CRegMaster" maxSize="65535"/>
			<Protocol class="fire.pb.master.CReqApprences" maxSize="65535"/>
			<Protocol class="fire.pb.master.CRequestAsApprentice" maxSize="65535"/>
			<Protocol class="fire.pb.master.CRequestAsMaster" maxSize="65535"/>
			<Protocol class="fire.pb.master.CRequestGiveMasterPrenticeTips" maxSize="8"/>
			<Protocol class="fire.pb.master.CRequestMasterPrenticeList" maxSize="8"/>
			<Protocol class="fire.pb.master.CRequestPrenticeOnLineState" maxSize="8"/>
			<Protocol class="fire.pb.master.CSearchMaster" maxSize="65535"/>
			<Protocol class="fire.pb.master.CSearchPrentice" maxSize="65535"/>
			<Protocol class="fire.pb.master.CSelectedMasterAward" maxSize="65535"/>
			<Protocol class="fire.pb.master.CSelectedPrenticePassBook" maxSize="65535"/>
			<Protocol class="fire.pb.master.CSendRequestWordToMaster" maxSize="65535"/>
			<Protocol class="fire.pb.master.CSendRequestWordToPrentice" maxSize="65535"/>
			<Protocol class="fire.pb.master.CTakeAchiveAward" maxSize="128"/>
			<Protocol class="fire.pb.master.SAddPrePrentice" maxSize="8"/>
			<Protocol class="fire.pb.master.SCanAcceptPrentice" maxSize="65535"/>
			<Protocol class="fire.pb.master.SCanRequestAsMaster" maxSize="65535"/>
			<Protocol class="fire.pb.master.SCanRequestAsPrentice" maxSize="65535"/>
			<Protocol class="fire.pb.master.SCantRequestAsMaster" maxSize="65535"/>
			<Protocol class="fire.pb.master.SCantRequestAsPrentice" maxSize="65535"/>
			<Protocol class="fire.pb.master.SDismissApprentces" maxSize="128"/>
			<Protocol class="fire.pb.master.SDissolveRelation" maxSize="65535"/>
			<Protocol class="fire.pb.master.SEvaluate" maxSize="128"/>
			<Protocol class="fire.pb.master.SInitPrenticeList" maxSize="65535"/>
			<Protocol class="fire.pb.master.SMasterPrenticeData" maxSize="65535"/>
			<Protocol class="fire.pb.master.SNotifyAppMaster" maxSize="128"/>
			<Protocol class="fire.pb.master.SNotifyDismissMaster" maxSize="128"/>
			<Protocol class="fire.pb.master.SNotifyMaster" maxSize="128"/>
			<Protocol class="fire.pb.master.SPrenticeList" maxSize="65535"/>
			<Protocol class="fire.pb.master.SPrenticesList" maxSize="65535"/>
			<Protocol class="fire.pb.master.SPreviousMasters" maxSize="65535"/>
			<Protocol class="fire.pb.master.SReadyRegMaster" maxSize="65535"/>
			<Protocol class="fire.pb.master.SReceiveNewPrentice" maxSize="65535"/>
			<Protocol class="fire.pb.master.SRegMaster" maxSize="65535"/>
			<Protocol class="fire.pb.master.SRequestAsApprentice" maxSize="65535"/>
			<Protocol class="fire.pb.master.SRequestAsMaster" maxSize="65535"/>
			<Protocol class="fire.pb.master.SRequestPrenticeSuccess" maxSize="65535"/>
			<Protocol class="fire.pb.master.SSearchMaster" maxSize="65535"/>
			<Protocol class="fire.pb.master.SSearchPrentice" maxSize="65535"/>
			<Protocol class="fire.pb.master.SSendMasterPrenticeList" maxSize="16384"/>
			<Protocol class="fire.pb.master.SSendPrenticeOnLineState" maxSize="128"/>
			<Protocol class="fire.pb.master.STakeAchiveFresh" maxSize="128"/>
			<Protocol class="fire.pb.mission.CAbsentFairyland" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CActiveMissionAIBattle" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CAnsQuestion" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CAnswerQuestion" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CAskIntoInstance" maxSize="128"/>
			<Protocol class="fire.pb.mission.CAskLandTimes" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CCommitMission" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CDefineTeam" maxSize="128"/>
			<Protocol class="fire.pb.mission.CDropInstance" maxSize="128"/>
			<Protocol class="fire.pb.mission.CGetActivityInfo" maxSize="64"/>
			<Protocol class="fire.pb.mission.CGetArchiveAward" maxSize="128"/>
			<Protocol class="fire.pb.mission.CGetArchiveInfo" maxSize="64"/>
			<Protocol class="fire.pb.mission.CGetInstanceState" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CGetLineState" maxSize="64"/>
			<Protocol class="fire.pb.mission.CMissionDialogEnd" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CMissionReachScene" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CNotifyTeamMemeberSubmitItem" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CReqGoto" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CReqJionActivity" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CReqLandTask" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CReqLineTask" maxSize="128"/>
			<Protocol class="fire.pb.mission.CReqMissionCanAccept" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CReturnFairyland" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CTrackMission" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CUseTreasureMap" maxSize="65535"/>
			<Protocol class="fire.pb.mission.CUseTreasureMapEnd" maxSize="64"/>
			<Protocol class="fire.pb.mission.SAcceptMission" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SAddTreasureMap" maxSize="128"/>
			<Protocol class="fire.pb.mission.SAnswerInstance" maxSize="128"/>
			<Protocol class="fire.pb.mission.SAskIntoInstance" maxSize="1024"/>
			<Protocol class="fire.pb.mission.SCopyDestroyTime" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SDailyTaskStateList" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SDefineTeam" maxSize="1024"/>
			<Protocol class="fire.pb.mission.SDropInstance" maxSize="1024"/>
			<Protocol class="fire.pb.mission.SFairylandStatus" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SGetActivityInfo" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SGetArchiveInfo" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SGetInstanceState" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SGetLineState" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SLandTimes" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SNotifyTuiSongList" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SNpcFollowEnd" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SNpcFollowStart" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SPlayXianJingCG" maxSize="1024"/>
			<Protocol class="fire.pb.mission.SQuestion" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SRefreshMissionState" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SRefreshMissionValue" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SRemoveTuiSong" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SReqMissionCanAccept" maxSize="65535"/>
			<Protocol class="fire.pb.mission.STrackMission" maxSize="65535"/>
			<Protocol class="fire.pb.mission.STrackedMissions" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SUseMissionItemFail" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SUseTreasureMap" maxSize="65535"/>
			<Protocol class="fire.pb.mission.SWaitRollTime" maxSize="128"/>
			<Protocol class="fire.pb.mission.activelist.CDrawGiftBox" maxSize="128"/>
			<Protocol class="fire.pb.mission.activelist.CRefreshActivityListFinishTimes" maxSize="65535"/>
			<Protocol class="fire.pb.mission.activelist.CShareActivity" maxSize="128"/>
			<Protocol class="fire.pb.mission.activelist.SActivityOpen" maxSize="128"/>
			<Protocol class="fire.pb.mission.activelist.SDrawGiftBox" maxSize="128"/>
			<Protocol class="fire.pb.mission.activelist.SRefreshActivityListFinishTimes" maxSize="65535"/>
			<Protocol class="fire.pb.move.CCheckMove" maxSize="65535"/>
			<Protocol class="fire.pb.move.CEnterDangerConfirm" maxSize="65535"/>
			<Protocol class="fire.pb.move.CGMGetAroundRoles" maxSize="65535"/>
			<Protocol class="fire.pb.move.CRelocateRolePos" maxSize="65535"/>
			<Protocol class="fire.pb.move.CReqSeeEachOther" maxSize="65535"/>
			<Protocol class="fire.pb.move.CRoleJump" maxSize="65535"/>
			<Protocol class="fire.pb.move.CRoleJumpStop" maxSize="65535"/>
			<Protocol class="fire.pb.move.CRoleMove" maxSize="65535"/>
			<Protocol class="fire.pb.move.CRoleStop" maxSize="65535"/>
			<Protocol class="fire.pb.move.CRoleTurn" maxSize="65535"/>
			<Protocol class="fire.pb.move.CSendAutoMovePathID" maxSize="65535"/>
			<Protocol class="fire.pb.move.SAddActivityNpc" maxSize="65535"/>
			<Protocol class="fire.pb.move.SAddPickupScreen" maxSize="65535"/>
			<Protocol class="fire.pb.move.SAddUserScreen" maxSize="65535"/>
			<Protocol class="fire.pb.move.SBeginBaitang" maxSize="65535"/>
			<Protocol class="fire.pb.move.SChangeEquipEffect" maxSize="64"/>
			<Protocol class="fire.pb.move.SGMGetAroundRoles" maxSize="65536"/>
			<Protocol class="fire.pb.move.SHideRole" maxSize="65535"/>
			<Protocol class="fire.pb.move.SNPCMoveTo" maxSize="65535"/>
			<Protocol class="fire.pb.move.SRelocateRolePos" maxSize="65535"/>
			<Protocol class="fire.pb.move.SRemoveActivityNpc" maxSize="65535"/>
			<Protocol class="fire.pb.move.SRemovePickupScreen" maxSize="65535"/>
			<Protocol class="fire.pb.move.SRemoveUserScreen" maxSize="65535"/>
			<Protocol class="fire.pb.move.SRoleChangeShape" maxSize="65535"/>
			<Protocol class="fire.pb.move.SRoleComponentsChange" maxSize="65535"/>
			<Protocol class="fire.pb.move.SRoleEnterScene" maxSize="65535"/>
			<Protocol class="fire.pb.move.SRoleJump" maxSize="65535"/>
			<Protocol class="fire.pb.move.SRoleJumpDrawback" maxSize="65535"/>
			<Protocol class="fire.pb.move.SRoleModelChange" maxSize="64"/>
			<Protocol class="fire.pb.move.SRoleMove" maxSize="65535"/>
			<Protocol class="fire.pb.move.SRolePlayAction" maxSize="128"/>
			<Protocol class="fire.pb.move.SRoleStop" maxSize="65535"/>
			<Protocol class="fire.pb.move.SRoleTurn" maxSize="65535"/>
			<Protocol class="fire.pb.move.SSetRoleLocation" maxSize="65535"/>
			<Protocol class="fire.pb.move.SSetRoleTeamInfo" maxSize="65535"/>
			<Protocol class="fire.pb.move.STransfromShape" maxSize="65535"/>
			<Protocol class="fire.pb.move.SUpdateNpcSceneState" maxSize="64"/>
			<Protocol class="fire.pb.move.SUpdateRoleSceneState" maxSize="65536"/>
			<Protocol class="fire.pb.npc.CAbandonMacth" maxSize="256"/>
			<Protocol class="fire.pb.npc.CActivityAnswerQuestionHelp" maxSize="1024"/>
			<Protocol class="fire.pb.npc.CAnsQuestion" maxSize="65535"/>
			<Protocol class="fire.pb.npc.CAnswerActivityQuestion" maxSize="1024"/>
			<Protocol class="fire.pb.npc.CAnswerQuestion" maxSize="1024"/>
			<Protocol class="fire.pb.npc.CApplyImpExam" maxSize="65535"/>
			<Protocol class="fire.pb.npc.CConfirmImpExam" maxSize="65535"/>
			<Protocol class="fire.pb.npc.CDoneFortuneWheel" maxSize="65535"/>
			<Protocol class="fire.pb.npc.CExitCopy" maxSize="65535"/>
			<Protocol class="fire.pb.npc.CFinishFortuneWheel" maxSize="65535"/>
			<Protocol class="fire.pb.npc.CGeneralSummonCommand" maxSize="65525"/>
			<Protocol class="fire.pb.npc.CGiveUpQuestion" maxSize="1024"/>
			<Protocol class="fire.pb.npc.CGrabActivityReward" maxSize="1024"/>
			<Protocol class="fire.pb.npc.CHasFortuneWheel" maxSize="65535"/>
			<Protocol class="fire.pb.npc.CImpExamHelp" maxSize="1024"/>
			<Protocol class="fire.pb.npc.CNpcService" maxSize="65535"/>
			<Protocol class="fire.pb.npc.COpenChest" maxSize="1024"/>
			<Protocol class="fire.pb.npc.CQueryImpExamState" maxSize="1024"/>
			<Protocol class="fire.pb.npc.CReqQuestion" maxSize="65535"/>
			<Protocol class="fire.pb.npc.CRespondInviteToMarry" maxSize="256"/>
			<Protocol class="fire.pb.npc.CRequestActivityAnswerQuestion" maxSize="1024"/>
			<Protocol class="fire.pb.npc.CSendImpExamAnswer" maxSize="65535"/>
			<Protocol class="fire.pb.npc.CStartFortuneWheel" maxSize="65535"/>
			<Protocol class="fire.pb.npc.CSubmit2Npc" maxSize="65535"/>
			<Protocol class="fire.pb.npc.CVisitNpc" maxSize="65535"/>
			<Protocol class="fire.pb.npc.CWinnerChangeTask" maxSize="65535"/>
			<Protocol class="fire.pb.npc.SActivityAnswerQuestionHelp" maxSize="1024"/>
			<Protocol class="fire.pb.npc.SAskQuestion" maxSize="1024"/>
			<Protocol class="fire.pb.npc.SAttendImpExam" maxSize="65535"/>
			<Protocol class="fire.pb.npc.SBattleToNpcError" maxSize="65535"/>
			<Protocol class="fire.pb.npc.SGeneralSummonCommand" maxSize="65525"/>
			<Protocol class="fire.pb.npc.SGrabActivityReward" maxSize="1024"/>
			<Protocol class="fire.pb.npc.SImpExamHelp" maxSize="65535"/>
			<Protocol class="fire.pb.npc.SInvitationToMarry" maxSize="256"/>
			<Protocol class="fire.pb.npc.SMacthResult" maxSize="256"/>
			<Protocol class="fire.pb.npc.SNpcBattleTime" maxSize="256"/>
			<Protocol class="fire.pb.npc.SPingJi" maxSize="65535"/>
			<Protocol class="fire.pb.npc.SQueryImpExamState" maxSize="1024"/>
			<Protocol class="fire.pb.npc.SReqFortuneWheel" maxSize="65535"/>
			<Protocol class="fire.pb.npc.SSendImpExamAssist" maxSize="65535"/>
			<Protocol class="fire.pb.npc.SSendImpExamProv" maxSize="65535"/>
			<Protocol class="fire.pb.npc.SSendImpExamStart" maxSize="65535"/>
			<Protocol class="fire.pb.npc.SSendImpExamState" maxSize="65535"/>
			<Protocol class="fire.pb.npc.SSendImpExamVill" maxSize="65535"/>
			<Protocol class="fire.pb.npc.SSendNpcMsg" maxSize="65535"/>
			<Protocol class="fire.pb.npc.SSendNpcService" maxSize="65535"/>
			<Protocol class="fire.pb.npc.SSubmit2Npc" maxSize="65535"/>
			<Protocol class="fire.pb.npc.SVisitNpc" maxSize="65535"/>
			<Protocol class="fire.pb.npc.SVisitNpcContainChatMsg" maxSize="65535"/>
			<Protocol class="fire.pb.npc.SWinnerChangeTask" maxSize="65535"/>
			<Protocol class="fire.pb.pet.Cxiechuneidan" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CFreePet1" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CGetPetInfo" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CGetPetcolumnInfo" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CModPetName" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CMovePetColumn" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetAddPoint" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetAptitudeCultivate" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetAptitudeCultivatee" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetDepotColumnAddCapacity" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetExpCultivate" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetLearnSkillByBook" maxSize="65535"/>
            <Protocol class="fire.pb.pet.CPetLearnInternalByBook" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetRecover" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetRecoverList" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetResetPoint" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetSell" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetSetAutoAddPoint" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetSkillCertification" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetSkillCertificationLingWu" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetSkillCertificationYiWang" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetSynthesize" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetWash" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CRecoverPetInfo" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CSetFightPet" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CSetFightPetRest" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CShowPet" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CShowPetInfo" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CShowPetOff" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SAddPetToColumn" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SGetPetInfo" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SGetPetcolumnInfo" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SModPetName" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SPetAptitudeCultivate" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SPetAptitudeCultivatee" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SPetError" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SPetGossip" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SPetRecover" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SPetRecoverList" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SPetSetAutoAddPoint" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SPetSkillCertification" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SPetSynthesize" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SPetWash" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SRecoverPetInfo" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SRefreshPetColumnCapacity" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SRefreshPetExp" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SRefreshPetInfo" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SRefreshPetScore" maxSize="256"/>
			<Protocol class="fire.pb.pet.SRefreshPetSkill" maxSize="65535"/>
          	<Protocol class="fire.pb.pet.SRefreshPetInternal" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SRemovePetFromCol" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SSetFightPet" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SSetFightPetRest" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SShowPetAround" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SShowPetInfo" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CPetEquipbyPet" maxSize="65535"/>
			<Protocol class="fire.pb.pet.CGetPetEquipInfo" maxSize="65535"/>
          	<Protocol class="fire.pb.pet.CGetPetEquipList" maxSize="65535"/>
			<Protocol class="fire.pb.pet.SSetPetEquipInfo" maxSize="65535"/>
          	<Protocol class="fire.pb.pet.SSetPetEquipList" maxSize="65535"/>
          	<Protocol class="fire.pb.pet.CPetHuanHua" maxSize="65535"/>
			<Protocol class="fire.pb.pet.shenshou.CShenShouChongZhi" maxSize="65535"/>
			<Protocol class="fire.pb.pet.shenshou.CShenShouDuiHuan" maxSize="65535"/>
			<Protocol class="fire.pb.pet.shenshou.CShenShouYangCheng" maxSize="65535"/>
			<Protocol class="fire.pb.pingbi.CAddBlackRole" maxSize="65535"/>
			<Protocol class="fire.pb.pingbi.CRemoveBlackRole" maxSize="65535"/>
			<Protocol class="fire.pb.pingbi.CReqBlackRoles" maxSize="65535"/>
			<Protocol class="fire.pb.pingbi.CSearchBlackRole" maxSize="65535"/>
			<Protocol class="fire.pb.pingbi.SBlackRoles" maxSize="65535"/>
			<Protocol class="fire.pb.pingbi.SSearchBlackRoleInfo" maxSize="65535"/>
			<Protocol class="fire.pb.product.CMakeEquip" maxSize="65535"/>
			<Protocol class="fire.pb.product.CResolveEquip" maxSize="64"/>
			<Protocol class="fire.pb.product.CResolveGem" maxSize="64"/>
			<Protocol class="fire.pb.product.SErrorCode" maxSize="65535"/>
			<Protocol class="fire.pb.product.SProductMadeUp" maxSize="65535"/>
			<Protocol class="fire.pb.ranklist.CRequestRankList" maxSize="65535"/>
			<Protocol class="fire.pb.ranklist.CRequestRankPetData" maxSize="65535"/>
			<Protocol class="fire.pb.ranklist.SRequestRankList" maxSize="65535"/>
			<Protocol class="fire.pb.ranklist.SSendRankPetData" maxSize="65535"/>
			<Protocol class="fire.pb.ranklist.getrankinfo.CRankGetInfo" maxSize="32"/>
			<Protocol class="fire.pb.ranklist.getrankinfo.CRankGetPetInfo" maxSize="32"/>
			<Protocol class="fire.pb.ranklist.getrankinfo.SFactionRankInfo" maxSize="1024"/>
			<Protocol class="fire.pb.ranklist.getrankinfo.SRankRoleInfo" maxSize="512"/>
			<Protocol class="fire.pb.ranklist.getrankinfo.SRankRoleInfo2" maxSize="65535"/>
			<Protocol class="fire.pb.school.CSendElectorWords" maxSize="65535"/>
			<Protocol class="fire.pb.school.CShouxiShape" maxSize="65535"/>
			<Protocol class="fire.pb.school.CVoteCandidate" maxSize="65535"/>
			<Protocol class="fire.pb.school.SCanElect" maxSize="65535"/>
			<Protocol class="fire.pb.school.SMyElector" maxSize="65535"/>
			<Protocol class="fire.pb.school.SSendCandidates" maxSize="65535"/>
			<Protocol class="fire.pb.school.SSendShouxiInfo" maxSize="65535"/>
			<Protocol class="fire.pb.school.SShouxiShape" maxSize="65535"/>
			<Protocol class="fire.pb.school.SVoteCandidate" maxSize="65535"/>
			<Protocol class="fire.pb.school.change.CChangeGem" maxSize="65535"/>
			<Protocol class="fire.pb.school.change.CChangeSchool" maxSize="65535"/>
			<Protocol class="fire.pb.school.change.CChangeSchoolExtInfo" maxSize="65535"/>
			<Protocol class="fire.pb.school.change.CChangeWeapon" maxSize="65535"/>
			<Protocol class="fire.pb.school.change.CChangeWeapon2" maxSize="65535"/>
			<Protocol class="fire.pb.school.change.COldSchoolList" maxSize="65535"/>
			<Protocol class="fire.pb.school.change.SChangeGem" maxSize="65535"/>
			<Protocol class="fire.pb.school.change.SChangeSchoolExtInfo" maxSize="65535"/>
			<Protocol class="fire.pb.school.change.SChangeWeapon" maxSize="65535"/>
			<Protocol class="fire.pb.school.change.SChangeWeapon2" maxSize="65535"/>
			<Protocol class="fire.pb.school.change.SOldSchoolList" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CAttentionGoods" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CBuyMallShop" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CBuyNpcShop" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CBaiTanError" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CChamberOfCommerceShop" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CExchangeCurrency" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CExchangeShop" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CGetMarketUpPrice" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CGoldOrderBrowseBlackMarket" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CGoldOrderDownBlackMarket" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CGoldOrderTakeOutBlackMarket" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CGoldOrderUpBlackMarket" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CMarketAttentionBrowse" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CMarketBrowse" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CMarketBuy" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CMarketCleanTradeLog" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CMarketContainerBrowse" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CMarketDown" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CMarketItemChatShow" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CMarketPetTips" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CMarketSearchEquip" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CMarketSearchPet" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CMarketTradeLog" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CMarketUp" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CQueryLimit" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CReMarketUp" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CRequstShopPrice" maxSize="65535"/>
			<Protocol class="fire.pb.shop.CTakeBackTempMarketContainerItem" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SAttentionGoods" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SGetMarketUpPrice" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SGoldOrderBrowseBlackMarket" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SGoldOrderDownBlackMarket" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SGoldOrderUpBlackMarket" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SMarketBrowse" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SMarketBuy" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SMarketContainerBrowse" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SMarketItemChatShow" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SMarketPetTips" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SMarketSearchResult" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SMarketTradeLog" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SMarketUpSucc" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SNotifyBuySuccess" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SQueryLimit" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SRefreshGoldOrderState" maxSize="65535"/>
			<Protocol class="fire.pb.shop.SResponseShopPrice" maxSize="65535"/>
			<Protocol class="fire.pb.shop.STakeBackTempMarketContainerItem" maxSize="65535"/>
			<Protocol class="fire.pb.shop.STempMarketContainer" maxSize="65535"/>
			<Protocol class="fire.pb.skill.CPractiseSkill" maxSize="65535"/>
			<Protocol class="fire.pb.skill.CUpdaetAssistSkillLevel" maxSize="65535"/>
			<Protocol class="fire.pb.skill.CUpdateInborn" maxSize="65535"/>
			<Protocol class="fire.pb.skill.CUseSceneSkill" maxSize="65535"/>
			<Protocol class="fire.pb.skill.SSendAssistSkillMaxLevels" maxSize="65535"/>
			<Protocol class="fire.pb.skill.SSendInborns" maxSize="65535"/>
			<Protocol class="fire.pb.skill.SSendSpecialSkills" maxSize="65535"/>
			<Protocol class="fire.pb.skill.SSkillError" maxSize="65535"/>
			<Protocol class="fire.pb.skill.SUpdateAssistSkill" maxSize="65535"/>
			<Protocol class="fire.pb.skill.SUpdateExtSkill" maxSize="65535"/>
			<Protocol class="fire.pb.skill.SUpdateInborn" maxSize="65535"/>
			<Protocol class="fire.pb.skill.liveskill.CLiveSkillMakeDrug" maxSize="65536"/>
			<Protocol class="fire.pb.skill.liveskill.CLiveSkillMakeEnhancement" maxSize="65536"/>
			<Protocol class="fire.pb.skill.liveskill.CLiveSkillMakeFarm" maxSize="65536"/>
			<Protocol class="fire.pb.skill.liveskill.CLiveSkillMakeFood" maxSize="65536"/>
			<Protocol class="fire.pb.skill.liveskill.CLiveSkillMakeFriendGift" maxSize="65536"/>
			<Protocol class="fire.pb.skill.liveskill.CLiveSkillMakeStuff" maxSize="65536"/>
			<Protocol class="fire.pb.skill.liveskill.CRequestAttr" maxSize="65536"/>
			<Protocol class="fire.pb.skill.liveskill.CRequestLearnLiveSkill" maxSize="65536"/>
			<Protocol class="fire.pb.skill.liveskill.CRequestLiveSkillList" maxSize="65535"/>
			<Protocol class="fire.pb.skill.liveskill.SLiveSkillMakeDrug" maxSize="65536"/>
			<Protocol class="fire.pb.skill.liveskill.SLiveSkillMakeEnhancement" maxSize="65536"/>
			<Protocol class="fire.pb.skill.liveskill.SLiveSkillMakeFarm" maxSize="65536"/>
			<Protocol class="fire.pb.skill.liveskill.SLiveSkillMakeFood" maxSize="65536"/>
			<Protocol class="fire.pb.skill.liveskill.SLiveSkillMakeFriendGift" maxSize="65536"/>
			<Protocol class="fire.pb.skill.liveskill.SLiveSkillMakeStuff" maxSize="65536"/>
			<Protocol class="fire.pb.skill.liveskill.SRequestLiveSkillList" maxSize="65536"/>
			<Protocol class="fire.pb.skill.liveskill.SUpdateLearnLiveSkill" maxSize="65536"/>
			<Protocol class="fire.pb.skill.particleskill.CRequestLearnParticleSkill" maxSize="65536"/>
			<Protocol class="fire.pb.skill.particleskill.CRequestParticleSkillList" maxSize="65535"/>
			<Protocol class="fire.pb.skill.particleskill.SRequestParticleSkillList" maxSize="65536"/>
			<Protocol class="fire.pb.skill.particleskill.SUpdateLearnParticleSkill" maxSize="65536"/>
			<Protocol class="fire.pb.talk.CChatItemTips" maxSize="65535"/>
			<Protocol class="fire.pb.talk.CTransChatMessage2Serv" maxSize="65535"/>
			<Protocol class="fire.pb.talk.SChatHelpResult" maxSize="65535"/>
			<Protocol class="fire.pb.talk.SChatItemTips" maxSize="65535"/>
			<Protocol class="fire.pb.talk.SExpMessageTips" maxSize="65535"/>
			<Protocol class="fire.pb.talk.STransChatMessage2Client" maxSize="65535"/>
			<Protocol class="fire.pb.talk.STransChatMessageNotify2Client" maxSize="65535"/>
			<Protocol class="fire.pb.team.CAbsentReturnTeam" maxSize="65535"/>
			<Protocol class="fire.pb.team.CAcceptToTeam" maxSize="65535"/>
			<Protocol class="fire.pb.team.CAnswerforCallBack" maxSize="65535"/>
			<Protocol class="fire.pb.team.CAnswerforSetLeader" maxSize="65535"/>
			<Protocol class="fire.pb.team.CCallbackMember" maxSize="65535"/>
			<Protocol class="fire.pb.team.CCreateTeam" maxSize="65535"/>
			<Protocol class="fire.pb.team.CDismissTeam" maxSize="65535"/>
			<Protocol class="fire.pb.team.CExpelMember" maxSize="65535"/>
			<Protocol class="fire.pb.team.CFInviteJoinTeam" maxSize="256"/>
			<Protocol class="fire.pb.team.CFormationMakeBook" maxSize="2048"/>
			<Protocol class="fire.pb.team.CInviteJoinTeam" maxSize="65535"/>
			<Protocol class="fire.pb.team.COneKeyApplyTeamInfo" maxSize="65535"/>
			<Protocol class="fire.pb.team.COneKeyTeamMatch" maxSize="65535"/>
			<Protocol class="fire.pb.team.CQuitTeam" maxSize="65535"/>
			<Protocol class="fire.pb.team.CRequestClanFightRoleList" maxSize="65535"/>
			<Protocol class="fire.pb.team.CRequestClanFightTeamList" maxSize="65535"/>
			<Protocol class="fire.pb.team.CRequestClanFightTeamRoleNum" maxSize="65535"/>
			<Protocol class="fire.pb.team.CRequestHaveTeam" maxSize="65536"/>
			<Protocol class="fire.pb.team.CRequestJoinTeam" maxSize="65535"/>
			<Protocol class="fire.pb.team.CRequestMatchInfo" maxSize="65535"/>
			<Protocol class="fire.pb.team.CRequestSetFormation" maxSize="65535"/>
			<Protocol class="fire.pb.team.CRequestSetTeamLevel" maxSize="65535"/>
			<Protocol class="fire.pb.team.CRequestSetTeamMatchInfo" maxSize="65536"/>
			<Protocol class="fire.pb.team.CRequestStopTeamMatch" maxSize="65535"/>
			<Protocol class="fire.pb.team.CRequestTeamMatch" maxSize="65535"/>
			<Protocol class="fire.pb.team.CRequestTeamMatchList" maxSize="65535"/>
			<Protocol class="fire.pb.team.CRespondInvite" maxSize="65535"/>
			<Protocol class="fire.pb.team.CSetTeamLeader" maxSize="65535"/>
			<Protocol class="fire.pb.team.CSwapMember" maxSize="65535"/>
			<Protocol class="fire.pb.team.CUseFormBook" maxSize="2048"/>
			<Protocol class="fire.pb.team.SAbsentReturnTeam" maxSize="65535"/>
			<Protocol class="fire.pb.team.SAddTeamApply" maxSize="655350"/>
			<Protocol class="fire.pb.team.SAddTeamMember" maxSize="655350"/>
			<Protocol class="fire.pb.team.SAskforCallBack" maxSize="65535"/>
			<Protocol class="fire.pb.team.SAskforSetLeader" maxSize="65535"/>
			<Protocol class="fire.pb.team.SCreateTeam" maxSize="65535"/>
			<Protocol class="fire.pb.team.SDismissTeam" maxSize="65535"/>
			<Protocol class="fire.pb.team.SForceInviteJointTeam" maxSize="65535"/>
			<Protocol class="fire.pb.team.SFormationsMap" maxSize="2048"/>
			<Protocol class="fire.pb.team.SInviteJoinSucc" maxSize="65535"/>
			<Protocol class="fire.pb.team.SInviteJoinTeam" maxSize="65535"/>
			<Protocol class="fire.pb.team.SMemberSequence" maxSize="65535"/>
			<Protocol class="fire.pb.team.SOneKeyApplyTeamInfo" maxSize="65535"/>
			<Protocol class="fire.pb.team.SOneKeyTeamMatch" maxSize="65535"/>
			<Protocol class="fire.pb.team.SRemoveTeamApply" maxSize="65535"/>
			<Protocol class="fire.pb.team.SRemoveTeamMember" maxSize="65535"/>
			<Protocol class="fire.pb.team.SRequestClanFightRoleList" maxSize="65535"/>
			<Protocol class="fire.pb.team.SRequestClanFightTeamList" maxSize="65535"/>
			<Protocol class="fire.pb.team.SRequestClanFightTeamRoleNum" maxSize="65535"/>
			<Protocol class="fire.pb.team.SRequestHaveTeam" maxSize="65536"/>
			<Protocol class="fire.pb.team.SRequestJoinSucc" maxSize="65535"/>
			<Protocol class="fire.pb.team.SRequestMatchInfo" maxSize="65536"/>
			<Protocol class="fire.pb.team.SRequestSetLeaderSucc" maxSize="65535"/>
			<Protocol class="fire.pb.team.SRequestTeamMatch" maxSize="65535"/>
			<Protocol class="fire.pb.team.SRequestTeamMatchList" maxSize="65535"/>
			<Protocol class="fire.pb.team.SRespondInvite" maxSize="65535"/>
			<Protocol class="fire.pb.team.SSendSingleCharacterList" maxSize="65535"/>
			<Protocol class="fire.pb.team.SSetMyFormation" maxSize="65535"/>
			<Protocol class="fire.pb.team.SSetTeamFormation" maxSize="65535"/>
			<Protocol class="fire.pb.team.SSetTeamLeader" maxSize="65535"/>
			<Protocol class="fire.pb.team.SSetTeamLevel" maxSize="65535"/>
			<Protocol class="fire.pb.team.SSetTeamState" maxSize="65535"/>
			<Protocol class="fire.pb.team.SStopTeamMatch" maxSize="65535"/>
			<Protocol class="fire.pb.team.SSwapMember" maxSize="65535"/>
			<Protocol class="fire.pb.team.STeamError" maxSize="65535"/>
			<Protocol class="fire.pb.team.SUpdateMemberHPMP" maxSize="65535"/>
			<Protocol class="fire.pb.team.SUpdateMemberLevel" maxSize="65535"/>
			<Protocol class="fire.pb.team.SUpdateMemberMaxHPMP" maxSize="65535"/>
			<Protocol class="fire.pb.team.SUpdateMemberPosition" maxSize="65535"/>
			<Protocol class="fire.pb.team.SUpdateMemberState" maxSize="65535"/>
			<Protocol class="fire.pb.team.SUpdateTeamMemberBasic" maxSize="65535"/>
			<Protocol class="fire.pb.team.SUpdateTeamMemberComponent" maxSize="65535"/>
			<Protocol class="fire.pb.team.teammelon.CRequestRollItemTips" maxSize="65536"/>
			<Protocol class="fire.pb.team.teammelon.CTeamRollMelon" maxSize="65535"/>
			<Protocol class="fire.pb.team.teammelon.SOneTeamRollMelonInfo" maxSize="65536"/>
			<Protocol class="fire.pb.team.teammelon.STeamRollMelon" maxSize="65535"/>
			<Protocol class="fire.pb.team.teammelon.STeamRollMelonInfo" maxSize="65536"/>
			<Protocol class="fire.pb.qiannengguo.COpenPotentialFruit" maxSize="65536"/>
			<Protocol class="fire.pb.qiannengguo.CResetPotentialFruit" maxSize="65536"/>
			<Protocol class="fire.pb.qiannengguo.CResetPotentialFruitExtra" maxSize="65536"/>
			<Protocol class="fire.pb.qiannengguo.CReturnPotentialFruit" maxSize="65536"/>
			<Protocol class="fire.pb.qiannengguo.SyncPotentialFruit" maxSize="65536"/>
			<Protocol class="fire.pb.qiannengguo.CQueryPotentialFruit" maxSize="65536"/>
			<Protocol class="fire.pb.title.COffTitle" maxSize="65535"/>
			<Protocol class="fire.pb.title.COnTitle" maxSize="65535"/>
			<Protocol class="fire.pb.title.SAddTitle" maxSize="65535"/>
			<Protocol class="fire.pb.title.SOffTitle" maxSize="65535"/>
			<Protocol class="fire.pb.title.SOnTitle" maxSize="65535"/>
			<Protocol class="fire.pb.title.SOnTitle1" maxSize="65535"/>
			<Protocol class="fire.pb.title.SRemoveTitle" maxSize="65535"/>
			<Protocol class="fire.pb.title.STitleErr" maxSize="65535"/>
			<Protocol class="fire.pb.triggers.CNpcToTrigger" maxSize="65535"/>
			<Protocol class="gnet.link.AnnounceLinkId" maxSize="32"/>
			<Protocol class="gnet.link.Bind" maxSize="1024"/>
			<Protocol class="gnet.link.Broadcast" maxSize="65536"/>
			<Protocol class="gnet.link.Dispatch" maxSize="65536"/>
			<Protocol class="gnet.link.Kick" maxSize="32"/>
			<Protocol class="gnet.link.LinkBroken" maxSize="32"/>
			<Protocol class="gnet.link.LinkServerControl" maxSize="32"/>
			<Protocol class="gnet.link.Send" maxSize="1048576"/>
			<Protocol class="gnet.link.SetLogin" maxSize="128"/>
			<Protocol class="gnet.link.UnBind" maxSize="1024"/>
		</Coder>
	</Manager>
	<!-- GameDBServer -->
	<Manager class="gnet.DeliveryManager" name="GdeliveryClient">
		<Connector inputBufferSize="131072" outputBufferSize="2097152" receiveBufferSize="16384" remoteIp="127.0.0.1" remotePort="45001" keepOutputBuffer="true" sendBufferSize="16384" tcpNoDelay="false"/>
		<Coder comment="DO NOT EDIT CODER!">
			<Protocol class="cross.BroadcastData" maxSize="1048576"/>
			<Protocol class="cross.CommonDataTransfer" maxSize="1048576"/>
			<Protocol class="cross.SendUserInfoAndTicket" maxSize="2048"/>
			<Protocol class="cross.SendUserInfoAndTicket_Re" maxSize="32"/>
			<Protocol class="gnet.AU2Game" maxSize="1048576"/>
			<Protocol class="gnet.AddCash" maxSize="256"/>
			<Protocol class="gnet.AddCash_Re" maxSize="256"/>
			<Protocol class="gnet.DataBetweenAuAnyAndClient" maxSize="1048576"/>
			<Protocol class="gnet.DataBetweenAuAnyAndGS" maxSize="104857"/>
			<Protocol class="gnet.DiscountAnnounce" maxSize="512"/>
			<Protocol class="gnet.GMKickoutUser" maxSize="256"/>
			<Protocol class="gnet.GMKickoutUser_Re" maxSize="64"/>
			<Protocol class="gnet.GMShutup" maxSize="256"/>
			<Protocol class="gnet.GMShutup_Re" maxSize="64"/>
			<Protocol class="gnet.Game2AU" maxSize="1048576"/>
			<Protocol class="gnet.GetAddCashSN2Rep" maxSize="256"/>
			<Protocol class="gnet.GetAddCashSN2Req" maxSize="256"/>
			<Protocol class="gnet.GetAddCashSNRep" maxSize="256"/>
			<Protocol class="gnet.GetAddCashSNReq" maxSize="256"/>
			<Rpc class="gnet.GetMaxOnlineNum" maxSize="64"/>
			<Protocol class="gnet.PassportGetRoleListRep" maxSize="512"/>
			<Protocol class="gnet.PassportGetRoleListReq" maxSize="32"/>
			<Protocol class="gnet.QueryOrderRequest" maxSize="1048576"/>
			<Protocol class="gnet.QueryOrderResponse" maxSize="1048576"/>
			<Protocol class="gnet.QueryUserPrivilege3" maxSize="32"/>
			<Protocol class="gnet.QueryUserPrivilege3_Re" maxSize="4096"/>
			<Protocol class="gnet.QueryUserid2Rep" maxSize="256"/>
			<Protocol class="gnet.QueryUserid2Req" maxSize="256"/>
			<Protocol class="gnet.ServerIDResponse" maxSize="512"/>
			<Rpc class="gnet.SetMaxOnlineNum" maxSize="32"/>
			<Rpc class="gnet.SetServerAttr" maxSize="64"/>
			<Protocol class="gnet.SysSendMail2" maxSize="4096"/>
			<Protocol class="gnet.SysSendMail2_Re" maxSize="32"/>
			<Protocol class="gnet.UserInfoRep" maxSize="1024"/>
			<Protocol class="gnet.UserInfoReq" maxSize="32"/>
			<Protocol class="gnet.VerifyMaster2" maxSize="128"/>
			<Protocol class="gnet.VerifyMaster2_Re" maxSize="128"/>
		</Coder>
	</Manager>
</MkioConf>
