[LinkServer]
type            = tcp
port            = 42001
address         = 0.0.0.0
listen_backlog  = 10

# buffer
so_sndbuf       = 16384
so_rcvbuf       = 16384
ibuffermax      = 65536
obuffermax      = 65536
accumulate      = 131072	 

# check session state timeout(default not check,state's timeout defined in gnet.link.xml)
session_state_check	= 1
# check KeepAlive(default not check),link will close client(LinkBroken's reason=7) when not receive client's KeepAlive in keepalive_interval(unit is second)
keepalive_open		= 1
keepalive_interval 	= 30

# exceed errcode=2 && errcode=3, ip be forbidden time,unit is second
forbid_ip_interval 	= 10

# checkunknownprotocol type and size
checkunknownprotocol	= 0

#gs control link open the listen port,1:link open port by itself
listen_port_open	= 1

#open protocol statistic,default not open
stat_open		= 0

#login and logout log level,mengzhu is 15,yitian is 11,default is 15
log_level		= 6
;log_level		= 15

# user number control
max_users		=	10000
halflogin_users 	=	3000

# misc
tcp_nodelay      = 0
close_discard    = 1
urgency_support  = 1
;so_broadcast     = 1

version	  = 16777217

[ProviderServer]
type            = tcp
port            = 43001
address         = 0.0.0.0
listen_backlog  = 10

# zero is reserved
linkid          = 5019

# buffer
so_sndbuf       = 16384
so_rcvbuf       = 16384
ibuffermax      = 1638400
obuffermax      = 1638400
accumulate      = 131072


[DeliveryClient]
type         	  = tcp
port 		  = 44001 
address           = 0.0.0.0

# buffer
so_sndbuf       = 16384
so_rcvbuf       = 16384
ibuffermax      = 1638400
obuffermax      = 1638400
accumulate      = 131072

# crypto
;isec             = 2
;iseckey          = 123
;osec             = 2
;oseckey          = 456

[SpeedLimit]
window		  =  10
high		  =  3

[LogClientManager]
type         	  = udp
port 		  = 11100 
address           = 0.0.0.0

# buffer
so_sndbuf       = 65536
so_rcvbuf       = 65536
ibuffermax      = 65536
obuffermax      = 1048576
accumulate      = 1048576

[LogClientTcpManager]
type         	  = tcp
port 		  = 11101 
address           = 0.0.0.0

# buffer
so_sndbuf       = 65536
so_rcvbuf       = 65536
ibuffermax      = 65536
obuffermax      = 1048576
accumulate      = 1048576
