# log4cpp.properties

log4cpp.rootCategory=DEBUG, rootAppender
log4cpp.category.glinkd=DEBUG, glinkdAppender

log4cpp.appender.rootAppender=ConsoleAppender
log4cpp.appender.rootAppender.layout=PatternLayout
log4cpp.appender.rootAppender.layout.ConversionPattern=%d{%m-%d %H:%M:%S}[%p]-%m%n

#######################################################################################
#log4cpp.appender.glinkdAppender=DailyRollingFileAppender
#log4cpp.appender.glinkdAppender.DatePattern='.'yyyy-MM-dd
#log4cpp.appender.glinkdAppender.fileName=logs/gate.log
#log4cpp.appender.glinkdAppender.layout=PatternLayout
#log4cpp.appender.glinkdAppender.layout.ConversionPattern=%d{%m-%d %H:%M:%S}[%p]-%m%n

#######################################################################################
log4cpp.appender.glinkdAppender=RollingFileAppender
log4cpp.appender.glinkdAppender.threshold=DEBUG
log4cpp.appender.glinkdAppender.fileName=logs/gate.log
log4cpp.appender.glinkdAppender.maxFileSize=1024
log4cpp.appender.glinkdAppender.maxBackupIndex=10
log4cpp.appender.glinkdAppender.layout=PatternLayout
log4cpp.appender.glinkdAppender.layout.ConversionPattern=%d{%m-%d %H:%M:%S}[%p]-%m%n
