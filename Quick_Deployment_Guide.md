# GSXDB游戏服务器快速部署指南

## 🚀 快速开始

### 前置要求
- **操作系统**: CentOS 7.6+ / Ubuntu 18.04+
- **Java环境**: JDK 8+ (推荐OpenJDK 11)
- **数据库**: MySQL 5.7+ 或 H2 (开发环境)
- **内存**: 最低2GB，推荐4GB+
- **磁盘**: 最低10GB可用空间

### 1分钟快速启动

```bash
# 1. 进入部署目录
cd centos7.6_game_server

# 2. 设置执行权限
chmod +x bin/qd

# 3. 启动公共服务
./bin/qd 0

# 4. 启动大区1
./bin/qd 1

# 5. 检查服务状态
./bin/qd dk
```

## 📋 详细部署步骤

### 步骤1: 环境准备

```bash
# 安装Java (CentOS)
sudo yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel

# 安装Java (Ubuntu)
sudo apt update && sudo apt install -y openjdk-8-jdk

# 验证Java安装
java -version
```

### 步骤2: 数据库配置

#### MySQL配置 (生产环境推荐)
```bash
# 安装MySQL
sudo yum install -y mysql-server mysql

# 启动MySQL服务
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 创建游戏数据库
mysql -u root -p
CREATE DATABASE gamedb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'gameuser'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON gamedb.* TO 'gameuser'@'localhost';
FLUSH PRIVILEGES;
```

#### H2配置 (开发环境)
```bash
# H2数据库无需额外配置，自动创建
# 数据文件位置: ./data/gamedb.mv.db
```

### 步骤3: 配置文件调整

```bash
# 编辑数据库配置
vi config/common/database.properties

# 修改数据库连接信息
db.mysql.url=*************************************************************************
db.mysql.username=gameuser
db.mysql.password=your_secure_password
```

### 步骤4: 服务启动

```bash
# 进入管理目录
cd centos7.6_game_server

# 启动服务 (按顺序)
./bin/qd 0    # 启动公共服务 (名称服务器、SDK服务器)
./bin/qd 1    # 启动大区1 (游戏服务器、网关、代理)

# 查看启动状态
./bin/qd dk   # 查看端口占用情况
./bin/qd monitor  # 系统监控
```

### 步骤5: 验证部署

```bash
# 检查进程状态
ps aux | grep java

# 检查端口监听
netstat -tlnp | grep -E "(14001|42001|43001|44001)"

# 查看日志
tail -f game/server1/game_server/logs/xgen.log
```

## 🔧 服务管理命令

### 基础命令
```bash
./bin/qd          # 显示菜单
./bin/qd help     # 显示帮助
./bin/qd dk       # 查看端口状态
./bin/qd monitor  # 系统监控
```

### 服务启动
```bash
./bin/qd 0        # 启动公共服务
./bin/qd 1        # 启动大区1
./bin/qd 2        # 启动大区2
# ... 支持1-99大区
```

### 服务关闭
```bash
./bin/qd 00       # 关闭公共服务
./bin/qd 01       # 关闭大区1
./bin/qd 02       # 关闭大区2
./bin/qd gbA      # 关闭所有大区
```

### 机器人管理
```bash
./bin/qd robot    # 机器人管理菜单
# 1. 启动机器人
# 2. 关闭机器人  
# 3. 查看机器人状态
```

## 🌐 端口配置

### 默认端口分配
```
大区N的端口配置:
- 游戏服务器: 14000 + N
- 网关服务器: 42000 + N  
- 代理服务器: 43000 + N
- 传输服务器: 44000 + N
- GM管理端口: 41000 + N

公共服务端口:
- 名称服务器: 15000
- SDK服务器: 16000
```

### 防火墙配置
```bash
# CentOS 7 防火墙配置
sudo firewall-cmd --permanent --add-port=14001-14099/tcp
sudo firewall-cmd --permanent --add-port=41001-41099/tcp
sudo firewall-cmd --permanent --add-port=42001-42099/tcp
sudo firewall-cmd --permanent --add-port=43001-43099/tcp
sudo firewall-cmd --permanent --add-port=44001-44099/tcp
sudo firewall-cmd --permanent --add-port=15000/tcp
sudo firewall-cmd --permanent --add-port=16000/tcp
sudo firewall-cmd --reload

# Ubuntu 防火墙配置
sudo ufw allow 14001:14099/tcp
sudo ufw allow 41001:41099/tcp
sudo ufw allow 42001:42099/tcp
sudo ufw allow 43001:43099/tcp
sudo ufw allow 44001:44099/tcp
sudo ufw allow 15000/tcp
sudo ufw allow 16000/tcp
```

## 📊 性能调优

### JVM参数优化
```bash
# 编辑启动脚本
vi game/server1/game_server/start.sh

# 根据服务器内存调整JVM参数
# 2GB内存服务器: -Xms512m -Xmx1024m
# 4GB内存服务器: -Xms1024m -Xmx2048m  
# 8GB内存服务器: -Xms1536m -Xmx3072m
```

### 数据库优化
```sql
-- MySQL配置优化
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
sync_binlog = 0
max_connections = 1000
```

## 🔍 故障排查

### 常见问题

#### 1. 服务启动失败
```bash
# 检查Java环境
java -version

# 检查端口占用
netstat -tlnp | grep 14001

# 查看错误日志
tail -f game/server1/game_server/logs/xgen.log
```

#### 2. 数据库连接失败
```bash
# 测试数据库连接
mysql -h localhost -u gameuser -p gamedb

# 检查数据库配置
cat config/common/database.properties
```

#### 3. 内存不足
```bash
# 检查系统内存
free -h

# 调整JVM参数
vi game/server1/game_server/start.sh
```

### 日志位置
```
系统日志: /home/<USER>/script.log
游戏日志: game/server1/game_server/logs/xgen.log
GC日志: game/server1/game_server/gc.log
网关日志: game/server1/gate_server/logs/
代理日志: game/server1/proxy_server/logs/
```

## 🚀 生产环境部署

### 高可用配置
```bash
# 1. 多实例部署
./bin/qd 1    # 大区1-主服务器
./bin/qd 2    # 大区2-备用服务器

# 2. 负载均衡配置
# 使用Nginx进行负载均衡

# 3. 数据库主从配置
# 配置MySQL主从复制
```

### 监控集成
```bash
# 集成Prometheus监控
# 配置Grafana仪表板
# 设置告警规则
```

## 📞 技术支持

### 获取帮助
- **文档**: 查看 `docs/` 目录下的详细文档
- **日志**: 检查相关日志文件
- **社区**: 参考企业级文档和最佳实践

### 联系方式
- **技术支持**: 查看项目README
- **问题反馈**: 提交Issue到项目仓库
- **紧急支持**: 联系项目维护团队

---

🎉 **恭喜！您已成功部署GSXDB游戏服务器！**

现在可以开始您的游戏服务器之旅了！
