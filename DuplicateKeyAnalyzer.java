import java.io.*;
import java.util.*;
import java.util.regex.*;

/**
 * Analyzes duplicate Map keys in JsFunManager.java file
 */
public class DuplicateKeyAnalyzer {

    private static class KeyValuePair {
        String key;
        int value;
        int lineNumber;

        KeyValuePair(String key, int value, int lineNumber) {
            this.key = key;
            this.value = value;
            this.lineNumber = lineNumber;
        }

        @Override
        public String toString() {
            return String.format("Line %d: \"%s\" -> %d", lineNumber, key, value);
        }
    }

    public static void main(String[] args) {
        String filePath = "src/fire/script/JsFunManager.java";
        analyzeDuplicateKeys(filePath);
    }

    public static void analyzeDuplicateKeys(String filePath) {
        System.out.println("Analyzing duplicate Map keys in JsFunManager.java");
        System.out.println("============================================================");
        
        Map<String, List<KeyValuePair>> keyMap = new HashMap<>();
        Pattern pattern = Pattern.compile("funMap\\.put\\(\"([^\"]+)\",\\s*(\\d+)\\);");
        
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            int lineNumber = 0;
            int totalEntries = 0;
            
            while ((line = reader.readLine()) != null) {
                lineNumber++;
                Matcher matcher = pattern.matcher(line);
                
                if (matcher.find()) {
                    String key = matcher.group(1);
                    int value = Integer.parseInt(matcher.group(2));
                    totalEntries++;
                    
                    KeyValuePair pair = new KeyValuePair(key, value, lineNumber);
                    keyMap.computeIfAbsent(key, k -> new ArrayList<>()).add(pair);
                }
            }
            
            System.out.println("Statistics:");
            System.out.println("Total entries: " + totalEntries);
            System.out.println("Unique keys: " + keyMap.size());
            System.out.println("Duplicate keys: " + keyMap.entrySet().stream()
                .mapToInt(entry -> entry.getValue().size() > 1 ? 1 : 0).sum());
            System.out.println();

            // Analyze duplicates
            analyzeDuplicates(keyMap);

            // Analyze value conflicts
            analyzeValueConflicts(keyMap);

            // Generate fix suggestions
            generateFixSuggestions(keyMap);

        } catch (IOException e) {
            System.err.println("Error reading file: " + e.getMessage());
        }
    }
    
    private static void analyzeDuplicates(Map<String, List<KeyValuePair>> keyMap) {
        System.out.println("Duplicate Key Analysis:");
        System.out.println("----------------------------------------");

        List<Map.Entry<String, List<KeyValuePair>>> duplicates = keyMap.entrySet().stream()
            .filter(entry -> entry.getValue().size() > 1)
            .sorted((a, b) -> Integer.compare(b.getValue().size(), a.getValue().size()))
            .collect(java.util.stream.Collectors.toList());

        if (duplicates.isEmpty()) {
            System.out.println("No duplicate keys found");
            return;
        }

        System.out.println("Found " + duplicates.size() + " duplicate keys:");
        System.out.println();

        for (Map.Entry<String, List<KeyValuePair>> entry : duplicates) {
            String key = entry.getKey();
            List<KeyValuePair> pairs = entry.getValue();

            System.out.println("Duplicate key: \"" + key + "\" (appears " + pairs.size() + " times)");
            for (KeyValuePair pair : pairs) {
                System.out.println("   " + pair);
            }
            System.out.println();
        }
    }
    
    private static void analyzeValueConflicts(Map<String, List<KeyValuePair>> keyMap) {
        System.out.println("Value Conflict Analysis:");
        System.out.println("----------------------------------------");

        List<Map.Entry<String, List<KeyValuePair>>> conflicts = keyMap.entrySet().stream()
            .filter(entry -> entry.getValue().size() > 1)
            .filter(entry -> {
                Set<Integer> values = new HashSet<>();
                for (KeyValuePair pair : entry.getValue()) {
                    values.add(pair.value);
                }
                return values.size() > 1; // Different values exist
            })
            .collect(java.util.stream.Collectors.toList());

        if (conflicts.isEmpty()) {
            System.out.println("No value conflicts found - all duplicate keys have same values");
            return;
        }

        System.out.println("Found " + conflicts.size() + " value conflicts:");
        System.out.println();

        for (Map.Entry<String, List<KeyValuePair>> entry : conflicts) {
            String key = entry.getKey();
            List<KeyValuePair> pairs = entry.getValue();

            System.out.println("CONFLICT - Key: \"" + key + "\"");
            for (KeyValuePair pair : pairs) {
                System.out.println("   " + pair);
            }
            System.out.println();
        }
    }
    
    private static void generateFixSuggestions(Map<String, List<KeyValuePair>> keyMap) {
        System.out.println("Fix Suggestions:");
        System.out.println("----------------------------------------");

        List<Map.Entry<String, List<KeyValuePair>>> duplicates = keyMap.entrySet().stream()
            .filter(entry -> entry.getValue().size() > 1)
            .collect(java.util.stream.Collectors.toList());

        if (duplicates.isEmpty()) {
            System.out.println("No fixes needed");
            return;
        }

        System.out.println("Suggested fix actions:");
        System.out.println();

        for (Map.Entry<String, List<KeyValuePair>> entry : duplicates) {
            String key = entry.getKey();
            List<KeyValuePair> pairs = entry.getValue();

            // Check if values are the same
            Set<Integer> values = new HashSet<>();
            for (KeyValuePair pair : pairs) {
                values.add(pair.value);
            }

            if (values.size() == 1) {
                // Same values, just remove duplicates
                System.out.println("Key: \"" + key + "\"");
                System.out.println("   Status: Duplicate with same value (ID: " + values.iterator().next() + ")");
                System.out.println("   Action: Remove duplicates, keep first one (line " + pairs.get(0).lineNumber + ")");
                System.out.println("   Remove: " + pairs.subList(1, pairs.size()).stream()
                    .map(p -> "line " + p.lineNumber).reduce((a, b) -> a + ", " + b).orElse(""));
            } else {
                // Different values, needs manual review
                System.out.println("WARNING - Key: \"" + key + "\"");
                System.out.println("   Status: Value conflict");
                System.out.println("   Action: Manual review needed to determine correct value");
                for (KeyValuePair pair : pairs) {
                    System.out.println("   Option: " + pair);
                }
            }
            System.out.println();
        }
    }
}
