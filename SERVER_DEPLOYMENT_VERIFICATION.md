# 服务器部署验证指南

## 📋 概览

本指南提供了在Linux服务器环境中验证QD脚本启动逻辑的完整流程。

## 🎯 验证目标

- ✅ 验证QD脚本语法正确性
- ✅ 验证启动逻辑功能完整性
- ✅ 验证服务器环境兼容性
- ✅ 验证游戏服务器正常启动

## 📁 需要上传的文件

### 核心脚本文件
```
centos7.6_game_server/
├── bin/
│   ├── qd                    # 原始QD脚本
│   └── qd_fixed             # 修复版QD脚本 (推荐使用)
├── game/
│   ├── server1/
│   │   ├── qd.sh            # Server1启动脚本 (已修复)
│   │   └── game_server/
│   │       └── start.sh     # 游戏服务器启动脚本 (已优化)
│   └── common/              # 公共服务文件
└── scripts/                 # 本地管理脚本 (可选)
```

## 🚀 服务器验证步骤

### 第一步：上传文件到服务器

```bash
# 1. 上传整个centos7.6_game_server目录到服务器
scp -r centos7.6_game_server/ user@server:/home/<USER>/

# 2. 或者使用rsync同步
rsync -avz centos7.6_game_server/ user@server:/home/<USER>/
```

### 第二步：设置文件权限

```bash
# 登录服务器
ssh user@server

# 切换到游戏目录
cd /home/<USER>

# 设置安全权限
find . -type d -exec chmod 755 {} \;
find . -type f -exec chmod 644 {} \;

# 设置脚本可执行权限
find . -name "*.sh" -exec chmod 755 {} \;
chmod 755 bin/qd bin/qd_fixed
chmod 755 game/server1/game_server/gateserver 2>/dev/null || true
chmod 755 game/server1/game_server/proxyserver 2>/dev/null || true
```

### 第三步：验证脚本语法

```bash
# 验证原始QD脚本
echo "=== 验证原始QD脚本 ==="
bash -n bin/qd
if [ $? -eq 0 ]; then
    echo "✅ 原始QD脚本语法正确"
else
    echo "❌ 原始QD脚本语法错误"
fi

# 验证修复版QD脚本
echo "=== 验证修复版QD脚本 ==="
bash -n bin/qd_fixed
if [ $? -eq 0 ]; then
    echo "✅ 修复版QD脚本语法正确"
else
    echo "❌ 修复版QD脚本语法错误"
fi

# 验证Server1脚本
echo "=== 验证Server1脚本 ==="
bash -n game/server1/qd.sh
if [ $? -eq 0 ]; then
    echo "✅ Server1脚本语法正确"
else
    echo "❌ Server1脚本语法错误"
fi

# 验证游戏启动脚本
echo "=== 验证游戏启动脚本 ==="
bash -n game/server1/game_server/start.sh
if [ $? -eq 0 ]; then
    echo "✅ 游戏启动脚本语法正确"
else
    echo "❌ 游戏启动脚本语法错误"
fi
```

### 第四步：验证帮助功能

```bash
# 测试修复版QD脚本帮助
echo "=== 测试QD脚本帮助功能 ==="
./bin/qd_fixed help

# 测试Server1脚本帮助
echo "=== 测试Server1脚本帮助功能 ==="
cd game/server1
./qd.sh help
cd ../..

# 测试游戏启动脚本帮助
echo "=== 测试游戏启动脚本帮助功能 ==="
cd game/server1/game_server
./start.sh help
cd ../../..
```

### 第五步：验证状态检查功能

```bash
# 检查修复版QD脚本状态功能
echo "=== 测试QD脚本状态检查 ==="
./bin/qd_fixed status

# 检查Server1脚本状态功能
echo "=== 测试Server1脚本状态检查 ==="
cd game/server1
./qd.sh status
cd ../..

# 检查游戏启动脚本状态功能
echo "=== 测试游戏启动脚本状态检查 ==="
cd game/server1/game_server
./start.sh status
cd ../../..
```

### 第六步：验证环境依赖

```bash
# 检查Java环境
echo "=== 检查Java环境 ==="
java -version
if [ $? -eq 0 ]; then
    echo "✅ Java环境正常"
else
    echo "❌ Java环境异常"
fi

# 检查必需工具
echo "=== 检查系统工具 ==="
for tool in ps kill netstat lsof; do
    if command -v $tool &> /dev/null; then
        echo "✅ $tool 可用"
    else
        echo "❌ $tool 不可用"
    fi
done

# 检查端口可用性
echo "=== 检查端口可用性 ==="
for port in 22200 29200 41001 42001 43001; do
    if netstat -ln | grep ":$port " &> /dev/null; then
        echo "⚠️  端口 $port 被占用"
    else
        echo "✅ 端口 $port 可用"
    fi
done
```

## 🎮 实际启动测试

### 安全测试（推荐）

```bash
# 1. 启动公共服务
echo "=== 启动公共服务 ==="
./bin/qd_fixed 0

# 等待3秒
sleep 3

# 2. 检查公共服务状态
echo "=== 检查公共服务状态 ==="
./bin/qd_fixed status

# 3. 启动大区1
echo "=== 启动大区1 ==="
./bin/qd_fixed 1

# 等待10秒
sleep 10

# 4. 检查所有服务状态
echo "=== 检查所有服务状态 ==="
./bin/qd_fixed status

# 5. 停止所有服务
echo "=== 停止所有服务 ==="
./bin/qd_fixed stop-all
```

### 完整测试

```bash
# 一键启动所有服务
echo "=== 一键启动所有服务 ==="
./bin/qd_fixed start-all

# 等待30秒让服务完全启动
sleep 30

# 检查服务状态
echo "=== 检查服务状态 ==="
./bin/qd_fixed status

# 检查端口监听
echo "=== 检查端口监听 ==="
netstat -tlnp | grep -E ":(22200|29200|41001|42001|43001)"

# 检查进程
echo "=== 检查游戏进程 ==="
ps aux | grep -E "(java|gateserver|proxyserver)" | grep -v grep

# 停止所有服务
echo "=== 停止所有服务 ==="
./bin/qd_fixed stop-all
```

## 📊 验证检查清单

### 语法验证
- [ ] 原始QD脚本语法检查通过
- [ ] 修复版QD脚本语法检查通过
- [ ] Server1脚本语法检查通过
- [ ] 游戏启动脚本语法检查通过

### 功能验证
- [ ] QD脚本帮助功能正常
- [ ] QD脚本状态检查功能正常
- [ ] Server1脚本帮助功能正常
- [ ] Server1脚本状态检查功能正常
- [ ] 游戏启动脚本帮助功能正常
- [ ] 游戏启动脚本状态检查功能正常

### 环境验证
- [ ] Java环境正常
- [ ] 系统工具完整
- [ ] 游戏端口可用
- [ ] 文件权限正确

### 启动验证
- [ ] 公共服务启动成功
- [ ] 大区1启动成功
- [ ] 所有端口正常监听
- [ ] 游戏进程正常运行
- [ ] 服务停止功能正常

## 🔧 常见问题解决

### 权限问题
```bash
# 如果遇到权限问题
chmod -R 755 /home/<USER>
chown -R game:game /home/<USER>
```

### 端口占用问题
```bash
# 查看端口占用
netstat -tlnp | grep :端口号

# 终止占用进程
kill -9 PID
```

### Java内存问题
```bash
# 检查系统内存
free -m

# 如果内存不足，修改JVM参数
# 编辑 game/server1/game_server/start.sh
# 调整 -Xmx 参数
```

### 数据库锁文件问题
```bash
# 清理数据库锁文件
find . -name "mkdb.inuse" -delete
```

## 📝 验证报告模板

```
=== GSXDB服务器部署验证报告 ===

验证时间: $(date)
服务器信息: $(uname -a)
Java版本: $(java -version 2>&1 | head -1)

语法验证:
[ ] 原始QD脚本: 通过/失败
[ ] 修复版QD脚本: 通过/失败
[ ] Server1脚本: 通过/失败
[ ] 游戏启动脚本: 通过/失败

功能验证:
[ ] 帮助功能: 通过/失败
[ ] 状态检查: 通过/失败

启动验证:
[ ] 公共服务: 通过/失败
[ ] 大区1: 通过/失败
[ ] 端口监听: 通过/失败
[ ] 进程运行: 通过/失败

总体结果: 通过/失败

备注:
(记录遇到的问题和解决方案)
```

## 🎯 验证成功标准

### 必须通过的验证
1. ✅ 所有脚本语法检查通过
2. ✅ 帮助和状态功能正常
3. ✅ 公共服务启动成功
4. ✅ 大区1启动成功
5. ✅ 所有游戏端口正常监听

### 可选验证
1. 🔄 多大区启动测试
2. 🔄 服务重启测试
3. 🔄 异常恢复测试
4. 🔄 性能压力测试

---

**🎮 完成以上验证后，QD脚本即可投入生产环境使用！**
