{"simulation_info": {"timestamp": "2025-07-26T23:41:47.173954", "environment": "CentOS 7.6 Simulation", "project_root": "E:\\MT3_Projects\\gsxdb_mt3_mly", "simulation_root": "E:\\MT3_Projects\\gsxdb_mt3_mly\\centos_simulation"}, "setup_result": {"timestamp": "2025-07-26T23:41:17.021048", "environment_setup": {}, "java_environment": {"java_available": true, "java_version": "java version \"1.8.0_144\"", "java_home": "C:\\Program Files\\Java\\jdk1.8.0_144", "classpath_support": true}, "dependencies_check": {"jar_files": {"gsxdb.jar": {"exists": true, "size": 16649448, "path": "E:\\MT3_Projects\\gsxdb_mt3_mly\\gsxdb.jar"}}, "lib_directories": {"lib": {"exists": true, "jar_count": 36}, "gs_lib": {"exists": true, "jar_count": 23}, "lib2": {"exists": true, "jar_count": 2}, "libsys": {"exists": true, "jar_count": 1}}, "essential_files": {"AbstractJSEngine.class": {"exists": true, "size": 4649}, "FightJSEngine.class": {"exists": true, "size": 5394}, "SceneJSEngine.class": {"exists": true, "size": 2763}, "EffectRole.class": {"exists": true, "size": 390}}, "all_dependencies_ready": true}, "simulation_ready": true}, "simulation_result": {"timestamp": "2025-07-26T23:41:17.139570", "startup_attempts": [{"attempt_time": "2025-07-26T23:41:17.139570", "command": ["java", "-Xms1024m", "-Xmx2048m", "-XX:+UseG1GC", "-Dfile.encoding=UTF-8", "-Duser.timezone=Asia/Shanghai", "-Xdebug", "-Xrunjdwp:transport=dt_socket,address=42999,server=y,suspend=n", "-cp", "gsxdb.jar;lib/*;gs_lib/*;lib2/*;libsys/*", "fire.pb.main.Gs"], "working_directory": "E:\\MT3_Projects\\gsxdb_mt3_mly\\centos_simulation\\home\\game\\server1\\game_server", "success": false, "error_output": "", "runtime_seconds": 30.03, "stdout": "Listening for transport dt_socket at address: 42999\n23:41:17.421 [main] ERROR SYSTEM - 杞藉叆E:\\MT3_Projects\\gsxdb_mt3_mly\\centos_simulation\\home\\game\\server1\\game_server\\gamedata\\xml\\auto\\ModuleInfo.xml澶辫触\ncom.thoughtworks.xstream.converters.reflection.AbstractReflectionConverter$UnknownFieldException: No such field fire.pb.main.ModuleInfo.enabled\n---- Debugging information ----\nfield               : enabled\nclass               : fire.pb.main.ModuleInfo\nrequired-type       : fire.pb.main.ModuleInfo\nconverter-type      : com.thoughtworks.xstream.converters.reflection.ReflectionConverter\npath                : /tree-map/entry/fire.pb.main.ModuleInfo/enabled\nline number         : 8\nclass[1]            : java.util.TreeMap\nconverter-type[1]   : com.thoughtworks.xstream.converters.collections.TreeMapConverter\nversion             : null\n-------------------------------\n\tat com.thoughtworks.xstream.converters.reflection.AbstractReflectionConverter.determineType(AbstractReflectionConverter.java:453) ~[xstream-1.4.3.jar:?]\n\tat com.thoughtworks.xstream.converters.reflection.AbstractReflectionConverter.doUnmarshal(AbstractReflectionConverter.java:294) ~[xstream-1.4.3.jar:?]\n\tat com.thoughtworks.xstream.converters.reflection.AbstractReflectionConverter.unmarshal(AbstractReflectionConverter.java:234) ~[xstream-1.4.3.jar:?]\n\tat com.thoughtworks.xstream.core.TreeUnmarshaller.convert(TreeUnmarshaller.java:72) ~[xstream-1.4.3.jar:?]\n\tat com.thoughtworks.xstream.core.AbstractReferenceUnmarshaller.convert(AbstractReferenceUnmarshaller.java:65) ~[xstream-1.4.3.jar:?]\n\tat com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:66) ~[xstream-1.4.3.jar:?]\n\tat com.thoughtworks.xstream.core.TreeUnmarshaller.convertAnother(TreeUnmarshaller.java:50) ~[xstream-1.4.3.jar:?]\n\tat com.thoughtworks.xstream.converters.collections.AbstractCollectionConverter.readItem(AbstractCollectionConverter.java:71) ~[xstream-1.4.3.jar:?]\n\tat com.thoughtworks.xstream.converters.collections.", "stderr": "ERROR StatusLogger No log4j2 configuration file found. Using default configuration: logging only errors to the console.\n", "return_code": 1}], "error_analysis": {"verify_errors": [], "nosuchmethod_errors": [], "classnotfound_errors": [], "other_errors": [], "error_summary": {"verify_error_count": 0, "nosuchmethod_error_count": 0, "classnotfound_error_count": 0, "other_error_count": 0, "total_errors": 0}}, "log_analysis": {"gs_log_exists": true, "gs_log_size": 50476, "recent_errors": [], "startup_success": false}, "simulation_success": false}, "conclusions": {"environment_ready": true, "java_environment_ok": true, "dependencies_ready": true, "simulation_successful": false, "major_issues": [], "recommendations": ["环境配置良好，可以正常运行"]}}