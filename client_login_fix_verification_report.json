{"timestamp": "2025-07-26T19:29:34.423626", "verification_steps": ["class文件验证", "方法签名检查", "日志分析"], "class_files_status": {"IJavaScriptEngine.class": {"exists": true, "size": 562, "modified": "2025-07-26T19:28:04.246284", "recently_modified": true}, "AbstractJSEngine.class": {"exists": true, "size": 4251, "modified": "2025-07-26T19:28:04.285037", "recently_modified": true}, "SceneJSEngine.class": {"exists": true, "size": 2556, "modified": "2025-07-26T19:28:04.294805", "recently_modified": true}, "FightJSEngine.class": {"exists": true, "size": 6778, "modified": "2025-07-26T19:17:51.750896", "recently_modified": true}}, "method_signature_check": {"IJavaScriptEngine": {"success": true, "has_correct_put_signature": false, "output_sample": "Compiled from \"IJavaScriptEngine.java\"\npublic interface fire.script.IJavaScriptEngine {\n  public abstract java.lang.Object eval(java.lang.String) throws javax.script.ScriptException;\n    descriptor: ("}, "SceneJSEngine": {"success": true, "has_correct_put_signature": false, "output_sample": "Compiled from \"SceneJSEngine.java\"\npublic class fire.script.SceneJSEngine extends fire.script.AbstractJSEngine {\n  public fire.script.SceneJSEngine();\n    descriptor: ()V\n\n  public void setSkillLevel("}}, "log_analysis": {"total_recent_lines": 100, "scenejs_put_errors": 4, "nosuchmethod_errors": 4, "login_related_errors": 10, "scenejs_error_details": [{"line_number": 7002, "content": "java.lang.NoSuchMethodError: fire.script.SceneJSEngine.put(Ljava/lang/String;Ljava/lang/Object;)V"}, {"line_number": 7021, "content": "2025-07-26 19:23:46,732 [EnterWorldThread] ERROR SYSTEM.process(61) - roleid=4097 登入出错。java.util.concurrent.ExecutionException: java.lang.NoSuchMethodError: fire.script.SceneJSEngine.put(Ljava/lang/String;Ljava/lang/Object;)V"}, {"line_number": 7060, "content": "java.lang.NoSuchMethodError: fire.script.SceneJSEngine.put(Ljava/lang/String;Ljava/lang/Object;)V"}], "recent_nosuchmethod_errors": [{"line_number": 7021, "content": "2025-07-26 19:23:46,732 [EnterWorldThread] ERROR SYSTEM.process(61) - roleid=4097 登入出错。java.util.concurrent.ExecutionException: java.lang.NoSuchMethodError: fire.script.SceneJSEngine.put(Ljava/lang/String;Ljava/lang/Object;)V"}, {"line_number": 7060, "content": "java.lang.NoSuchMethodError: fire.script.SceneJSEngine.put(Ljava/lang/String;Ljava/lang/Object;)V"}, {"line_number": 7079, "content": "2025-07-26 19:23:48,178 [EnterWorldThread] ERROR SYSTEM.process(61) - roleid=4097 登入出错。java.util.concurrent.ExecutionException: java.lang.NoSuchMethodError: fire.script.SceneJSEngine.put(Ljava/lang/String;Ljava/lang/Object;)V"}], "recent_login_errors": [{"line_number": 7057, "content": "2025-07-26 19:23:48,175 [mkdb.Worker.procedure.31] INFO  SYSTEM.process(25) - roleId=4097 角色开始登录：帐号userId = 1class fire.pb.state.PRoleOnline"}, {"line_number": 7080, "content": "2025-07-26 19:23:48,178 [EnterWorldThread] ERROR SYSTEM.kick(402) - roleId=4097 kick role error=2049"}, {"line_number": 7087, "content": "2025-07-26 19:23:48.180 DEBUG <mkdb.Worker..27> mkio.send type=65543 class=gnet.link.SetLogin this=(30,0,-1,),size=16"}]}, "fix_success": false, "fix_summary": {"verification_date": "2025-07-26T19:29:35.131903", "total_verification_steps": 3, "class_files_verified": 4, "method_signatures_checked": 2, "log_lines_analyzed": 100}}