# QD Script Robot Logic Test - PowerShell Version
# Test if QD script robot logic is correct with original file names

Write-Host "========================================" -ForegroundColor Green
Write-Host "QD Script Robot Logic Test" -ForegroundColor Green
Write-Host "Verify Original File Name Logic" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Test Configuration
$QD_SCRIPT = ".\qd"
$TEST_ZONE_ID = 1
$ROBOT_DIR = "..\game\server1\game_server\robot"

$TotalChecks = 0
$PassedChecks = 0

# Check Function
function Test-Item {
    param($ItemName, $CheckCondition)
    
    $script:TotalChecks++
    Write-Host "Check: $ItemName ... " -NoNewline
    
    if ($CheckCondition) {
        Write-Host "PASS" -ForegroundColor Green
        $script:PassedChecks++
        return $true
    } else {
        Write-Host "FAIL" -ForegroundColor Red
        return $false
    }
}

# Step 1: Basic File Check
Write-Host ""
Write-Host "Step 1: Basic File Check" -ForegroundColor Yellow
Test-Item "QD script exists" (Test-Path $QD_SCRIPT)
Test-Item "QD script readable" (Test-Path $QD_SCRIPT -PathType Leaf)

# Step 2: Robot Directory Check
Write-Host ""
Write-Host "Step 2: Robot Directory Check" -ForegroundColor Yellow
Test-Item "Robot directory exists" (Test-Path $ROBOT_DIR -PathType Container)

if (Test-Path $ROBOT_DIR -PathType Container) {
    # Step 3: Original File Check
    Write-Host ""
    Write-Host "Step 3: Original File Check" -ForegroundColor Yellow
    Test-Item "robot.jar exists" (Test-Path "$ROBOT_DIR\robot.jar")
    Test-Item "start_robot.sh exists" (Test-Path "$ROBOT_DIR\start_robot.sh")
    Test-Item "robot.xio.xml exists" (Test-Path "$ROBOT_DIR\robot.xio.xml")
    Test-Item "log4j.properties exists" (Test-Path "$ROBOT_DIR\log4j.properties")
    Test-Item "robot.conf exists" (Test-Path "$ROBOT_DIR\robot.conf")
    
    # Step 4: Dependency Library Check
    Write-Host ""
    Write-Host "Step 4: Dependency Library Check" -ForegroundColor Yellow
    Test-Item "lib directory exists" (Test-Path "$ROBOT_DIR\lib" -PathType Container)
    
    if (Test-Path "$ROBOT_DIR\lib" -PathType Container) {
        $jarCount = (Get-ChildItem "$ROBOT_DIR\lib\*.jar" -ErrorAction SilentlyContinue).Count
        Test-Item "JAR files sufficient" ($jarCount -ge 5)
        Write-Host "  Found $jarCount JAR files"
        
        # Check critical libraries
        $criticalLibs = @("httpasyncclient", "httpclient", "httpcore", "log4j", "jio")
        foreach ($lib in $criticalLibs) {
            $libExists = (Get-ChildItem "$ROBOT_DIR\lib\*$lib*.jar" -ErrorAction SilentlyContinue).Count -gt 0
            if ($libExists) {
                Write-Host "  ✓ $lib library exists" -ForegroundColor Green
            } else {
                Write-Host "  ⚠ $lib library missing" -ForegroundColor Yellow
            }
        }
    }
}

# Step 5: QD Script Logic Check
Write-Host ""
Write-Host "Step 5: QD Script Logic Check" -ForegroundColor Yellow

if (Test-Path $QD_SCRIPT) {
    try {
        $content = Get-Content $QD_SCRIPT -Raw -ErrorAction SilentlyContinue
        
        # Check if using original file names
        Test-Item "Uses start_robot.sh" ($content -match "start_robot\.sh")
        Test-Item "Uses robot.conf" ($content -match "robot\.conf")
        Test-Item "Checks robot.jar" ($content -match "robot\.jar")
        
        # Check robot functions
        Test-Item "Robot environment check function" ($content -match "check_robot_environment")
        Test-Item "Start robot function" ($content -match "start_robot_test")
        Test-Item "Stop robot function" ($content -match "stop_robot_test")
        Test-Item "Robot status check" ($content -match "check_robot_status")
        
        # Check menu items
        Test-Item "robot menu item" ($content -match "robot.*启动机器人测试")
        Test-Item "robotstatus menu item" ($content -match "robotstatus.*查看机器人状态")
        Test-Item "robotbatch menu item" ($content -match "robotbatch.*批量机器人管理")
        Test-Item "robothealth menu item" ($content -match "robothealth.*机器人健康检查")
        
    } catch {
        Write-Host "ERROR: Cannot read QD script content" -ForegroundColor Red
    }
}

# Step 6: Simulate Startup Test
Write-Host ""
Write-Host "Step 6: Simulate Startup Test" -ForegroundColor Yellow

Write-Host "Simulate execution: $QD_SCRIPT robot" -ForegroundColor Cyan
Write-Host "This will test QD script robot startup logic..."

Write-Host ""
Write-Host "Startup Logic Verification:"

# Simulate zone check
Write-Host "1. Check zone $TEST_ZONE_ID ... " -NoNewline
if ((Test-Path "..\game\server$TEST_ZONE_ID" -PathType Container) -or (Test-Path "\home\game\server$TEST_ZONE_ID" -PathType Container)) {
    Write-Host "ZONE EXISTS" -ForegroundColor Green
} else {
    Write-Host "ZONE NOT EXISTS (simulation environment)" -ForegroundColor Yellow
}

# Simulate robot environment check
Write-Host "2. Check robot environment ... " -NoNewline
if ((Test-Path $ROBOT_DIR -PathType Container) -and (Test-Path "$ROBOT_DIR\robot.jar") -and (Test-Path "$ROBOT_DIR\start_robot.sh")) {
    Write-Host "ENVIRONMENT COMPLETE" -ForegroundColor Green
} else {
    Write-Host "ENVIRONMENT INCOMPLETE" -ForegroundColor Red
}

# Simulate game server check
Write-Host "3. Check game server port ... " -NoNewline
$gamePort = $TEST_ZONE_ID + 41000
Write-Host "PORT $gamePort (requires game server running)" -ForegroundColor Yellow

# Simulate config file creation
Write-Host "4. Create temporary config ... " -NoNewline
if ($content -match "robot\.conf") {
    Write-Host "USES robot.conf CONFIG" -ForegroundColor Green
} else {
    Write-Host "CONFIG LOGIC ERROR" -ForegroundColor Red
}

# Simulate startup script call
Write-Host "5. Call startup script ... " -NoNewline
if ($content -match "start_robot\.sh") {
    Write-Host "USES ORIGINAL start_robot.sh" -ForegroundColor Green
} else {
    Write-Host "STARTUP LOGIC ERROR" -ForegroundColor Red
}

# Step 7: Generate Test Report
Write-Host ""
Write-Host "Step 7: Generate Test Report" -ForegroundColor Yellow

$successRate = if ($TotalChecks -gt 0) { [math]::Round(($PassedChecks / $TotalChecks) * 100, 1) } else { 0 }

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Test Results Summary" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Total checks: $TotalChecks"
Write-Host "Passed checks: $PassedChecks"
Write-Host "Failed checks: $($TotalChecks - $PassedChecks)"
Write-Host "Success rate: $successRate%"

Write-Host ""
if ($successRate -ge 90) {
    Write-Host "✓ QD script robot logic is COMPLETE and EFFECTIVE" -ForegroundColor Green
    Write-Host "✓ Original file name logic is CORRECT" -ForegroundColor Green
    Write-Host "✓ Ready for startup testing" -ForegroundColor Green
} elseif ($successRate -ge 70) {
    Write-Host "⚠ QD script robot logic is BASICALLY CORRECT" -ForegroundColor Yellow
    Write-Host "⚠ Some features may need adjustment" -ForegroundColor Yellow
} else {
    Write-Host "✗ QD script robot logic has ISSUES" -ForegroundColor Red
    Write-Host "✗ Needs fixing before testing" -ForegroundColor Red
}

Write-Host ""
Write-Host "Original File Usage:" -ForegroundColor Cyan
Write-Host "  ✓ start_robot.sh - Original startup script"
Write-Host "  ✓ robot.conf - Original config file"
Write-Host "  ✓ robot.jar - Original main program"
Write-Host "  ✓ robot.xio.xml - Original network config"
Write-Host "  ✓ log4j.properties - Original log config"

Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
if ($successRate -ge 90) {
    Write-Host "1. Start game server"
    Write-Host "2. Run ./qd robot for actual testing"
    Write-Host "3. Check robot running status and logs"
} else {
    Write-Host "1. Check and fix failed items"
    Write-Host "2. Ensure all original files exist"
    Write-Host "3. Re-run this test script"
}

Write-Host ""
Write-Host "Logic Verification Results:" -ForegroundColor Cyan
if ($content -match "start_robot\.sh" -and $content -match "robot\.conf") {
    Write-Host "✓ LOGIC CORRECT: Uses original file names" -ForegroundColor Green
    Write-Host "✓ LOGIC CORRECT: Proper startup sequence" -ForegroundColor Green
    Write-Host "✓ LOGIC CORRECT: Configuration management" -ForegroundColor Green
} else {
    Write-Host "✗ LOGIC ERROR: File name usage incorrect" -ForegroundColor Red
}

# Generate detailed report
$reportFile = "qd_robot_logic_test_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
$reportContent = @"
# QD Script Robot Logic Test Report

## Test Information
- **Test Date**: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
- **Test Environment**: Windows PowerShell
- **QD Script**: $QD_SCRIPT
- **Robot Directory**: $ROBOT_DIR

## Test Results Summary
- **Total Checks**: $TotalChecks
- **Passed Checks**: $PassedChecks
- **Failed Checks**: $($TotalChecks - $PassedChecks)
- **Success Rate**: $successRate%

## Original File Verification
$(if (Test-Path "$ROBOT_DIR\robot.jar") { "- ✅ robot.jar: EXISTS" } else { "- ❌ robot.jar: MISSING" })
$(if (Test-Path "$ROBOT_DIR\start_robot.sh") { "- ✅ start_robot.sh: EXISTS" } else { "- ❌ start_robot.sh: MISSING" })
$(if (Test-Path "$ROBOT_DIR\robot.conf") { "- ✅ robot.conf: EXISTS" } else { "- ❌ robot.conf: MISSING" })
$(if (Test-Path "$ROBOT_DIR\robot.xio.xml") { "- ✅ robot.xio.xml: EXISTS" } else { "- ❌ robot.xio.xml: MISSING" })
$(if (Test-Path "$ROBOT_DIR\log4j.properties") { "- ✅ log4j.properties: EXISTS" } else { "- ❌ log4j.properties: MISSING" })

## Logic Verification
$(if ($content -match "start_robot\.sh") { "- ✅ Uses original start_robot.sh" } else { "- ❌ Does not use start_robot.sh" })
$(if ($content -match "robot\.conf") { "- ✅ Uses original robot.conf" } else { "- ❌ Does not use robot.conf" })
$(if ($content -match "check_robot_environment") { "- ✅ Has environment check function" } else { "- ❌ Missing environment check" })

## Conclusion
$(if ($successRate -ge 90) {
"✅ **LOGIC CORRECT**: QD script robot logic is complete and effective. Ready for startup testing."
} elseif ($successRate -ge 70) {
"⚠️ **LOGIC PARTIAL**: QD script robot logic is basically correct but may need minor adjustments."
} else {
"❌ **LOGIC ERROR**: QD script robot logic has issues and needs fixing before testing."
})

## Recommendations
$(if ($successRate -ge 90) {
"1. Proceed with actual robot startup testing
2. Monitor robot execution and logs
3. Verify all test scenarios work correctly"
} else {
"1. Fix missing or incorrect file references
2. Ensure all original files are present
3. Re-run logic verification test"
})

---
*Report generated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')*
"@

$reportContent | Out-File -FilePath $reportFile -Encoding UTF8

Write-Host ""
Write-Host "Test report generated: $reportFile" -ForegroundColor Cyan

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "QD Script Robot Logic Test Complete" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
