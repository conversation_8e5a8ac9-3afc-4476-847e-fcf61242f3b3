#!/bin/bash
#==============================================================================
# 启动脚本验证测试套件
# 版本: 1.0
# 创建时间: 2025-01-29
# 描述: 验证修复后的启动脚本的语法和逻辑正确性
#==============================================================================

set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m'

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
WARNINGS=0

# 测试结果函数
test_result() {
    local test_name="$1"
    local result="$2"
    local message="$3"
    
    ((TOTAL_TESTS++))
    
    if [ "$result" = "PASS" ]; then
        printf "${GREEN}✓ PASS${NC} - $test_name: $message\n"
        ((PASSED_TESTS++))
    elif [ "$result" = "FAIL" ]; then
        printf "${RED}✗ FAIL${NC} - $test_name: $message\n"
        ((FAILED_TESTS++))
    elif [ "$result" = "WARN" ]; then
        printf "${YELLOW}⚠ WARN${NC} - $test_name: $message\n"
        ((WARNINGS++))
    fi
}

# 检查脚本语法
check_script_syntax() {
    local script_path="$1"
    local script_name="$2"
    
    if [ ! -f "$script_path" ]; then
        test_result "语法检查" "FAIL" "$script_name 文件不存在"
        return 1
    fi
    
    if bash -n "$script_path" 2>/dev/null; then
        test_result "语法检查" "PASS" "$script_name 语法正确"
        return 0
    else
        test_result "语法检查" "FAIL" "$script_name 语法错误"
        return 1
    fi
}

# 检查脚本权限
check_script_permissions() {
    local script_path="$1"
    local script_name="$2"
    
    if [ ! -f "$script_path" ]; then
        test_result "权限检查" "FAIL" "$script_name 文件不存在"
        return 1
    fi
    
    if [ -x "$script_path" ]; then
        test_result "权限检查" "PASS" "$script_name 具有执行权限"
        return 0
    else
        test_result "权限检查" "WARN" "$script_name 缺少执行权限"
        return 1
    fi
}

# 检查脚本内容质量
check_script_quality() {
    local script_path="$1"
    local script_name="$2"
    
    if [ ! -f "$script_path" ]; then
        test_result "质量检查" "FAIL" "$script_name 文件不存在"
        return 1
    fi
    
    local issues=0
    
    # 检查是否有严格模式
    if grep -q "set -euo pipefail" "$script_path"; then
        test_result "质量检查" "PASS" "$script_name 使用严格模式"
    else
        test_result "质量检查" "WARN" "$script_name 未使用严格模式"
        ((issues++))
    fi
    
    # 检查是否有错误处理函数
    if grep -q "handle_error\|error_handler" "$script_path"; then
        test_result "质量检查" "PASS" "$script_name 包含错误处理"
    else
        test_result "质量检查" "WARN" "$script_name 缺少错误处理"
        ((issues++))
    fi
    
    # 检查是否有日志函数
    if grep -q "log_message\|log_info\|log_error" "$script_path"; then
        test_result "质量检查" "PASS" "$script_name 包含日志功能"
    else
        test_result "质量检查" "WARN" "$script_name 缺少日志功能"
        ((issues++))
    fi
    
    # 检查变量引用是否安全
    if grep -q '\$[a-zA-Z_][a-zA-Z0-9_]*[^"]' "$script_path" && ! grep -q '"\$[a-zA-Z_]' "$script_path"; then
        test_result "质量检查" "WARN" "$script_name 可能存在不安全的变量引用"
        ((issues++))
    else
        test_result "质量检查" "PASS" "$script_name 变量引用安全"
    fi
    
    return $issues
}

# 检查脚本功能完整性
check_script_functionality() {
    local script_path="$1"
    local script_name="$2"
    local expected_functions="$3"
    
    if [ ! -f "$script_path" ]; then
        test_result "功能检查" "FAIL" "$script_name 文件不存在"
        return 1
    fi
    
    local missing_functions=()
    IFS=',' read -ra FUNCTIONS <<< "$expected_functions"
    
    for func in "${FUNCTIONS[@]}"; do
        if grep -q "^[[:space:]]*$func[[:space:]]*(" "$script_path"; then
            test_result "功能检查" "PASS" "$script_name 包含函数: $func"
        else
            missing_functions+=("$func")
        fi
    done
    
    if [ ${#missing_functions[@]} -gt 0 ]; then
        test_result "功能检查" "WARN" "$script_name 缺少函数: ${missing_functions[*]}"
        return 1
    else
        test_result "功能检查" "PASS" "$script_name 功能完整"
        return 0
    fi
}

# 检查配置文件
check_configuration() {
    local config_path="$1"
    local config_name="$2"
    
    if [ ! -f "$config_path" ]; then
        test_result "配置检查" "WARN" "$config_name 配置文件不存在"
        return 1
    fi
    
    # 检查配置文件是否为空
    if [ -s "$config_path" ]; then
        test_result "配置检查" "PASS" "$config_name 配置文件存在且非空"
    else
        test_result "配置检查" "WARN" "$config_name 配置文件为空"
        return 1
    fi
    
    # 检查是否包含基本配置项
    local required_configs=("GAME_HOME" "LOG_FILE" "MAX_ZONE")
    local missing_configs=()
    
    for config in "${required_configs[@]}"; do
        if grep -q "^[[:space:]]*$config[[:space:]]*=" "$config_path"; then
            test_result "配置检查" "PASS" "$config_name 包含配置: $config"
        else
            missing_configs+=("$config")
        fi
    done
    
    if [ ${#missing_configs[@]} -gt 0 ]; then
        test_result "配置检查" "WARN" "$config_name 缺少配置: ${missing_configs[*]}"
    fi
    
    return 0
}

# 主测试函数
main() {
    printf "${BLUE}=== 启动脚本验证测试套件 ===${NC}\n"
    printf "${BLUE}测试时间: $(date)${NC}\n\n"
    
    # 1. 主管理脚本测试
    printf "${CYAN}1. 主管理脚本测试${NC}\n"
    check_script_syntax "centos7.6_game_server/bin/qd" "主管理脚本"
    check_script_permissions "centos7.6_game_server/bin/qd" "主管理脚本"
    check_script_quality "centos7.6_game_server/bin/qd" "主管理脚本"
    check_script_functionality "centos7.6_game_server/bin/qd" "主管理脚本" "check_dependencies,handle_error,log_message"
    
    # 2. 配置文件测试
    printf "\n${CYAN}2. 配置文件测试${NC}\n"
    check_configuration "centos7.6_game_server/bin/qd.conf" "主配置文件"
    
    # 3. 大区启动脚本测试
    printf "\n${CYAN}3. 大区启动脚本测试${NC}\n"
    check_script_syntax "centos7.6_game_server/game/server1/qd.sh" "大区启动脚本"
    check_script_permissions "centos7.6_game_server/game/server1/qd.sh" "大区启动脚本"
    check_script_quality "centos7.6_game_server/game/server1/qd.sh" "大区启动脚本"
    check_script_functionality "centos7.6_game_server/game/server1/qd.sh" "大区启动脚本" "start_gate,start_proxy,start_game,stop_service"
    
    # 4. 游戏服务器启动脚本测试
    printf "\n${CYAN}4. 游戏服务器启动脚本测试${NC}\n"
    check_script_syntax "centos7.6_game_server/game/server1/game_server/start.sh" "游戏服务器启动脚本"
    check_script_permissions "centos7.6_game_server/game/server1/game_server/start.sh" "游戏服务器启动脚本"
    check_script_quality "centos7.6_game_server/game/server1/game_server/start.sh" "游戏服务器启动脚本"
    check_script_functionality "centos7.6_game_server/game/server1/game_server/start.sh" "游戏服务器启动脚本" "start_server,stop_server,check_status,configure_jvm_options"
    
    # 5. 脚本协调性测试
    printf "\n${CYAN}5. 脚本协调性测试${NC}\n"
    
    # 检查端口配置一致性
    if grep -q "41001" "centos7.6_game_server/bin/qd" && \
       grep -q "41001" "centos7.6_game_server/game/server1/qd.sh" && \
       grep -q "41001" "centos7.6_game_server/game/server1/game_server/start.sh"; then
        test_result "协调性检查" "PASS" "端口配置一致"
    else
        test_result "协调性检查" "WARN" "端口配置可能不一致"
    fi
    
    # 检查日志格式一致性
    if grep -q "log_message" "centos7.6_game_server/bin/qd" && \
       grep -q "log_message" "centos7.6_game_server/game/server1/qd.sh" && \
       grep -q "log_message" "centos7.6_game_server/game/server1/game_server/start.sh"; then
        test_result "协调性检查" "PASS" "日志格式一致"
    else
        test_result "协调性检查" "WARN" "日志格式可能不一致"
    fi
    
    # 6. 安全性检查
    printf "\n${CYAN}6. 安全性检查${NC}\n"
    
    # 检查是否使用了不安全的命令
    local unsafe_patterns=("rm -rf /" "chmod 777" "kill -9.*grep")
    local scripts=("centos7.6_game_server/bin/qd" "centos7.6_game_server/game/server1/qd.sh" "centos7.6_game_server/game/server1/game_server/start.sh")
    
    for script in "${scripts[@]}"; do
        if [ -f "$script" ]; then
            local unsafe_found=false
            for pattern in "${unsafe_patterns[@]}"; do
                if grep -q "$pattern" "$script"; then
                    test_result "安全性检查" "WARN" "$(basename "$script") 包含潜在不安全命令: $pattern"
                    unsafe_found=true
                fi
            done
            if [ "$unsafe_found" = false ]; then
                test_result "安全性检查" "PASS" "$(basename "$script") 未发现不安全命令"
            fi
        fi
    done
    
    # 生成测试报告
    printf "\n${BLUE}=== 测试结果汇总 ===${NC}\n"
    printf "总测试数: $TOTAL_TESTS\n"
    printf "${GREEN}通过: $PASSED_TESTS${NC}\n"
    printf "${RED}失败: $FAILED_TESTS${NC}\n"
    printf "${YELLOW}警告: $WARNINGS${NC}\n"
    
    local success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    printf "成功率: ${success_rate}%%\n"
    
    # 生成建议
    printf "\n${BLUE}=== 建议 ===${NC}\n"
    if [ "$FAILED_TESTS" -eq 0 ]; then
        if [ "$WARNINGS" -eq 0 ]; then
            printf "${GREEN}✓ 所有脚本验证通过！可以安全使用。${NC}\n"
        else
            printf "${YELLOW}⚠ 脚本基本可用，但建议处理警告项以获得更好的体验。${NC}\n"
        fi
    else
        printf "${RED}✗ 发现严重问题，建议修复后再使用。${NC}\n"
    fi
    
    printf "\n${CYAN}快速修复命令：${NC}\n"
    printf "chmod +x centos7.6_game_server/bin/qd\n"
    printf "chmod +x centos7.6_game_server/game/server1/qd.sh\n"
    printf "chmod +x centos7.6_game_server/game/server1/game_server/start.sh\n"
    
    if [ "$FAILED_TESTS" -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# 执行主函数
main "$@"
