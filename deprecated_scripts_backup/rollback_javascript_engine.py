#!/usr/bin/env python3
# JavaScript引擎升级回滚脚本
import shutil
from pathlib import Path

def rollback():
    project_root = Path(".")
    backup_dir = project_root / "backup" / "javascript_engine"
    
    # 恢复原始文件
    original_jsfun = project_root / "src" / "fire" / "script" / "JsFunManager.java"
    backup_jsfun = backup_dir / "JsFunManager.java.original"
    
    if backup_jsfun.exists():
        shutil.copy2(backup_jsfun, original_jsfun)
        print("✓ 已恢复原始JsFunManager.java")
    
    print("回滚完成")

if __name__ == "__main__":
    rollback()
