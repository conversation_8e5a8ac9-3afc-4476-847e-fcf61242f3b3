#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
长期连接稳定性测试
专门测试游戏运行时的掉线问题
"""

import subprocess
import time
import os
import re
from datetime import datetime

class LongTermStabilityTester:
    def __init__(self):
        self.log_file = "game/server1/game_server/gs.log"
        self.running = True
        self.connection_errors = []
        self.last_log_position = 0
        self.start_time = time.time()
        
    def log(self, message, level="INFO"):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        prefix = {
            "INFO": "📊",
            "WARN": "⚠️ ",
            "ERROR": "❌",
            "SUCCESS": "✅",
            "CRITICAL": "🚨"
        }.get(level, "📝")
        print(f"[{timestamp}] {prefix} {message}")
    
    def check_processes(self):
        """检查关键进程状态"""
        processes = {}
        
        # 检查Java进程
        try:
            result = subprocess.run(['powershell', '-Command', 
                'Get-Process java -ErrorAction SilentlyContinue | Select-Object Id'], 
                capture_output=True, text=True, timeout=5)
            processes['java'] = bool(result.returncode == 0 and result.stdout.strip())
        except:
            processes['java'] = False
        
        # 检查Python进程 (Gate Server)
        try:
            result = subprocess.run(['powershell', '-Command', 
                'Get-Process python -ErrorAction SilentlyContinue | Select-Object Id'], 
                capture_output=True, text=True, timeout=5)
            processes['python'] = bool(result.returncode == 0 and result.stdout.strip())
        except:
            processes['python'] = False
        
        return processes
    
    def check_ports(self):
        """检查端口状态"""
        ports = {}
        
        try:
            result = subprocess.run(['powershell', '-Command', 
                'netstat -an | findstr "LISTEN" | findstr "4100\\|4200\\|4300"'], 
                capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                output = result.stdout.strip()
                ports['41001'] = ':41001' in output
                ports['42001'] = ':42001' in output  
                ports['43001'] = ':43001' in output
            else:
                ports = {'41001': False, '42001': False, '43001': False}
        except:
            ports = {'41001': False, '42001': False, '43001': False}
        
        return ports
    
    def analyze_new_logs(self):
        """分析新的日志内容"""
        if not os.path.exists(self.log_file):
            return []
        
        try:
            current_size = os.path.getsize(self.log_file)
            if current_size <= self.last_log_position:
                return []
            
            with open(self.log_file, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(self.last_log_position)
                new_content = f.read()
                self.last_log_position = current_size
                
                # 分析连接相关的错误
                connection_issues = []
                lines = new_content.split('\n')
                
                for line in lines:
                    line = line.strip()
                    if not line:
                        continue
                    
                    # 检查连接中断
                    if 'Connect Abort' in line:
                        connection_issues.append(('DISCONNECT', line))
                        self.connection_errors.append({
                            'time': datetime.now(),
                            'type': 'Connect Abort',
                            'detail': line
                        })
                    
                    # 检查连接拒绝
                    elif '拒绝连接' in line or 'Connection refused' in line:
                        connection_issues.append(('REFUSED', line))
                        self.connection_errors.append({
                            'time': datetime.now(),
                            'type': 'Connection Refused',
                            'detail': line
                        })
                    
                    # 检查超时
                    elif 'timeout' in line.lower() or '超时' in line:
                        connection_issues.append(('TIMEOUT', line))
                        self.connection_errors.append({
                            'time': datetime.now(),
                            'type': 'Timeout',
                            'detail': line
                        })
                    
                    # 检查其他网络错误
                    elif any(keyword in line for keyword in ['IOException', 'SocketException', 'NetworkException']):
                        connection_issues.append(('NETWORK_ERROR', line))
                        self.connection_errors.append({
                            'time': datetime.now(),
                            'type': 'Network Error',
                            'detail': line
                        })
                
                return connection_issues
                
        except Exception as e:
            self.log(f"分析日志失败: {e}", "ERROR")
            return []
    
    def generate_status_report(self, processes, ports, connection_issues):
        """生成状态报告"""
        uptime = time.time() - self.start_time
        hours = int(uptime // 3600)
        minutes = int((uptime % 3600) // 60)
        seconds = int(uptime % 60)
        
        print("\n" + "="*80)
        print(f"🎮 游戏服务器长期稳定性监控 - 运行时间: {hours:02d}:{minutes:02d}:{seconds:02d}")
        print("="*80)
        
        # 进程状态
        print("📊 进程状态:")
        for process, status in processes.items():
            status_icon = "✅" if status else "❌"
            print(f"   {status_icon} {process.upper()}: {'运行中' if status else '已停止'}")
        
        # 端口状态
        print("\n📡 端口状态:")
        port_names = {'41001': 'RMI Server', '42001': 'Gate Server', '43001': 'Provider Server'}
        for port, status in ports.items():
            status_icon = "✅" if status else "❌"
            print(f"   {status_icon} {port} ({port_names[port]}): {'监听中' if status else '未监听'}")
        
        # 连接问题
        if connection_issues:
            print(f"\n🚨 发现 {len(connection_issues)} 个新的连接问题:")
            for issue_type, detail in connection_issues:
                print(f"   ⚠️  {issue_type}: {detail}")
        else:
            print("\n✅ 本次检查未发现连接问题")
        
        # 累计错误统计
        if self.connection_errors:
            print(f"\n📈 累计连接错误统计 (共 {len(self.connection_errors)} 个):")
            error_types = {}
            for error in self.connection_errors:
                error_type = error['type']
                error_types[error_type] = error_types.get(error_type, 0) + 1
            
            for error_type, count in error_types.items():
                print(f"   📊 {error_type}: {count} 次")
        
        print("="*80)
    
    def run_long_term_test(self, duration_hours=2, check_interval=60):
        """运行长期稳定性测试"""
        self.log(f"开始长期稳定性测试 (持续 {duration_hours} 小时，每 {check_interval} 秒检查一次)")
        
        end_time = time.time() + (duration_hours * 3600)
        check_count = 0
        
        try:
            while time.time() < end_time and self.running:
                check_count += 1
                
                # 检查进程状态
                processes = self.check_processes()
                
                # 检查端口状态
                ports = self.check_ports()
                
                # 分析新日志
                connection_issues = self.analyze_new_logs()
                
                # 生成报告
                self.generate_status_report(processes, ports, connection_issues)
                
                # 检查关键问题
                if not processes['java']:
                    self.log("🚨 关键问题：Java进程已停止！", "CRITICAL")
                
                if not ports['43001']:
                    self.log("🚨 关键问题：43001端口未监听！", "CRITICAL")
                
                if connection_issues:
                    self.log(f"⚠️  发现 {len(connection_issues)} 个连接问题", "WARN")
                
                # 等待下次检查
                if time.time() < end_time:
                    self.log(f"等待 {check_interval} 秒后进行第 {check_count + 1} 次检查...")
                    time.sleep(check_interval)
            
            # 测试完成总结
            self.log(f"长期稳定性测试完成！共进行了 {check_count} 次检查", "SUCCESS")
            
            if self.connection_errors:
                self.log(f"测试期间发现 {len(self.connection_errors)} 个连接问题", "WARN")
                print("\n📋 详细错误列表:")
                for i, error in enumerate(self.connection_errors, 1):
                    print(f"{i:3d}. [{error['time'].strftime('%H:%M:%S')}] {error['type']}: {error['detail'][:100]}...")
            else:
                self.log("测试期间未发现任何连接问题！", "SUCCESS")
            
        except KeyboardInterrupt:
            self.log("测试被用户中断", "WARN")
        except Exception as e:
            self.log(f"测试过程中发生错误: {e}", "ERROR")

def main():
    print("🎮 游戏服务器长期稳定性测试工具")
    print("=" * 60)
    
    tester = LongTermStabilityTester()
    
    # 运行2小时的长期测试，每分钟检查一次
    tester.run_long_term_test(duration_hours=2, check_interval=60)

if __name__ == "__main__":
    main()
