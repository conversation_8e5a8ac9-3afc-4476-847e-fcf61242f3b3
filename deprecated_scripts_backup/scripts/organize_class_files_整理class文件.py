#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Class文件整理工具
Class Files Organization Tool
============================

整理out目录中的class文件，区分正式文件和测试文件
Organize class files in out directory, separating production and test files

功能特性:
- 识别正式生产文件和测试文件
- 保留正式文件在out/production目录
- 移动测试文件到out/test目录
- 生成整理报告
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Set

class ClassFileOrganizer:
    """Class文件整理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.out_dir = self.project_root / "out"
        self.production_dir = self.out_dir / "production" / "gsxdb"
        self.test_dir = self.out_dir / "test" / "gsxdb"
        
        # 正式生产文件模式（这些是核心业务文件）
        self.production_patterns = {
            # 核心业务类
            "core_business": [
                "mkdb/Bean.class",
                "mkdb/Module.class", 
                "mkdb/Procedure*.class",
                "fire/pb/battle/Fighter*.class",
                "fire/pb/battle/BuffAgent.class",
                "fire/pb/role/EffectRole.class",
                "fire/script/JsFunManager.class",
                "fire/script/AbstractJSEngine.class",
                "fire/script/IJavaScriptEngine.class",
                "fire/script/FightJSEngine.class"
            ],
            
            # 修复和工具类（生产环境需要）
            "production_tools": [
                "fire/script/JavaScriptEngineFix.class"
            ]
        }
        
        # 测试和开发文件模式
        self.test_patterns = {
            # 测试和实验性文件
            "test_experimental": [
                "fire/script/JsFunManagerUpgraded.class",
                "fire/script/JsFunManagerFixed.class", 
                "fire/script/JavaScriptCompatibilityAdapter.class",
                "fire/script/TestJSEngine.class"
            ],
            
            # 临时和调试文件
            "temporary_debug": [
                "*Test*.class",
                "*Debug*.class",
                "*Temp*.class",
                "*Experimental*.class"
            ]
        }

    def organize_class_files(self):
        """整理class文件"""
        print("🗂️ 开始整理class文件...")
        
        organization_result = {
            "timestamp": datetime.now().isoformat(),
            "production_files": [],
            "test_files": [],
            "moved_files": [],
            "preserved_files": [],
            "statistics": {},
            "organization_success": False
        }
        
        try:
            # 步骤1: 分析现有文件
            self._analyze_existing_files(organization_result)
            
            # 步骤2: 创建目录结构
            self._create_directory_structure(organization_result)
            
            # 步骤3: 分类文件
            self._classify_files(organization_result)
            
            # 步骤4: 移动测试文件
            self._move_test_files(organization_result)
            
            # 步骤5: 验证整理结果
            self._verify_organization(organization_result)
            
            # 步骤6: 生成整理报告
            self._generate_organization_report(organization_result)
            
            organization_result["organization_success"] = True
            print("✅ Class文件整理完成!")
            
        except Exception as e:
            error_msg = f"整理过程中发生错误: {str(e)}"
            print(f"❌ {error_msg}")
            organization_result["error"] = error_msg
        
        return organization_result

    def _analyze_existing_files(self, result: dict):
        """分析现有文件"""
        print("📊 分析现有class文件...")
        
        if not self.production_dir.exists():
            print("  ⚠️ production目录不存在")
            return
        
        all_class_files = []
        
        # 递归查找所有.class文件
        for class_file in self.production_dir.rglob("*.class"):
            relative_path = class_file.relative_to(self.production_dir)
            file_info = {
                "path": str(relative_path),
                "full_path": str(class_file),
                "size": class_file.stat().st_size,
                "modified": datetime.fromtimestamp(class_file.stat().st_mtime).isoformat()
            }
            all_class_files.append(file_info)
        
        result["all_files"] = all_class_files
        print(f"  📁 发现 {len(all_class_files)} 个class文件")

    def _create_directory_structure(self, result: dict):
        """创建目录结构"""
        print("📁 创建目录结构...")
        
        # 创建test目录
        self.test_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录结构
        subdirs = ["fire/script", "fire/pb/battle", "fire/pb/role", "mkdb"]
        for subdir in subdirs:
            (self.test_dir / subdir).mkdir(parents=True, exist_ok=True)
        
        print(f"  ✓ 创建测试目录: {self.test_dir}")

    def _classify_files(self, result: dict):
        """分类文件"""
        print("🔍 分类文件...")
        
        all_files = result.get("all_files", [])
        production_files = []
        test_files = []
        
        for file_info in all_files:
            file_path = file_info["path"]
            is_production = self._is_production_file(file_path)
            
            if is_production:
                production_files.append(file_info)
            else:
                test_files.append(file_info)
        
        result["production_files"] = production_files
        result["test_files"] = test_files
        
        print(f"  📋 正式文件: {len(production_files)} 个")
        print(f"  🧪 测试文件: {len(test_files)} 个")

    def _is_production_file(self, file_path: str) -> bool:
        """判断是否为正式生产文件"""
        
        # 检查是否匹配生产文件模式
        for category, patterns in self.production_patterns.items():
            for pattern in patterns:
                if self._match_pattern(file_path, pattern):
                    return True
        
        # 检查是否匹配测试文件模式
        for category, patterns in self.test_patterns.items():
            for pattern in patterns:
                if self._match_pattern(file_path, pattern):
                    return False
        
        # 默认情况：核心包路径的文件视为生产文件
        core_paths = ["mkdb/", "fire/pb/battle/", "fire/pb/role/"]
        for core_path in core_paths:
            if file_path.startswith(core_path):
                return True
        
        # 其他情况视为测试文件
        return False

    def _match_pattern(self, file_path: str, pattern: str) -> bool:
        """匹配文件模式"""
        import fnmatch
        return fnmatch.fnmatch(file_path.replace("\\", "/"), pattern)

    def _move_test_files(self, result: dict):
        """移动测试文件"""
        print("📦 移动测试文件...")
        
        test_files = result.get("test_files", [])
        moved_files = []
        
        for file_info in test_files:
            try:
                source_path = Path(file_info["full_path"])
                relative_path = file_info["path"]
                target_path = self.test_dir / relative_path
                
                # 确保目标目录存在
                target_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 移动文件
                shutil.move(str(source_path), str(target_path))
                
                moved_info = {
                    "file": relative_path,
                    "from": str(source_path),
                    "to": str(target_path),
                    "size": file_info["size"]
                }
                moved_files.append(moved_info)
                
                print(f"  📦 移动: {relative_path}")
                
            except Exception as e:
                print(f"  ❌ 移动失败: {relative_path} - {str(e)}")
        
        result["moved_files"] = moved_files
        print(f"  ✓ 成功移动 {len(moved_files)} 个测试文件")

    def _verify_organization(self, result: dict):
        """验证整理结果"""
        print("✅ 验证整理结果...")
        
        # 统计production目录中的文件
        production_count = len(list(self.production_dir.rglob("*.class")))
        
        # 统计test目录中的文件
        test_count = len(list(self.test_dir.rglob("*.class"))) if self.test_dir.exists() else 0
        
        # 计算总数
        total_original = len(result.get("all_files", []))
        total_current = production_count + test_count
        
        statistics = {
            "original_total": total_original,
            "current_total": total_current,
            "production_files": production_count,
            "test_files": test_count,
            "files_moved": len(result.get("moved_files", [])),
            "organization_integrity": total_original == total_current
        }
        
        result["statistics"] = statistics
        
        print(f"  📊 原始文件总数: {total_original}")
        print(f"  📊 当前文件总数: {total_current}")
        print(f"  📁 正式文件: {production_count} 个")
        print(f"  🧪 测试文件: {test_count} 个")
        print(f"  📦 移动文件: {len(result.get('moved_files', []))} 个")
        
        if statistics["organization_integrity"]:
            print(f"  ✅ 文件完整性验证通过")
        else:
            print(f"  ⚠️ 文件数量不匹配，请检查")

    def _generate_organization_report(self, result: dict):
        """生成整理报告"""
        print("📄 生成整理报告...")
        
        report_file = self.project_root / "class_files_organization_report.json"
        
        # 添加整理总结
        result["organization_summary"] = {
            "total_files_processed": len(result.get("all_files", [])),
            "production_files_count": len(result.get("production_files", [])),
            "test_files_count": len(result.get("test_files", [])),
            "files_moved_count": len(result.get("moved_files", [])),
            "organization_date": datetime.now().isoformat(),
            "production_directory": str(self.production_dir),
            "test_directory": str(self.test_dir)
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"  ✓ 整理报告已保存: {report_file}")

    def create_build_script(self):
        """创建构建脚本"""
        print("📝 创建构建脚本...")
        
        # 创建生产环境构建脚本
        production_build_script = self.project_root / "build_production.py"
        production_script_content = '''#!/usr/bin/env python3
# 生产环境构建脚本
import subprocess
from pathlib import Path

def build_production():
    """构建生产环境"""
    project_root = Path(".")
    
    # 编译核心业务文件
    core_files = [
        "src/mkdb/Bean.java",
        "src/mkdb/Module.java", 
        "src/fire/pb/battle/Fighter.java",
        "src/fire/script/JsFunManager.java",
        "src/fire/script/JavaScriptEngineFix.java",
        "src/fire/script/FightJSEngine.java"
    ]
    
    print("🔨 编译生产环境文件...")
    
    for java_file in core_files:
        if Path(java_file).exists():
            cmd = [
                "javac",
                "-cp", "lib/*;gs_lib/*;lib2/*;libsys/*",
                "-encoding", "UTF-8",
                "-sourcepath", "src",
                "-d", "out/production/gsxdb",
                java_file
            ]
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"  ✅ {Path(java_file).name}")
                else:
                    print(f"  ❌ {Path(java_file).name}: {result.stderr}")
            except Exception as e:
                print(f"  ❌ {Path(java_file).name}: {str(e)}")
    
    print("✅ 生产环境构建完成")

if __name__ == "__main__":
    build_production()
'''
        
        with open(production_build_script, 'w', encoding='utf-8') as f:
            f.write(production_script_content)
        
        # 创建测试环境构建脚本
        test_build_script = self.project_root / "build_test.py"
        test_script_content = '''#!/usr/bin/env python3
# 测试环境构建脚本
import subprocess
from pathlib import Path

def build_test():
    """构建测试环境"""
    project_root = Path(".")
    
    # 编译测试文件
    test_files = [
        "src/fire/script/JavaScriptCompatibilityAdapter.java",
        "src/fire/script/JsFunManagerUpgraded.java"
    ]
    
    print("🧪 编译测试环境文件...")
    
    for java_file in test_files:
        if Path(java_file).exists():
            cmd = [
                "javac",
                "-cp", "lib/*;gs_lib/*;lib2/*;libsys/*;out/production/gsxdb",
                "-encoding", "UTF-8",
                "-sourcepath", "src",
                "-d", "out/test/gsxdb",
                java_file
            ]
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"  ✅ {Path(java_file).name}")
                else:
                    print(f"  ❌ {Path(java_file).name}: {result.stderr}")
            except Exception as e:
                print(f"  ❌ {Path(java_file).name}: {str(e)}")
    
    print("✅ 测试环境构建完成")

if __name__ == "__main__":
    build_test()
'''
        
        with open(test_build_script, 'w', encoding='utf-8') as f:
            f.write(test_script_content)
        
        print(f"  ✓ 生产构建脚本: {production_build_script}")
        print(f"  ✓ 测试构建脚本: {test_build_script}")

def main():
    """主函数"""
    organizer = ClassFileOrganizer()
    
    print("🗂️ Class文件整理工具")
    print("=" * 50)
    
    # 创建构建脚本
    organizer.create_build_script()
    
    # 执行整理
    result = organizer.organize_class_files()
    
    print("\n" + "=" * 50)
    print("📊 Class文件整理总结")
    print("=" * 50)
    
    # 显示整理结果
    organization_success = result.get("organization_success", False)
    statistics = result.get("statistics", {})
    
    print(f"📈 整理统计:")
    print(f"  📁 原始文件总数: {statistics.get('original_total', 0)} 个")
    print(f"  📁 正式文件: {statistics.get('production_files', 0)} 个")
    print(f"  🧪 测试文件: {statistics.get('test_files', 0)} 个")
    print(f"  📦 移动文件: {statistics.get('files_moved', 0)} 个")
    print(f"  🎯 整理状态: {'成功' if organization_success else '失败'}")
    
    # 显示目录结构
    if organization_success:
        print(f"\n📁 目录结构:")
        print(f"  📂 out/production/gsxdb/ - 正式生产文件")
        print(f"  📂 out/test/gsxdb/ - 测试开发文件")
        
        print(f"\n🔨 构建脚本:")
        print(f"  📝 build_production.py - 生产环境构建")
        print(f"  📝 build_test.py - 测试环境构建")
        
        print(f"\n💡 使用建议:")
        print(f"  1. 生产部署时只使用 out/production/ 目录")
        print(f"  2. 开发测试时可以使用 out/test/ 目录")
        print(f"  3. 使用构建脚本重新编译特定环境")
        print(f"  4. 定期清理不需要的测试文件")
    
    print(f"\n📄 详细报告: class_files_organization_report.json")

if __name__ == "__main__":
    main()
