#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CentOS 7.6游戏服务器模拟器
CentOS 7.6 Game Server Simulator
===============================

模拟CentOS 7.6环境运行游戏服务器，使用MCP工具进行本地测试
Simulate CentOS 7.6 environment to run game server with MCP tools for local testing
"""

import os
import sys
import subprocess
import json
import time
import shutil
from pathlib import Path
from datetime import datetime
import platform

class CentOSGameServerSimulator:
    """CentOS游戏服务器模拟器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.simulation_root = self.project_root / "centos_simulation"
        self.logs_dir = self.simulation_root / "logs"
        self.config_dir = self.simulation_root / "config"
        
    def setup_simulation_environment(self):
        """设置模拟环境"""
        print("🐧 设置CentOS 7.6游戏服务器模拟环境...")
        
        setup_result = {
            "timestamp": datetime.now().isoformat(),
            "environment_setup": {},
            "java_environment": {},
            "dependencies_check": {},
            "simulation_ready": False
        }
        
        try:
            # 创建模拟目录结构
            self._create_simulation_directories()
            
            # 检查Java环境
            java_info = self._check_java_environment()
            setup_result["java_environment"] = java_info
            
            # 检查依赖文件
            deps_info = self._check_dependencies()
            setup_result["dependencies_check"] = deps_info
            
            # 准备CentOS风格的配置
            self._prepare_centos_config()
            
            # 复制必要的文件
            self._copy_essential_files()
            
            setup_result["simulation_ready"] = True
            print("✅ CentOS模拟环境设置完成")
            
        except Exception as e:
            setup_result["error"] = str(e)
            print(f"❌ 环境设置失败: {str(e)}")
        
        return setup_result
    
    def _create_simulation_directories(self):
        """创建模拟目录结构"""
        print("  📁 创建目录结构...")
        
        # CentOS 7.6游戏服务器标准目录结构
        directories = [
            "centos_simulation",
            "centos_simulation/home/<USER>/server1/game_server",
            "centos_simulation/home/<USER>/server1/game_server/gamedata",
            "centos_simulation/home/<USER>/server1/game_server/gamedata/xml",
            "centos_simulation/home/<USER>/server1/game_server/gamedata/xml/auto",
            "centos_simulation/home/<USER>/server1/game_server/lib",
            "centos_simulation/home/<USER>/server1/game_server/gs_lib", 
            "centos_simulation/home/<USER>/server1/game_server/lib2",
            "centos_simulation/home/<USER>/server1/game_server/libsys",
            "centos_simulation/logs",
            "centos_simulation/config",
            "centos_simulation/backup"
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"    ✓ {directory}")
    
    def _check_java_environment(self):
        """检查Java环境"""
        print("  ☕ 检查Java环境...")
        
        java_info = {
            "java_available": False,
            "java_version": "",
            "java_home": "",
            "classpath_support": False
        }
        
        try:
            # 检查Java版本
            result = subprocess.run(
                ["java", "-version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                java_info["java_available"] = True
                # Java版本信息通常在stderr中
                version_output = result.stderr if result.stderr else result.stdout
                java_info["java_version"] = version_output.split('\n')[0]
                print(f"    ✓ Java版本: {java_info['java_version']}")
                
                # 检查JAVA_HOME
                java_home = os.environ.get('JAVA_HOME', '')
                if java_home:
                    java_info["java_home"] = java_home
                    print(f"    ✓ JAVA_HOME: {java_home}")
                else:
                    print("    ⚠️ JAVA_HOME未设置")
                
                # 检查classpath支持
                java_info["classpath_support"] = True
                print("    ✓ Classpath支持正常")
            else:
                print("    ❌ Java不可用")
                
        except Exception as e:
            print(f"    ❌ Java环境检查失败: {str(e)}")
        
        return java_info
    
    def _check_dependencies(self):
        """检查依赖文件"""
        print("  📦 检查依赖文件...")
        
        deps_info = {
            "jar_files": {},
            "lib_directories": {},
            "essential_files": {},
            "all_dependencies_ready": False
        }
        
        # 检查关键jar文件
        jar_files = ["gsxdb.jar"]
        for jar_file in jar_files:
            jar_path = self.project_root / jar_file
            deps_info["jar_files"][jar_file] = {
                "exists": jar_path.exists(),
                "size": jar_path.stat().st_size if jar_path.exists() else 0,
                "path": str(jar_path)
            }
            
            if jar_path.exists():
                print(f"    ✓ {jar_file} ({deps_info['jar_files'][jar_file]['size']} bytes)")
            else:
                print(f"    ❌ {jar_file} 缺失")
        
        # 检查lib目录
        lib_dirs = ["lib", "gs_lib", "lib2", "libsys"]
        for lib_dir in lib_dirs:
            lib_path = self.project_root / lib_dir
            if lib_path.exists():
                jar_count = len(list(lib_path.glob("*.jar")))
                deps_info["lib_directories"][lib_dir] = {
                    "exists": True,
                    "jar_count": jar_count
                }
                print(f"    ✓ {lib_dir}/ ({jar_count} jar文件)")
            else:
                deps_info["lib_directories"][lib_dir] = {"exists": False}
                print(f"    ❌ {lib_dir}/ 目录缺失")
        
        # 检查关键class文件
        essential_classes = [
            "out/production/gsxdb/fire/script/AbstractJSEngine.class",
            "out/production/gsxdb/fire/script/FightJSEngine.class",
            "out/production/gsxdb/fire/script/SceneJSEngine.class",
            "out/production/gsxdb/fire/pb/role/EffectRole.class"
        ]
        
        for class_file in essential_classes:
            class_path = self.project_root / class_file
            class_name = Path(class_file).name
            deps_info["essential_files"][class_name] = {
                "exists": class_path.exists(),
                "size": class_path.stat().st_size if class_path.exists() else 0
            }
            
            if class_path.exists():
                print(f"    ✓ {class_name} ({deps_info['essential_files'][class_name]['size']} bytes)")
            else:
                print(f"    ❌ {class_name} 缺失")
        
        # 判断依赖是否就绪
        jar_ready = all(info["exists"] for info in deps_info["jar_files"].values())
        lib_ready = all(info["exists"] for info in deps_info["lib_directories"].values())
        class_ready = all(info["exists"] for info in deps_info["essential_files"].values())
        
        deps_info["all_dependencies_ready"] = jar_ready and lib_ready and class_ready
        
        return deps_info
    
    def _prepare_centos_config(self):
        """准备CentOS风格的配置"""
        print("  ⚙️ 准备CentOS配置...")
        
        # 创建CentOS风格的启动脚本
        startup_script = self.simulation_root / "start_game_server.sh"
        
        startup_content = '''#!/bin/bash
# CentOS 7.6 Game Server Startup Script
# 游戏服务器启动脚本

echo "🐧 CentOS 7.6 Game Server Starting..."
echo "服务器启动时间: $(date)"

# 设置Java环境
export JAVA_HOME=${JAVA_HOME:-/usr/lib/jvm/java-1.8.0-openjdk}
export PATH=$JAVA_HOME/bin:$PATH

# 设置游戏服务器路径
GAME_SERVER_HOME="/home/<USER>/server1/game_server"
cd "$GAME_SERVER_HOME"

# 设置JVM参数 (CentOS 7.6优化)
JVM_OPTS="-Xms2048m -Xmx4096m"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=200"
JVM_OPTS="$JVM_OPTS -XX:+UnlockExperimentalVMOptions"
JVM_OPTS="$JVM_OPTS -XX:+UseStringDeduplication"
JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
JVM_OPTS="$JVM_OPTS -Duser.timezone=Asia/Shanghai"

# 设置Classpath
CLASSPATH="gsxdb.jar"
CLASSPATH="$CLASSPATH:lib/*"
CLASSPATH="$CLASSPATH:gs_lib/*"
CLASSPATH="$CLASSPATH:lib2/*"
CLASSPATH="$CLASSPATH:libsys/*"

# 调试端口
DEBUG_OPTS="-Xdebug -Xrunjdwp:transport=dt_socket,address=42998,server=y,suspend=n"

echo "启动参数:"
echo "  JVM_OPTS: $JVM_OPTS"
echo "  CLASSPATH: $CLASSPATH"
echo "  DEBUG_OPTS: $DEBUG_OPTS"

# 启动游戏服务器
echo "🚀 启动游戏服务器..."
java $JVM_OPTS $DEBUG_OPTS -cp "$CLASSPATH" fire.pb.main.Gs

echo "游戏服务器已停止"
'''
        
        with open(startup_script, 'w', encoding='utf-8') as f:
            f.write(startup_content)
        
        # 设置执行权限 (在Windows上模拟)
        if platform.system() != "Windows":
            os.chmod(startup_script, 0o755)
        
        print(f"    ✓ 启动脚本: {startup_script}")
    
    def _copy_essential_files(self):
        """复制必要的文件到模拟环境"""
        print("  📋 复制必要文件...")
        
        game_server_dir = self.simulation_root / "home/game/server1/game_server"
        
        # 复制jar文件
        if (self.project_root / "gsxdb.jar").exists():
            shutil.copy2(
                self.project_root / "gsxdb.jar",
                game_server_dir / "gsxdb.jar"
            )
            print("    ✓ gsxdb.jar")
        
        # 复制lib目录
        for lib_dir in ["lib", "gs_lib", "lib2", "libsys"]:
            src_lib = self.project_root / lib_dir
            dst_lib = game_server_dir / lib_dir
            
            if src_lib.exists():
                if dst_lib.exists():
                    shutil.rmtree(dst_lib)
                shutil.copytree(src_lib, dst_lib)
                jar_count = len(list(dst_lib.glob("*.jar")))
                print(f"    ✓ {lib_dir}/ ({jar_count} files)")
    
    def run_server_simulation(self):
        """运行服务器模拟"""
        print("🚀 运行CentOS 7.6游戏服务器模拟...")
        
        simulation_result = {
            "timestamp": datetime.now().isoformat(),
            "startup_attempts": [],
            "error_analysis": {},
            "log_analysis": {},
            "simulation_success": False
        }
        
        try:
            # 切换到模拟环境目录
            game_server_dir = self.simulation_root / "home/game/server1/game_server"
            
            # 准备启动命令
            java_cmd = [
                "java",
                "-Xms1024m", "-Xmx2048m",
                "-XX:+UseG1GC",
                "-Dfile.encoding=UTF-8",
                "-Duser.timezone=Asia/Shanghai",
                "-Xdebug", "-Xrunjdwp:transport=dt_socket,address=42999,server=y,suspend=n",
                "-cp", "gsxdb.jar;lib/*;gs_lib/*;lib2/*;libsys/*",
                "fire.pb.main.Gs"
            ]
            
            print(f"  📍 工作目录: {game_server_dir}")
            print(f"  🔧 启动命令: {' '.join(java_cmd)}")
            
            # 启动服务器进程
            print("  🚀 启动游戏服务器进程...")
            
            startup_attempt = {
                "attempt_time": datetime.now().isoformat(),
                "command": java_cmd,
                "working_directory": str(game_server_dir),
                "success": False,
                "error_output": "",
                "runtime_seconds": 0
            }
            
            start_time = time.time()
            
            process = subprocess.Popen(
                java_cmd,
                cwd=game_server_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # 等待一段时间收集启动日志
            print("  ⏱️ 等待服务器启动 (30秒)...")
            
            stdout_lines = []
            stderr_lines = []
            
            # 非阻塞读取输出
            for i in range(30):  # 30秒超时
                time.sleep(1)
                
                # 检查进程是否还在运行
                if process.poll() is not None:
                    print(f"    ⚠️ 进程在{i+1}秒后退出")
                    break
                
                print(f"    ⏳ 等待中... {i+1}/30秒")
            
            # 终止进程并收集输出
            if process.poll() is None:
                process.terminate()
                print("    🛑 进程已终止")
            
            stdout, stderr = process.communicate(timeout=5)
            
            runtime = time.time() - start_time
            startup_attempt["runtime_seconds"] = round(runtime, 2)
            startup_attempt["stdout"] = stdout[:2000] if stdout else ""
            startup_attempt["stderr"] = stderr[:2000] if stderr else ""
            startup_attempt["return_code"] = process.returncode
            startup_attempt["success"] = process.returncode == 0
            
            simulation_result["startup_attempts"].append(startup_attempt)
            
            # 分析错误输出
            if stderr:
                error_analysis = self._analyze_error_output(stderr)
                simulation_result["error_analysis"] = error_analysis
            
            # 检查日志文件
            log_analysis = self._analyze_log_files()
            simulation_result["log_analysis"] = log_analysis
            
            simulation_result["simulation_success"] = startup_attempt["success"]
            
            print(f"  📊 模拟结果: {'成功' if simulation_result['simulation_success'] else '失败'}")
            
        except Exception as e:
            simulation_result["error"] = str(e)
            print(f"  ❌ 模拟运行失败: {str(e)}")
        
        return simulation_result
    
    def _analyze_error_output(self, stderr_output: str):
        """分析错误输出"""
        print("  🔍 分析错误输出...")
        
        error_analysis = {
            "verify_errors": [],
            "nosuchmethod_errors": [],
            "classnotfound_errors": [],
            "other_errors": [],
            "error_summary": {}
        }
        
        lines = stderr_output.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if "VerifyError" in line:
                error_analysis["verify_errors"].append(line)
            elif "NoSuchMethodError" in line:
                error_analysis["nosuchmethod_errors"].append(line)
            elif "ClassNotFoundException" in line or "NoClassDefFoundError" in line:
                error_analysis["classnotfound_errors"].append(line)
            elif "Exception" in line or "Error" in line:
                error_analysis["other_errors"].append(line)
        
        # 生成错误总结
        error_analysis["error_summary"] = {
            "verify_error_count": len(error_analysis["verify_errors"]),
            "nosuchmethod_error_count": len(error_analysis["nosuchmethod_errors"]),
            "classnotfound_error_count": len(error_analysis["classnotfound_errors"]),
            "other_error_count": len(error_analysis["other_errors"]),
            "total_errors": len(error_analysis["verify_errors"]) + 
                           len(error_analysis["nosuchmethod_errors"]) + 
                           len(error_analysis["classnotfound_errors"]) + 
                           len(error_analysis["other_errors"])
        }
        
        print(f"    📊 错误统计:")
        print(f"      VerifyError: {error_analysis['error_summary']['verify_error_count']}")
        print(f"      NoSuchMethodError: {error_analysis['error_summary']['nosuchmethod_error_count']}")
        print(f"      ClassNotFound: {error_analysis['error_summary']['classnotfound_error_count']}")
        print(f"      其他错误: {error_analysis['error_summary']['other_error_count']}")
        
        return error_analysis
    
    def _analyze_log_files(self):
        """分析日志文件"""
        print("  📄 分析日志文件...")
        
        log_analysis = {
            "gs_log_exists": False,
            "gs_log_size": 0,
            "recent_errors": [],
            "startup_success": False
        }
        
        # 检查gs.log文件
        gs_log_path = self.project_root / "gs.log"
        if gs_log_path.exists():
            log_analysis["gs_log_exists"] = True
            log_analysis["gs_log_size"] = gs_log_path.stat().st_size
            
            # 读取最近的日志内容
            try:
                with open(gs_log_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    recent_lines = lines[-50:] if len(lines) > 50 else lines
                    
                    for line in recent_lines:
                        if "ERROR" in line or "Exception" in line or "VerifyError" in line:
                            log_analysis["recent_errors"].append(line.strip())
                    
                    # 检查是否成功启动
                    log_content = ''.join(recent_lines)
                    if "SERVER STARTING" in log_content and "VerifyError" not in log_content:
                        log_analysis["startup_success"] = True
                        
            except Exception as e:
                print(f"    ⚠️ 读取日志文件失败: {str(e)}")
        
        print(f"    📊 日志分析:")
        print(f"      gs.log存在: {log_analysis['gs_log_exists']}")
        print(f"      日志大小: {log_analysis['gs_log_size']} bytes")
        print(f"      最近错误: {len(log_analysis['recent_errors'])} 条")
        print(f"      启动成功: {log_analysis['startup_success']}")
        
        return log_analysis
    
    def generate_simulation_report(self, setup_result, simulation_result):
        """生成模拟报告"""
        print("📄 生成CentOS模拟测试报告...")
        
        report = {
            "simulation_info": {
                "timestamp": datetime.now().isoformat(),
                "environment": "CentOS 7.6 Simulation",
                "project_root": str(self.project_root),
                "simulation_root": str(self.simulation_root)
            },
            "setup_result": setup_result,
            "simulation_result": simulation_result,
            "conclusions": self._generate_conclusions(setup_result, simulation_result)
        }
        
        report_file = self.project_root / "centos_simulation_report.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"  ✓ 报告已保存: {report_file}")
        
        return report
    
    def _generate_conclusions(self, setup_result, simulation_result):
        """生成结论"""
        conclusions = {
            "environment_ready": setup_result.get("simulation_ready", False),
            "java_environment_ok": setup_result.get("java_environment", {}).get("java_available", False),
            "dependencies_ready": setup_result.get("dependencies_check", {}).get("all_dependencies_ready", False),
            "simulation_successful": simulation_result.get("simulation_success", False),
            "major_issues": [],
            "recommendations": []
        }
        
        # 分析主要问题
        error_analysis = simulation_result.get("error_analysis", {})
        error_summary = error_analysis.get("error_summary", {})
        
        if error_summary.get("verify_error_count", 0) > 0:
            conclusions["major_issues"].append("VerifyError - 类型兼容性问题")
        
        if error_summary.get("nosuchmethod_error_count", 0) > 0:
            conclusions["major_issues"].append("NoSuchMethodError - 方法签名问题")
        
        if error_summary.get("classnotfound_error_count", 0) > 0:
            conclusions["major_issues"].append("ClassNotFoundException - 类文件缺失")
        
        # 生成建议
        if not conclusions["java_environment_ok"]:
            conclusions["recommendations"].append("安装或配置Java环境")
        
        if not conclusions["dependencies_ready"]:
            conclusions["recommendations"].append("检查并更新依赖文件")
        
        if conclusions["major_issues"]:
            conclusions["recommendations"].append("修复已识别的主要问题")
        else:
            conclusions["recommendations"].append("环境配置良好，可以正常运行")
        
        return conclusions

def main():
    """主函数"""
    simulator = CentOSGameServerSimulator()
    
    print("🐧 CentOS 7.6游戏服务器模拟器")
    print("=" * 60)
    
    # 步骤1: 设置模拟环境
    print("\n📋 步骤1: 设置模拟环境")
    setup_result = simulator.setup_simulation_environment()
    
    # 步骤2: 运行服务器模拟
    print("\n🚀 步骤2: 运行服务器模拟")
    simulation_result = simulator.run_server_simulation()
    
    # 步骤3: 生成报告
    print("\n📄 步骤3: 生成模拟报告")
    report = simulator.generate_simulation_report(setup_result, simulation_result)
    
    # 显示总结
    print("\n" + "=" * 60)
    print("📊 CentOS模拟测试总结")
    print("=" * 60)
    
    conclusions = report["conclusions"]
    
    print(f"🔧 环境状态:")
    print(f"  环境就绪: {'✅' if conclusions['environment_ready'] else '❌'}")
    print(f"  Java环境: {'✅' if conclusions['java_environment_ok'] else '❌'}")
    print(f"  依赖就绪: {'✅' if conclusions['dependencies_ready'] else '❌'}")
    print(f"  模拟成功: {'✅' if conclusions['simulation_successful'] else '❌'}")
    
    if conclusions["major_issues"]:
        print(f"\n🚨 主要问题:")
        for issue in conclusions["major_issues"]:
            print(f"  ❌ {issue}")
    
    if conclusions["recommendations"]:
        print(f"\n💡 建议:")
        for recommendation in conclusions["recommendations"]:
            print(f"  🔧 {recommendation}")
    
    # 显示错误统计
    error_summary = simulation_result.get("error_analysis", {}).get("error_summary", {})
    if error_summary:
        print(f"\n📊 错误统计:")
        print(f"  VerifyError: {error_summary.get('verify_error_count', 0)}")
        print(f"  NoSuchMethodError: {error_summary.get('nosuchmethod_error_count', 0)}")
        print(f"  ClassNotFound: {error_summary.get('classnotfound_error_count', 0)}")
        print(f"  其他错误: {error_summary.get('other_error_count', 0)}")
        print(f"  总计: {error_summary.get('total_errors', 0)}")
    
    print(f"\n📄 详细报告: centos_simulation_report.json")
    
    # 最终结论
    if conclusions["simulation_successful"]:
        print(f"\n🎉 CentOS模拟测试成功！服务器可以正常启动。")
    else:
        print(f"\n⚠️ CentOS模拟测试发现问题，需要进一步修复。")

if __name__ == "__main__":
    main()
