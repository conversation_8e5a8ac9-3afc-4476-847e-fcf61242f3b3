#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CentOS游戏服务器项目清理和优化工具
专门用于清理临时文件并优化服务器环境配置
"""

import os
import sys
import shutil
import json
import time
import subprocess
from pathlib import Path
from datetime import datetime
import logging

class CentOSServerOptimizer:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root).resolve()
        self.centos_server_path = self.project_root / "centos7.6_game_server"
        self.backup_dir = self.project_root / "backup" / f"cleanup_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.report = {
            "start_time": datetime.now().isoformat(),
            "cleaned_files": [],
            "optimized_configs": [],
            "errors": [],
            "statistics": {}
        }
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('centos_cleanup.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def create_backup(self):
        """创建重要文件备份"""
        self.logger.info("🔄 创建备份...")
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份重要配置文件
        important_files = [
            "centos7.6_game_server",
            "config",
            "start_server.sh",
            "stop_server.sh"
        ]
        
        for item in important_files:
            src_path = self.project_root / item
            if src_path.exists():
                if src_path.is_dir():
                    shutil.copytree(src_path, self.backup_dir / item, dirs_exist_ok=True)
                else:
                    shutil.copy2(src_path, self.backup_dir / item)
                self.logger.info(f"✅ 已备份: {item}")

    def clean_temporary_files(self):
        """清理临时文件和构建产物"""
        self.logger.info("🧹 清理临时文件...")
        
        # 定义需要清理的目录和文件模式
        cleanup_patterns = {
            "directories": [
                "build", "build2", "build3", "build4",
                "out", "dist", "target",
                "debug", "logs", "cachefiles",
                "backup_decompiled", "mbackup",
                ".settings", "proguard"
            ],
            "file_extensions": [
                "*.tmp", "*.log", "*.bak", "*.old",
                "*.class~", "*.java~", "*.swp",
                "*.pid", "*.lock"
            ],
            "specific_files": [
                ".classpath", ".project", "*.iml",
                "xml.tmp", "out.map"
            ]
        }
        
        cleaned_count = 0
        
        # 清理目录
        for dir_name in cleanup_patterns["directories"]:
            for dir_path in self.project_root.rglob(dir_name):
                if dir_path.is_dir() and not self._is_protected_path(dir_path):
                    try:
                        shutil.rmtree(dir_path)
                        self.report["cleaned_files"].append(str(dir_path))
                        cleaned_count += 1
                        self.logger.info(f"🗑️  删除目录: {dir_path}")
                    except Exception as e:
                        self.report["errors"].append(f"删除目录失败 {dir_path}: {e}")
        
        # 清理文件
        for pattern in cleanup_patterns["file_extensions"] + cleanup_patterns["specific_files"]:
            for file_path in self.project_root.rglob(pattern):
                if file_path.is_file() and not self._is_protected_path(file_path):
                    try:
                        file_path.unlink()
                        self.report["cleaned_files"].append(str(file_path))
                        cleaned_count += 1
                        self.logger.info(f"🗑️  删除文件: {file_path}")
                    except Exception as e:
                        self.report["errors"].append(f"删除文件失败 {file_path}: {e}")
        
        self.report["statistics"]["cleaned_files_count"] = cleaned_count
        self.logger.info(f"✅ 清理完成，共删除 {cleaned_count} 个文件/目录")

    def _is_protected_path(self, path):
        """检查路径是否受保护"""
        protected_patterns = [
            "centos7.6_game_server",
            "src/main",
            "config/production",
            ".git",
            "backup/emergency_fix"
        ]
        
        path_str = str(path)
        return any(pattern in path_str for pattern in protected_patterns)

    def optimize_log_configuration(self):
        """优化日志配置"""
        self.logger.info("📝 优化日志配置...")
        
        # 优化log4j配置
        log4j_configs = list(self.project_root.rglob("log4j*.properties"))
        
        for config_file in log4j_configs:
            try:
                self._optimize_log4j_config(config_file)
                self.report["optimized_configs"].append(str(config_file))
            except Exception as e:
                self.report["errors"].append(f"优化日志配置失败 {config_file}: {e}")

    def _optimize_log4j_config(self, config_file):
        """优化单个log4j配置文件"""
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 生产环境优化配置
        optimizations = [
            # 调整日志级别
            ("log4j.rootLogger=DEBUG", "log4j.rootLogger=INFO"),
            ("log4j.rootLogger=TRACE", "log4j.rootLogger=INFO"),
            
            # 优化日志文件路径
            ("logs/", "/var/log/gsxdb/"),
            
            # 添加日志轮转
            ("DailyRollingFileAppender", "DailyRollingFileAppender\nlog4j.appender.FILE.MaxFileSize=100MB\nlog4j.appender.FILE.MaxBackupIndex=10")
        ]
        
        modified = False
        for old, new in optimizations:
            if old in content:
                content = content.replace(old, new)
                modified = True
        
        if modified:
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(content)
            self.logger.info(f"✅ 优化日志配置: {config_file}")

    def optimize_startup_scripts(self):
        """优化启动脚本"""
        self.logger.info("🚀 优化启动脚本...")
        
        # 查找启动脚本
        script_files = [
            "start_server.sh",
            "stop_server.sh",
            "centos7.6_game_server/bin/start.sh"
        ]
        
        for script_name in script_files:
            script_path = self.project_root / script_name
            if script_path.exists():
                self._optimize_startup_script(script_path)

    def _optimize_startup_script(self, script_path):
        """优化单个启动脚本"""
        try:
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # CentOS环境优化
            optimizations = [
                # JVM参数优化
                ("-Xmx1024m", "-Xmx2048m"),
                ("-Xms512m", "-Xms1024m"),
                
                # 添加GC优化
                ("java ", "java -XX:+UseG1GC -XX:MaxGCPauseMillis=200 "),
                
                # 添加错误处理
                ("#!/bin/bash", "#!/bin/bash\nset -e\nset -u"),
                
                # 优化路径
                ("./", "/opt/gsxdb/")
            ]
            
            modified = False
            for old, new in optimizations:
                if old in content and new not in content:
                    content = content.replace(old, new)
                    modified = True
            
            if modified:
                with open(script_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                # 设置执行权限
                os.chmod(script_path, 0o755)
                self.report["optimized_configs"].append(str(script_path))
                self.logger.info(f"✅ 优化启动脚本: {script_path}")
                
        except Exception as e:
            self.report["errors"].append(f"优化启动脚本失败 {script_path}: {e}")

    def create_centos_service_config(self):
        """创建CentOS系统服务配置"""
        self.logger.info("⚙️  创建系统服务配置...")
        
        service_config = """[Unit]
Description=GSXDB Game Server
After=network.target

[Service]
Type=forking
User=gsxdb
Group=gsxdb
WorkingDirectory=/opt/gsxdb
ExecStart=/opt/gsxdb/start_server.sh
ExecStop=/opt/gsxdb/stop_server.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
        
        service_file = self.centos_server_path / "gsxdb.service"
        with open(service_file, 'w', encoding='utf-8') as f:
            f.write(service_config)
        
        self.report["optimized_configs"].append(str(service_file))
        self.logger.info("✅ 创建系统服务配置文件")

    def optimize_centos_server_structure(self):
        """优化CentOS服务器目录结构"""
        self.logger.info("📁 优化服务器目录结构...")
        
        # 确保centos7.6_game_server目录存在
        if not self.centos_server_path.exists():
            self.centos_server_path.mkdir(parents=True)
        
        # 创建标准目录结构
        standard_dirs = [
            "bin", "config", "lib", "logs", "data", "backup"
        ]
        
        for dir_name in standard_dirs:
            dir_path = self.centos_server_path / dir_name
            dir_path.mkdir(exist_ok=True)
            self.logger.info(f"📁 确保目录存在: {dir_path}")

    def generate_deployment_guide(self):
        """生成部署指南"""
        self.logger.info("📖 生成部署指南...")
        
        guide_content = """# CentOS 7.6 游戏服务器部署指南

## 系统要求
- CentOS 7.6 或更高版本
- Java 8 或更高版本
- 至少 4GB RAM
- 至少 20GB 磁盘空间

## 部署步骤

### 1. 创建用户和目录
```bash
sudo useradd -m gsxdb
sudo mkdir -p /opt/gsxdb
sudo chown gsxdb:gsxdb /opt/gsxdb
```

### 2. 复制文件
```bash
sudo cp -r centos7.6_game_server/* /opt/gsxdb/
sudo chown -R gsxdb:gsxdb /opt/gsxdb
```

### 3. 安装系统服务
```bash
sudo cp /opt/gsxdb/gsxdb.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable gsxdb
```

### 4. 启动服务
```bash
sudo systemctl start gsxdb
sudo systemctl status gsxdb
```

## 监控和维护
- 日志位置: /var/log/gsxdb/
- 配置文件: /opt/gsxdb/config/
- 数据目录: /opt/gsxdb/data/

## 故障排除
1. 检查日志文件
2. 验证Java版本
3. 检查端口占用
4. 验证文件权限
"""
        
        guide_file = self.centos_server_path / "DEPLOYMENT_GUIDE.md"
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        self.logger.info("✅ 生成部署指南")

    def run_optimization(self):
        """运行完整的优化流程"""
        self.logger.info("🚀 开始CentOS服务器项目清理和优化...")
        
        try:
            # 第一阶段：备份
            self.create_backup()
            
            # 第二阶段：清理
            self.clean_temporary_files()
            
            # 第三阶段：优化配置
            self.optimize_log_configuration()
            self.optimize_startup_scripts()
            
            # 第四阶段：CentOS适配
            self.create_centos_service_config()
            self.optimize_centos_server_structure()
            self.generate_deployment_guide()
            
            # 生成报告
            self.generate_final_report()
            
            self.logger.info("✅ 优化完成！")
            
        except Exception as e:
            self.logger.error(f"❌ 优化过程中出现错误: {e}")
            self.report["errors"].append(str(e))
            raise

    def generate_final_report(self):
        """生成最终报告"""
        self.report["end_time"] = datetime.now().isoformat()
        self.report["duration"] = str(datetime.fromisoformat(self.report["end_time"]) - 
                                     datetime.fromisoformat(self.report["start_time"]))
        
        report_file = self.project_root / "centos_optimization_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📊 优化报告已保存: {report_file}")

if __name__ == "__main__":
    optimizer = CentOSServerOptimizer()
    optimizer.run_optimization()
