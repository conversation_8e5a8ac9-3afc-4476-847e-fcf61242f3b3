#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复反编译文件
Batch Fix Decompiled Files
=========================

基于原始代码参考，批量修复反编译文件中的var1, var2, var3等变量名问题
Based on original code reference, batch fix var1, var2, var3 variable name issues in decompiled files
"""

import os
import shutil
from pathlib import Path
from datetime import datetime
import json

class BatchDecompiledFixer:
    """批量反编译文件修复器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.src_dir = self.project_root / "src"
        self.original_dir = self.project_root / "solutions" / "gsxdb原始java代码参考"
        self.backup_dir = self.project_root / "backup" / "decompiled_fix"
    
    def batch_fix(self):
        """批量修复反编译文件"""
        print("🔧 批量修复反编译文件...")
        
        fix_result = {
            "timestamp": datetime.now().isoformat(),
            "files_processed": [],
            "files_replaced": [],
            "files_skipped": [],
            "errors": [],
            "success": False
        }
        
        try:
            # 1. 创建备份目录
            self._create_backup_dir()
            
            # 2. 查找需要修复的文件
            files_to_fix = self._find_files_to_fix()
            
            # 3. 批量替换文件
            self._batch_replace_files(files_to_fix, fix_result)
            
            # 4. 生成修复报告
            self._generate_fix_report(fix_result)
            
            fix_result["success"] = True
            print("✅ 批量修复完成")
            
        except Exception as e:
            fix_result["errors"].append(str(e))
            print(f"❌ 批量修复失败: {str(e)}")
        
        return fix_result
    
    def _create_backup_dir(self):
        """创建备份目录"""
        print("  📁 创建备份目录...")
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        print(f"    ✓ 备份目录: {self.backup_dir}")
    
    def _find_files_to_fix(self):
        """查找需要修复的文件"""
        print("  🔍 查找需要修复的文件...")
        
        files_to_fix = []
        
        # 遍历原始代码目录，找到对应的反编译文件
        for original_file in self.original_dir.rglob("*.java"):
            # 计算相对路径
            relative_path = original_file.relative_to(self.original_dir)
            
            # 检查src目录中是否存在对应文件
            src_file = self.src_dir / relative_path
            
            if src_file.exists():
                # 检查是否包含反编译变量名
                if self._contains_decompiled_vars(src_file):
                    files_to_fix.append({
                        "original": original_file,
                        "target": src_file,
                        "relative_path": str(relative_path)
                    })
        
        print(f"    ✓ 发现需要修复的文件: {len(files_to_fix)} 个")
        return files_to_fix
    
    def _contains_decompiled_vars(self, file_path: Path) -> bool:
        """检查文件是否包含反编译变量名"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查是否包含var1, var2, var3等模式
            import re
            var_pattern = r'\bvar\d+\b'
            return bool(re.search(var_pattern, content))
            
        except Exception:
            return False
    
    def _batch_replace_files(self, files_to_fix, result):
        """批量替换文件"""
        print("  🔄 批量替换文件...")
        
        for file_info in files_to_fix:
            try:
                original_file = file_info["original"]
                target_file = file_info["target"]
                relative_path = file_info["relative_path"]
                
                print(f"    🔧 修复: {relative_path}")
                
                # 备份原文件
                backup_file = self.backup_dir / relative_path
                backup_file.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(target_file, backup_file)
                
                # 替换为原始文件
                shutil.copy2(original_file, target_file)
                
                result["files_replaced"].append(relative_path)
                print(f"      ✓ 已替换")
                
            except Exception as e:
                error_msg = f"替换文件失败 {file_info['relative_path']}: {str(e)}"
                result["errors"].append(error_msg)
                print(f"      ❌ {error_msg}")
        
        result["files_processed"] = len(files_to_fix)
    
    def _generate_fix_report(self, result):
        """生成修复报告"""
        print("  📊 生成修复报告...")
        
        report_file = self.project_root / "batch_decompiled_fix_report.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"    ✓ 修复报告: {report_file}")
    
    def get_priority_files(self):
        """获取优先修复的文件列表"""
        priority_files = [
            # JavaScript引擎相关
            "fire/script/IJavaScriptEngine.java",
            "fire/script/AbstractJSEngine.java", 
            "fire/script/FightJSEngine.java",
            "fire/script/SceneJSEngine.java",
            
            # 战斗系统相关
            "fire/pb/battle/Fighter.java",
            "fire/pb/battle/BattleFieldFighter.java",
            "fire/pb/battle/Monster.java",
            "fire/pb/battle/BattleField.java",
            "fire/pb/battle/PNewBattle.java",
            
            # 技能系统相关
            "fire/pb/skill/particleskill/CRequestLearnParticleSkill.java",
            
            # 核心模块
            "fire/pb/main/Gs.java",
            "fire/pb/main/Module.java",
            
            # 日志系统
            "fire/log/YYLogger.java",
            
            # 宠物系统
            "fire/pb/pet/Pet.java",
            
            # 物品系统
            "fire/pb/item/ItemBase.java",
            
            # 角色系统
            "fire/pb/role/Role.java",
            "fire/pb/role/RoleImpl.java"
        ]
        
        return priority_files
    
    def fix_priority_files_only(self):
        """只修复优先文件"""
        print("🎯 修复优先文件...")
        
        fix_result = {
            "timestamp": datetime.now().isoformat(),
            "priority_files": [],
            "files_replaced": [],
            "files_skipped": [],
            "errors": [],
            "success": False
        }
        
        try:
            # 创建备份目录
            self._create_backup_dir()
            
            # 获取优先文件列表
            priority_files = self.get_priority_files()
            
            for relative_path in priority_files:
                try:
                    original_file = self.original_dir / relative_path
                    target_file = self.src_dir / relative_path
                    
                    if original_file.exists() and target_file.exists():
                        # 检查是否需要修复
                        if self._contains_decompiled_vars(target_file):
                            print(f"  🔧 修复优先文件: {relative_path}")
                            
                            # 备份原文件
                            backup_file = self.backup_dir / relative_path
                            backup_file.parent.mkdir(parents=True, exist_ok=True)
                            shutil.copy2(target_file, backup_file)
                            
                            # 替换为原始文件
                            shutil.copy2(original_file, target_file)
                            
                            fix_result["files_replaced"].append(relative_path)
                            print(f"    ✓ 已替换")
                        else:
                            fix_result["files_skipped"].append(f"{relative_path} (无需修复)")
                            print(f"    ℹ️ 跳过: {relative_path} (无需修复)")
                    else:
                        if not original_file.exists():
                            fix_result["files_skipped"].append(f"{relative_path} (原始文件不存在)")
                        if not target_file.exists():
                            fix_result["files_skipped"].append(f"{relative_path} (目标文件不存在)")
                        
                except Exception as e:
                    error_msg = f"修复优先文件失败 {relative_path}: {str(e)}"
                    fix_result["errors"].append(error_msg)
                    print(f"    ❌ {error_msg}")
            
            fix_result["priority_files"] = priority_files
            fix_result["success"] = True
            print("✅ 优先文件修复完成")
            
        except Exception as e:
            fix_result["errors"].append(str(e))
            print(f"❌ 优先文件修复失败: {str(e)}")
        
        return fix_result

def main():
    """主函数"""
    fixer = BatchDecompiledFixer()
    
    print("🔧 批量反编译文件修复器")
    print("=" * 50)
    
    # 选择修复模式
    print("请选择修复模式:")
    print("1. 只修复优先文件 (推荐)")
    print("2. 修复所有文件")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        # 修复优先文件
        result = fixer.fix_priority_files_only()
    else:
        # 修复所有文件
        result = fixer.batch_fix()
    
    print("\n" + "=" * 50)
    print("📊 修复结果总结")
    print("=" * 50)
    
    if result["success"]:
        print("✅ 修复成功!")
        print(f"📄 已替换文件: {len(result['files_replaced'])} 个")
        print(f"⏭️ 跳过文件: {len(result['files_skipped'])} 个")
        
        if result["files_replaced"]:
            print(f"\n🔧 已替换的文件:")
            for file_path in result["files_replaced"]:
                print(f"  ✓ {file_path}")
        
        if result["errors"]:
            print(f"\n⚠️ 错误信息:")
            for error in result["errors"]:
                print(f"  ❌ {error}")
        
        print(f"\n🎯 下一步:")
        print(f"  1. 在IDEA中重新编译修复后的文件")
        print(f"  2. 更新jar包")
        print(f"  3. 重启服务器测试")
        
    else:
        print("❌ 修复失败")
        if result["errors"]:
            for error in result["errors"]:
                print(f"  错误: {error}")

if __name__ == "__main__":
    main()
