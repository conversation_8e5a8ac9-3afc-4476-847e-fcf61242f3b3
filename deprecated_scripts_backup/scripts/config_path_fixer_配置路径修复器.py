#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置路径修复器
Config Path Fixer
================

解决NullPointerException配置文件路径问题
Fix NullPointerException config file path issues
"""

import os
import shutil
from pathlib import Path
from datetime import datetime

class ConfigPathFixer:
    """配置路径修复器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.centos_sim = self.project_root / "centos_simulation" / "home" / "game" / "server1" / "game_server"
    
    def fix_config_paths(self):
        """修复配置路径问题"""
        print("🔧 修复配置文件路径问题...")
        
        fix_result = {
            "timestamp": datetime.now().isoformat(),
            "fixes_applied": [],
            "directories_created": [],
            "files_copied": [],
            "success": False
        }
        
        try:
            # 1. 创建gamedata目录结构
            self._create_gamedata_structure(fix_result)
            
            # 2. 创建基本配置文件
            self._create_basic_configs(fix_result)
            
            # 3. 创建log4j2配置
            self._create_log4j2_config(fix_result)
            
            # 4. 复制现有的gamedata文件
            self._copy_existing_gamedata(fix_result)
            
            fix_result["success"] = True
            print("✅ 配置路径修复完成")
            
        except Exception as e:
            fix_result["error"] = str(e)
            print(f"❌ 配置修复失败: {str(e)}")
        
        return fix_result
    
    def _create_gamedata_structure(self, result):
        """创建gamedata目录结构"""
        print("  📁 创建gamedata目录结构...")
        
        directories = [
            "gamedata",
            "gamedata/xml",
            "gamedata/xml/auto",
            "gamedata/config",
            "gamedata/properties",
            "logs",
            "conf"
        ]
        
        for directory in directories:
            dir_path = self.centos_sim / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            result["directories_created"].append(str(dir_path))
            print(f"    ✓ {directory}")
    
    def _create_basic_configs(self, result):
        """创建基本配置文件"""
        print("  📄 创建基本配置文件...")
        
        # 创建server.properties
        server_props = self.centos_sim / "gamedata" / "properties" / "server.properties"
        server_content = """# Game Server Configuration
# 游戏服务器配置

# Server Basic Settings
server.id=1
server.name=TestServer
server.port=8080

# Database Settings  
db.host=localhost
db.port=3306
db.name=gamedb
db.user=game
db.password=game123

# Game Settings
game.max_players=1000
game.debug_mode=true

# Paths
gamedata.path=/home/<USER>/server1/game_server/gamedata
xml.path=/home/<USER>/server1/game_server/gamedata/xml
auto.xml.path=/home/<USER>/server1/game_server/gamedata/xml/auto
"""
        
        with open(server_props, 'w', encoding='utf-8') as f:
            f.write(server_content)
        
        result["files_copied"].append(str(server_props))
        print(f"    ✓ server.properties")
        
        # 创建game.properties
        game_props = self.centos_sim / "gamedata" / "properties" / "game.properties"
        game_content = """# Game Configuration
# 游戏配置

# JavaScript Engine Settings
js.engine.debug=true
js.engine.fix_enabled=true
js.script.timeout=5000

# Battle System
battle.debug=true
battle.timeout=30000

# Skill System
skill.debug=true
skill.cache_enabled=true
"""
        
        with open(game_props, 'w', encoding='utf-8') as f:
            f.write(game_content)
        
        result["files_copied"].append(str(game_props))
        print(f"    ✓ game.properties")
    
    def _create_log4j2_config(self, result):
        """创建log4j2配置"""
        print("  📝 创建log4j2配置...")
        
        log4j2_xml = self.centos_sim / "log4j2.xml"
        log4j2_content = '''<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Appenders>
        <!-- Console Appender -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level %logger{36}.%M(%L) - %msg%n"/>
        </Console>
        
        <!-- File Appender -->
        <File name="FileAppender" fileName="logs/gs.log">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level %logger{36}.%M(%L) - %msg%n"/>
        </File>
        
        <!-- Rolling File Appender -->
        <RollingFile name="RollingFileAppender" fileName="logs/gs.log" filePattern="logs/gs-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} [%t] %-5level %logger{36}.%M(%L) - %msg%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
    </Appenders>
    
    <Loggers>
        <!-- Game Server Loggers -->
        <Logger name="fire.pb" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFileAppender"/>
        </Logger>
        
        <Logger name="SYSTEM" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFileAppender"/>
        </Logger>
        
        <Logger name="BATTLE" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFileAppender"/>
        </Logger>
        
        <!-- Root Logger -->
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFileAppender"/>
        </Root>
    </Loggers>
</Configuration>'''
        
        with open(log4j2_xml, 'w', encoding='utf-8') as f:
            f.write(log4j2_content)
        
        result["files_copied"].append(str(log4j2_xml))
        print(f"    ✓ log4j2.xml")
    
    def _copy_existing_gamedata(self, result):
        """复制现有的gamedata文件"""
        print("  📋 复制现有gamedata文件...")
        
        # 检查原项目中是否有gamedata目录
        original_gamedata = self.project_root / "gamedata"
        if original_gamedata.exists():
            target_gamedata = self.centos_sim / "gamedata"
            
            # 复制xml文件
            if (original_gamedata / "xml").exists():
                for xml_file in (original_gamedata / "xml").rglob("*.xml"):
                    relative_path = xml_file.relative_to(original_gamedata)
                    target_file = target_gamedata / relative_path
                    target_file.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(xml_file, target_file)
                    result["files_copied"].append(str(target_file))
                    print(f"    ✓ {relative_path}")
        
        # 创建一些基本的XML配置文件（如果不存在）
        self._create_basic_xml_configs(result)
    
    def _create_basic_xml_configs(self, result):
        """创建基本的XML配置文件"""
        print("  📄 创建基本XML配置...")
        
        # 创建基本的配置XML
        basic_configs = [
            ("SCommon.xml", self._get_scommon_xml()),
            ("ModuleInfo.xml", self._get_moduleinfo_xml()),
            ("HotfixConfig.xml", self._get_hotfix_xml())
        ]
        
        auto_xml_dir = self.centos_sim / "gamedata" / "xml" / "auto"
        
        for filename, content in basic_configs:
            xml_file = auto_xml_dir / filename
            with open(xml_file, 'w', encoding='utf-8') as f:
                f.write(content)
            result["files_copied"].append(str(xml_file))
            print(f"    ✓ {filename}")
    
    def _get_scommon_xml(self):
        """获取SCommon.xml内容"""
        return '''<?xml version="1.0" encoding="UTF-8"?>
<tree-map>
  <entry>
    <int>1</int>
    <fire.pb.common.SCommon>
      <id>1</id>
      <name>TestConfig</name>
      <value>1</value>
    </fire.pb.common.SCommon>
  </entry>
</tree-map>'''
    
    def _get_moduleinfo_xml(self):
        """获取ModuleInfo.xml内容"""
        return '''<?xml version="1.0" encoding="UTF-8"?>
<tree-map>
  <entry>
    <int>1</int>
    <fire.pb.main.ModuleInfo>
      <id>1</id>
      <name>TestModule</name>
      <enabled>true</enabled>
    </fire.pb.main.ModuleInfo>
  </entry>
</tree-map>'''
    
    def _get_hotfix_xml(self):
        """获取HotfixConfig.xml内容"""
        return '''<?xml version="1.0" encoding="UTF-8"?>
<tree-map>
  <entry>
    <int>1</int>
    <fire.pb.main.HotfixConfig>
      <id>1</id>
      <name>JavaScriptEngineFix</name>
      <enabled>true</enabled>
      <description>JavaScript引擎修复</description>
    </fire.pb.main.HotfixConfig>
  </entry>
</tree-map>'''

def main():
    """主函数"""
    fixer = ConfigPathFixer()
    
    print("🔧 配置路径修复器")
    print("=" * 40)
    
    # 执行修复
    result = fixer.fix_config_paths()
    
    print("\n" + "=" * 40)
    print("📊 修复结果总结")
    print("=" * 40)
    
    if result["success"]:
        print("✅ 配置路径修复成功!")
        print(f"📁 创建目录: {len(result['directories_created'])} 个")
        print(f"📄 创建文件: {len(result['files_copied'])} 个")
        
        print(f"\n🎯 主要修复:")
        print(f"  ✅ gamedata目录结构")
        print(f"  ✅ log4j2.xml配置")
        print(f"  ✅ server.properties配置")
        print(f"  ✅ 基本XML配置文件")
        
        print(f"\n🚀 下一步:")
        print(f"  1. 重新运行CentOS模拟器")
        print(f"  2. 验证NullPointerException是否解决")
        print(f"  3. 测试服务器完整启动")
    else:
        print("❌ 配置路径修复失败")
        if "error" in result:
            print(f"错误: {result['error']}")

if __name__ == "__main__":
    main()
