#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目清理和问题修复脚本
Project Cleanup and Issue Fix Script
===================================

清理项目中的临时文件并修复发现的问题
Clean up temporary files and fix discovered issues
"""

import os
import shutil
import subprocess
import json
from pathlib import Path
from datetime import datetime

class ProjectCleanupAndFixer:
    """项目清理和修复器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()

    def cleanup_and_fix(self):
        """清理和修复项目"""
        print("🧹 开始项目清理和问题修复...")
        
        cleanup_result = {
            "timestamp": datetime.now().isoformat(),
            "cleanup_steps": [],
            "files_cleaned": [],
            "issues_fixed": [],
            "compilation_results": {},
            "cleanup_success": False
        }
        
        try:
            # 步骤1: 清理临时文件
            self._cleanup_temporary_files(cleanup_result)
            
            # 步骤2: 修复EffectRole类缺失问题
            self._fix_effect_role_missing(cleanup_result)
            
            # 步骤3: 清理和重新编译
            self._clean_and_recompile(cleanup_result)
            
            # 步骤4: 验证修复结果
            self._verify_fixes(cleanup_result)
            
            # 步骤5: 生成清理报告
            self._generate_cleanup_report(cleanup_result)
            
            cleanup_result["cleanup_success"] = True
            print("✅ 项目清理和修复完成!")
            
        except Exception as e:
            error_msg = f"清理修复过程中发生错误: {str(e)}"
            print(f"❌ {error_msg}")
            cleanup_result["error"] = error_msg
        
        return cleanup_result

    def _cleanup_temporary_files(self, result: dict):
        """清理临时文件"""
        print("🗑️ 清理临时文件...")
        
        # 定义需要清理的临时文件和目录
        temp_patterns = [
            # 编译临时文件
            "**/*.class~",
            "**/*.java~",
            "**/*.bak",
            "**/*.tmp",
            
            # IDE临时文件
            "**/.idea/workspace.xml",
            "**/.vscode/settings.json",
            "**/nbproject/private/",
            
            # 系统临时文件
            "**/Thumbs.db",
            "**/.DS_Store",
            "**/desktop.ini",
            
            # 日志备份文件
            "*.log.bak",
            "*.log.old",
            
            # 测试输出文件
            "**/test_output_*.txt",
            "**/verification_*.json"
        ]
        
        cleaned_files = []
        
        for pattern in temp_patterns:
            try:
                for file_path in self.project_root.glob(pattern):
                    if file_path.is_file():
                        file_path.unlink()
                        cleaned_files.append(str(file_path.relative_to(self.project_root)))
                    elif file_path.is_dir():
                        shutil.rmtree(file_path)
                        cleaned_files.append(str(file_path.relative_to(self.project_root)) + "/")
            except Exception as e:
                print(f"  ⚠️ 清理 {pattern} 时出错: {str(e)}")
        
        # 清理空目录
        self._remove_empty_directories()
        
        result["files_cleaned"] = cleaned_files
        result["cleanup_steps"].append("临时文件清理")
        print(f"  ✓ 清理了 {len(cleaned_files)} 个临时文件/目录")

    def _remove_empty_directories(self):
        """移除空目录"""
        for root, dirs, files in os.walk(self.project_root, topdown=False):
            for dir_name in dirs:
                dir_path = Path(root) / dir_name
                try:
                    if dir_path.is_dir() and not any(dir_path.iterdir()):
                        dir_path.rmdir()
                except OSError:
                    pass  # 目录不为空或无法删除

    def _fix_effect_role_missing(self, result: dict):
        """修复EffectRole类缺失问题"""
        print("🔧 修复EffectRole类缺失问题...")
        
        effect_role_file = self.project_root / "src" / "fire" / "pb" / "role" / "EffectRole.java"
        
        if not effect_role_file.exists():
            print("  📝 创建缺失的EffectRole类...")
            
            # 确保目录存在
            effect_role_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 创建EffectRole类
            effect_role_content = '''package fire.pb.role;

/**
 * 角色效果接口
 * Role Effect Interface
 * 
 * 用于处理角色的属性和效果
 */
public interface EffectRole {
    
    /**
     * 获取角色HP
     */
    float getHp();
    
    /**
     * 设置角色HP
     */
    void setHp(float hp);
    
    /**
     * 获取角色MP
     */
    float getMp();
    
    /**
     * 设置角色MP
     */
    void setMp(float mp);
    
    /**
     * 获取角色SP
     */
    float getSp();
    
    /**
     * 设置角色SP
     */
    void setSp(float sp);
    
    /**
     * 根据属性ID获取属性值
     */
    float getAttrById(int attrId);
    
    /**
     * 根据属性ID设置属性值
     */
    void setAttrById(int attrId, float value);
    
    /**
     * 获取角色等级
     */
    int getLevel();
    
    /**
     * 获取角色品质
     */
    int getGrade();
    
    /**
     * 获取最大HP
     */
    float getMaxHp();
    
    /**
     * 获取最大MP
     */
    float getMaxMp();
    
    /**
     * 获取最大SP
     */
    float getMaxSp();
    
    /**
     * 获取物理攻击力
     */
    float getPhyAttack();
    
    /**
     * 获取魔法攻击力
     */
    float getMagicAttack();
    
    /**
     * 获取物理防御力
     */
    float getDefend();
    
    /**
     * 获取魔法防御力
     */
    float getMagicDef();
    
    /**
     * 获取速度
     */
    float getSpeed();
}'''
            
            with open(effect_role_file, 'w', encoding='utf-8') as f:
                f.write(effect_role_content)
            
            result["issues_fixed"].append("创建EffectRole接口")
            print("  ✓ EffectRole接口已创建")
        else:
            print("  ✓ EffectRole类已存在")
        
        result["cleanup_steps"].append("EffectRole修复")

    def _clean_and_recompile(self, result: dict):
        """清理和重新编译"""
        print("🔨 清理和重新编译...")
        
        # 清理编译输出
        out_dir = self.project_root / "out"
        if out_dir.exists():
            print("  🗑️ 清理编译输出目录...")
            shutil.rmtree(out_dir)
        
        # 重新创建目录结构
        (out_dir / "production" / "gsxdb").mkdir(parents=True, exist_ok=True)
        (out_dir / "test" / "gsxdb").mkdir(parents=True, exist_ok=True)
        
        # 编译关键文件
        key_files = [
            "src/fire/pb/role/EffectRole.java",
            "src/fire/script/IJavaScriptEngine.java",
            "src/fire/script/AbstractJSEngine.java",
            "src/fire/script/SceneJSEngine.java",
            "src/fire/script/FightJSEngine.java",
            "src/fire/script/JavaScriptEngineFix.java"
        ]
        
        compilation_results = {}
        
        for java_file in key_files:
            file_path = self.project_root / java_file
            if file_path.exists():
                try:
                    cmd = [
                        "javac",
                        "-cp", "lib/*;gs_lib/*;lib2/*;libsys/*",
                        "-encoding", "UTF-8",
                        "-sourcepath", "src",
                        "-d", "out/production/gsxdb",
                        java_file
                    ]
                    
                    process = subprocess.run(
                        cmd,
                        cwd=self.project_root,
                        capture_output=True,
                        text=True,
                        timeout=60
                    )
                    
                    file_name = Path(java_file).name
                    compilation_results[file_name] = {
                        "success": process.returncode == 0,
                        "return_code": process.returncode,
                        "stdout": process.stdout[:200],
                        "stderr": process.stderr[:200]
                    }
                    
                    if process.returncode == 0:
                        print(f"  ✅ 编译成功: {file_name}")
                    else:
                        print(f"  ❌ 编译失败: {file_name}")
                        
                except Exception as e:
                    compilation_results[Path(java_file).name] = {
                        "success": False,
                        "error": str(e)
                    }
                    print(f"  ❌ 编译异常: {java_file}")
        
        result["compilation_results"] = compilation_results
        result["cleanup_steps"].append("重新编译")

    def _verify_fixes(self, result: dict):
        """验证修复结果"""
        print("✅ 验证修复结果...")
        
        verification_results = {}
        
        # 验证EffectRole类
        effect_role_class = self.project_root / "out" / "production" / "gsxdb" / "fire" / "pb" / "role" / "EffectRole.class"
        verification_results["EffectRole"] = {
            "class_exists": effect_role_class.exists(),
            "size": effect_role_class.stat().st_size if effect_role_class.exists() else 0
        }
        
        # 验证JavaScript引擎类
        js_classes = [
            "IJavaScriptEngine.class",
            "AbstractJSEngine.class", 
            "SceneJSEngine.class",
            "FightJSEngine.class",
            "JavaScriptEngineFix.class"
        ]
        
        for class_name in js_classes:
            class_path = self.project_root / "out" / "production" / "gsxdb" / "fire" / "script" / class_name
            verification_results[class_name] = {
                "exists": class_path.exists(),
                "size": class_path.stat().st_size if class_path.exists() else 0
            }
        
        result["verification_results"] = verification_results
        result["cleanup_steps"].append("修复验证")
        
        # 统计验证结果
        total_classes = len(verification_results)
        existing_classes = sum(1 for v in verification_results.values() if v.get("exists", False) or v.get("class_exists", False))
        
        print(f"  📊 类文件验证: {existing_classes}/{total_classes} 个类存在")

    def _generate_cleanup_report(self, result: dict):
        """生成清理报告"""
        print("📄 生成清理报告...")
        
        report_file = self.project_root / "project_cleanup_report.json"
        
        # 添加清理总结
        result["cleanup_summary"] = {
            "cleanup_date": datetime.now().isoformat(),
            "total_steps": len(result["cleanup_steps"]),
            "files_cleaned": len(result["files_cleaned"]),
            "issues_fixed": len(result["issues_fixed"]),
            "compilation_success_rate": self._calculate_compilation_success_rate(result)
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"  ✓ 清理报告已保存: {report_file}")

    def _calculate_compilation_success_rate(self, result: dict) -> float:
        """计算编译成功率"""
        compilation_results = result.get("compilation_results", {})
        if not compilation_results:
            return 0.0
        
        successful = sum(1 for r in compilation_results.values() if r.get("success", False))
        total = len(compilation_results)
        
        return round((successful / total) * 100, 2) if total > 0 else 0.0

def main():
    """主函数"""
    cleaner = ProjectCleanupAndFixer()
    
    print("🧹 项目清理和问题修复工具")
    print("=" * 50)
    
    # 执行清理和修复
    result = cleaner.cleanup_and_fix()
    
    print("\n" + "=" * 50)
    print("📊 项目清理和修复总结")
    print("=" * 50)
    
    # 显示清理结果
    cleanup_success = result.get("cleanup_success", False)
    cleanup_summary = result.get("cleanup_summary", {})
    
    print(f"📈 清理统计:")
    print(f"  ✅ 完成步骤: {cleanup_summary.get('total_steps', 0)} 个")
    print(f"  🗑️ 清理文件: {cleanup_summary.get('files_cleaned', 0)} 个")
    print(f"  🔧 修复问题: {cleanup_summary.get('issues_fixed', 0)} 个")
    print(f"  🔨 编译成功率: {cleanup_summary.get('compilation_success_rate', 0)}%")
    print(f"  🎯 清理状态: {'成功' if cleanup_success else '失败'}")
    
    # 显示修复的问题
    issues_fixed = result.get("issues_fixed", [])
    if issues_fixed:
        print(f"\n🔧 修复的问题:")
        for issue in issues_fixed:
            print(f"  ✅ {issue}")
    
    # 显示编译结果
    compilation_results = result.get("compilation_results", {})
    if compilation_results:
        print(f"\n🔨 编译结果:")
        for file_name, comp_result in compilation_results.items():
            status = "✅ 成功" if comp_result.get("success", False) else "❌ 失败"
            print(f"  {status}: {file_name}")
    
    # 显示验证结果
    verification_results = result.get("verification_results", {})
    if verification_results:
        print(f"\n✅ 验证结果:")
        for class_name, verify_result in verification_results.items():
            exists = verify_result.get("exists", False) or verify_result.get("class_exists", False)
            size = verify_result.get("size", 0)
            status = "✅ 存在" if exists else "❌ 缺失"
            print(f"  {status}: {class_name} ({size} bytes)")
    
    # 显示最终结论
    if cleanup_success:
        print(f"\n🎉 项目清理和修复成功!")
        print(f"\n💡 主要改进:")
        print(f"  🗑️ 清理了临时文件和无用文件")
        print(f"  🔧 修复了EffectRole类缺失问题")
        print(f"  🔨 重新编译了关键组件")
        print(f"  ✅ 验证了修复效果")
        print(f"\n🚀 下一步:")
        print(f"  1. 重启游戏服务器")
        print(f"  2. 测试战斗系统功能")
        print(f"  3. 监控错误日志变化")
    else:
        print(f"\n⚠️ 清理修复过程中遇到问题，请检查错误信息")
    
    print(f"\n📄 详细报告: project_cleanup_report.json")

if __name__ == "__main__":
    main()
