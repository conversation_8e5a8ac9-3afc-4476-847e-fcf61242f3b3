#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急修复类型兼容性问题
Emergency Fix for Type Compatibility Issues
==========================================

修复FightJSEngine类型不兼容导致的VerifyError
Fix VerifyError caused by FightJSEngine type incompatibility
"""

import os
import shutil
import subprocess
from pathlib import Path
from datetime import datetime

class EmergencyTypeFixer:
    """紧急类型修复器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.src_dir = self.project_root / "src"
        self.backup_dir = self.project_root / "backup" / "emergency_fix"

    def emergency_fix(self):
        """执行紧急修复"""
        print("🚨 执行紧急类型兼容性修复...")
        
        try:
            # 步骤1: 创建紧急备份
            self._create_emergency_backup()
            
            # 步骤2: 恢复FightJSEngine的正确继承关系
            self._fix_fightjs_engine_inheritance()
            
            # 步骤3: 确保AbstractJSEngine存在并正确
            self._ensure_abstract_js_engine()
            
            # 步骤4: 编译验证
            self._compile_and_verify()
            
            print("✅ 紧急修复完成!")
            return True
            
        except Exception as e:
            print(f"❌ 紧急修复失败: {str(e)}")
            return False

    def _create_emergency_backup(self):
        """创建紧急备份"""
        print("📦 创建紧急备份...")
        
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # 备份关键文件
        files_to_backup = [
            "src/fire/script/FightJSEngine.java",
            "src/fire/script/AbstractJSEngine.java"
        ]
        
        for file_path in files_to_backup:
            src_file = self.project_root / file_path
            if src_file.exists():
                backup_file = self.backup_dir / src_file.name
                shutil.copy2(src_file, backup_file)
                print(f"  ✓ 备份: {src_file.name}")

    def _fix_fightjs_engine_inheritance(self):
        """修复FightJSEngine继承关系"""
        print("🔧 修复FightJSEngine继承关系...")
        
        fightjs_file = self.src_dir / "fire" / "script" / "FightJSEngine.java"
        
        if not fightjs_file.exists():
            print("  ❌ FightJSEngine.java 不存在")
            return
        
        # 读取当前内容
        with open(fightjs_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复继承关系和导入
        fixed_content = self._fix_fightjs_content(content)
        
        # 写回文件
        with open(fightjs_file, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print("  ✓ FightJSEngine继承关系已修复")

    def _fix_fightjs_content(self, content: str) -> str:
        """修复FightJSEngine内容"""
        
        # 确保正确的包声明和导入
        fixed_content = '''//
//

package fire.script;

import fire.pb.battle.Fighter;
import java.util.List;

public class FightJSEngine extends AbstractJSEngine {
    
    // 简化的Battle类常量
    private static final int DEBUG_FORMULA_MODULE = 1;
    
    // 简化的Module类常量
    private static final int[] fightAttrTypeIds = {100, 110, 120, 130, 140, 150};
    private static final int[] extfightAttrTypeIds = {100, 110, 120};
    
    // 升级的JavaScript引擎支持
    private static JavaScriptEngineFix jsEngineFix;
    
    static {
        try {
            jsEngineFix = new JavaScriptEngineFix();
            System.out.println("FightJSEngine: JavaScript引擎修复工具初始化成功");
        } catch (Exception e) {
            System.err.println("FightJSEngine: JavaScript引擎修复工具初始化失败 - " + e.getMessage());
        }
    }
    
    boolean isInitrand = false;
    
    // 简化的Module类
    private static class SimpleModule {
        private static SimpleModule instance = new SimpleModule();
        
        public static SimpleModule getInstance() {
            return instance;
        }
        
        public String getEffectNameById(int effectId) {
            switch (effectId) {
                case 100: return "hp";
                case 110: return "mp";
                case 120: return "sp";
                case 130: return "attack";
                case 140: return "defense";
                case 150: return "speed";
                default: return "attr" + effectId;
            }
        }
    }

    public void setBattleType(int battleType, Fighter opfighter, int[] usedattrtypea) {
        if (battleType == 1) {
            this.isInitrand = true;
        } else {
            System.err.println("FightJSEngine.setBattleType中传入参数为无法找到的类型:" + battleType);
        }

        if (DEBUG_FORMULA_MODULE == 1) {
            for(int attrtype : usedattrtypea) {
                if (attrtype == extfightAttrTypeIds[0]) {
                    this.put("curhpa", opfighter.getEffectRole().getHp());
                } else if (attrtype == extfightAttrTypeIds[1]) {
                    this.put("curmpa", opfighter.getEffectRole().getMp());
                } else if (attrtype == extfightAttrTypeIds[2]) {
                    this.put("curspa", opfighter.getEffectRole().getSp());
                } else {
                    String name = SimpleModule.getInstance().getEffectNameById(attrtype);
                    float value = opfighter.getEffectRole().getAttrById(attrtype);
                    if (name != null) {
                        this.put(name + "a", value);
                    }
                }
            }
        }
    }

    public void setBattleType(int battleType, Fighter opfighter) {
        if (battleType == 1) {
            this.isInitrand = true;
        }

        if (DEBUG_FORMULA_MODULE == 1) {
            for(int i = 0; i < fightAttrTypeIds.length; ++i) {
                String name = SimpleModule.getInstance().getEffectNameById(fightAttrTypeIds[i]);
                float value = opfighter.getEffectRole().getAttrById(fightAttrTypeIds[i]);
                if (name != null) {
                    this.put(name + "a", value);
                }
            }

            this.put("curhpa", opfighter.getEffectRole().getHp());
            this.put("curmpa", opfighter.getEffectRole().getMp());
            this.put("curspa", opfighter.getEffectRole().getSp());
        }
    }

    public void setBattleType(int battleType, Fighter opfighter, Fighter aimfighter, int[] usedattrtypea, int[] usedattrtypeb) {
        if (battleType == 1) {
            this.isInitrand = true;
        } else {
            System.err.println("FightJSEngine.setBattleType中传入参数为无法找到的类型:" + battleType);
        }

        if (DEBUG_FORMULA_MODULE == 1) {
            for(int attrtype : usedattrtypeb) {
                if (attrtype == extfightAttrTypeIds[0]) {
                    this.put("curhpb", aimfighter.getEffectRole().getHp());
                } else if (attrtype == extfightAttrTypeIds[1]) {
                    this.put("curmpb", aimfighter.getEffectRole().getMp());
                } else if (attrtype == extfightAttrTypeIds[2]) {
                    this.put("curspb", aimfighter.getEffectRole().getSp());
                } else {
                    String name = SimpleModule.getInstance().getEffectNameById(attrtype);
                    float value = aimfighter.getEffectRole().getAttrById(attrtype);
                    if (name != null) {
                        this.put(name + "b", value);
                    }
                }
            }
        }
    }

    public void setBattleType(int battleType, Fighter opfighter, Fighter aimfighter) {
        if (battleType == 1) {
            this.isInitrand = true;
        }

        if (DEBUG_FORMULA_MODULE == 1) {
            for(int i = 0; i < fightAttrTypeIds.length; ++i) {
                String name = SimpleModule.getInstance().getEffectNameById(fightAttrTypeIds[i]);
                float value = aimfighter.getEffectRole().getAttrById(fightAttrTypeIds[i]);
                if (name != null) {
                    this.put(name + "b", value);
                }
            }

            this.put("curhpb", aimfighter.getEffectRole().getHp());
            this.put("curmpb", aimfighter.getEffectRole().getMp());
            this.put("curspb", aimfighter.getEffectRole().getSp());
        }
    }
    
    /**
     * 使用JavaScript引擎修复工具处理脚本
     */
    public String fixAndExecuteScript(String script) {
        if (jsEngineFix != null) {
            return jsEngineFix.fixScriptSyntax(script);
        }
        return script;
    }
}'''
        
        return fixed_content

    def _ensure_abstract_js_engine(self):
        """确保AbstractJSEngine存在并正确"""
        print("🔧 确保AbstractJSEngine正确...")
        
        abstract_js_file = self.src_dir / "fire" / "script" / "AbstractJSEngine.java"
        
        if not abstract_js_file.exists():
            print("  📝 创建AbstractJSEngine.java")
            self._create_abstract_js_engine(abstract_js_file)
        else:
            print("  ✓ AbstractJSEngine.java 已存在")

    def _create_abstract_js_engine(self, file_path: Path):
        """创建AbstractJSEngine"""
        
        content = '''package fire.script;

import java.util.HashMap;

/**
 * 抽象JavaScript引擎基类
 * Abstract JavaScript Engine Base Class
 */
public abstract class AbstractJSEngine extends HashMap<String, Object> {
    
    /**
     * 默认构造函数
     */
    public AbstractJSEngine() {
        super();
    }
    
    /**
     * 获取引擎类型
     */
    public String getEngineType() {
        return "AbstractJSEngine";
    }
    
    /**
     * 初始化引擎
     */
    public void initialize() {
        // 子类可以重写此方法
    }
}'''
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

    def _compile_and_verify(self):
        """编译和验证"""
        print("🔨 编译和验证...")
        
        # 编译关键文件
        files_to_compile = [
            "src/fire/script/AbstractJSEngine.java",
            "src/fire/script/JavaScriptEngineFix.java",
            "src/fire/script/FightJSEngine.java"
        ]
        
        for java_file in files_to_compile:
            try:
                cmd = [
                    "javac",
                    "-cp", "lib/*;gs_lib/*;lib2/*;libsys/*",
                    "-encoding", "UTF-8",
                    "-sourcepath", "src",
                    "-d", "out/production/gsxdb",
                    java_file
                ]
                
                result = subprocess.run(
                    cmd,
                    cwd=self.project_root,
                    capture_output=True,
                    text=True,
                    timeout=60
                )
                
                file_name = Path(java_file).name
                if result.returncode == 0:
                    print(f"  ✅ 编译成功: {file_name}")
                else:
                    print(f"  ❌ 编译失败: {file_name}")
                    if result.stderr:
                        print(f"    错误: {result.stderr[:200]}")
                    
            except Exception as e:
                print(f"  ❌ 编译异常: {java_file} - {str(e)}")

def main():
    """主函数"""
    fixer = EmergencyTypeFixer()
    
    print("🚨 紧急类型兼容性修复工具")
    print("=" * 50)
    
    # 执行紧急修复
    success = fixer.emergency_fix()
    
    print("\n" + "=" * 50)
    print("📊 紧急修复结果")
    print("=" * 50)
    
    if success:
        print("✅ 紧急修复成功!")
        print("\n💡 修复内容:")
        print("  🔧 恢复FightJSEngine继承AbstractJSEngine")
        print("  📝 确保AbstractJSEngine类存在")
        print("  🔨 验证编译通过")
        print("\n🚀 下一步:")
        print("  1. 重启游戏服务器")
        print("  2. 检查VerifyError是否解决")
        print("  3. 监控服务器启动日志")
    else:
        print("❌ 紧急修复失败!")
        print("\n🔄 建议:")
        print("  1. 检查错误信息")
        print("  2. 手动恢复备份文件")
        print("  3. 联系技术支持")

if __name__ == "__main__":
    main()
