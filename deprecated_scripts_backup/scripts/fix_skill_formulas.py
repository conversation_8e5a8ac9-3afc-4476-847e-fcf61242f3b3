#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技能公式修复脚本
用于分析gs.log中的技能公式错误并生成修复代码
"""

import re
import os
import sys
from collections import defaultdict, Counter
from pathlib import Path

class SkillFormulaFixer:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.gs_log_path = self.project_root / "centos7.6_game_server/game/server1/game_server/gs.log"
        self.jsfun_manager_path = self.project_root / "src/fire/script/JsFunManager.java"
        
        # 错误统计
        self.missing_formulas = Counter()
        self.missing_variables = Counter()
        self.js_script_errors = 0
        self.reference_errors = 0
        
        # 正则表达式
        self.js_script_pattern = re.compile(r'JS脚本.*?脚本:(.*?)$')
        self.reference_error_pattern = re.compile(r'ReferenceError: "(.*?)" is not defined')
        
    def analyze_gs_log(self):
        """分析gs.log中的技能公式错误"""
        print("🔍 开始分析gs.log中的技能公式错误...")
        
        if not self.gs_log_path.exists():
            print(f"❌ 找不到gs.log文件: {self.gs_log_path}")
            return False
        
        try:
            with open(self.gs_log_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    self._analyze_line(line.strip())
            
            self._print_analysis_results()
            return True
            
        except Exception as e:
            print(f"❌ 分析gs.log时出错: {str(e)}")
            return False
    
    def _analyze_line(self, line):
        """分析单行日志"""
        # 分析JS脚本注册错误
        if "JS脚本注册JavaScript表达式" in line:
            self.js_script_errors += 1
            match = self.js_script_pattern.search(line)
            if match:
                formula = match.group(1)
                self.missing_formulas[formula] += 1
        
        # 分析变量未定义错误
        if "ReferenceError" in line and "is not defined" in line:
            self.reference_errors += 1
            match = self.reference_error_pattern.search(line)
            if match:
                variable = match.group(1)
                self.missing_variables[variable] += 1
    
    def _print_analysis_results(self):
        """输出分析结果"""
        print(f"\n📊 分析结果:")
        print(f"   JS脚本注册错误: {self.js_script_errors} 个")
        print(f"   变量未定义错误: {self.reference_errors} 个")
        print(f"   缺失的技能公式: {len(self.missing_formulas)} 种")
        print(f"   缺失的变量: {len(self.missing_variables)} 种")
        
        print(f"\n🔝 最常见的缺失变量 (前10个):")
        for var, count in self.missing_variables.most_common(10):
            print(f"   {var}: {count} 次")
        
        print(f"\n🔝 最常见的缺失公式 (前10个):")
        for formula, count in self.missing_formulas.most_common(10):
            print(f"   {formula}: {count} 次")
    
    def generate_formula_mapping(self, start_id=900):
        """生成技能公式映射代码"""
        print(f"\n🔧 生成技能公式映射代码...")
        
        mapping_code = []
        handler_code = []
        
        current_id = start_id
        for formula, count in self.missing_formulas.most_common():
            # 生成映射代码
            mapping_code.append(f'        funMap.put("{formula}", {current_id});')
            
            # 生成处理代码
            java_code = self._convert_formula_to_java(formula)
            handler_code.append(f'            case {current_id}:')
            handler_code.append(f'                return {java_code};')
            
            current_id += 1
        
        return mapping_code, handler_code
    
    def _convert_formula_to_java(self, js_formula):
        """将JavaScript公式转换为Java代码"""
        # 移除 with(Math) 包装
        formula = re.sub(r'with\s*\(\s*Math\s*\)\s*\{\s*return\s*', '', js_formula)
        formula = re.sub(r'\s*;\s*\}\s*$', '', formula)
        
        # 替换JavaScript函数为Java Math函数
        formula = re.sub(r'\brandom\(\)', 'Math.random()', formula)
        formula = re.sub(r'\babs\(', 'Math.abs(', formula)
        formula = re.sub(r'\bmax\(', 'Math.max(', formula)
        formula = re.sub(r'\bmin\(', 'Math.min(', formula)
        formula = re.sub(r'\bfloor\(', 'Math.floor(', formula)
        formula = re.sub(r'\bpow\(', 'Math.pow(', formula)
        
        # 替换游戏变量为Java代码
        formula = self._replace_game_variables(formula)
        
        return formula
    
    def _replace_game_variables(self, formula):
        """替换游戏变量为对应的Java代码"""
        # 攻击者属性 (a结尾)
        replacements = {
            r'\bphyattacka\b': 'opf.getEffectRole().getAttrById(130)',
            r'\bmagicattacka\b': 'opf.getEffectRole().getAttrById(131)',
            r'\bmedicala\b': 'opf.getEffectRole().getAttrById(132)',
            r'\bdefenda\b': 'opf.getEffectRole().getAttrById(140)',
            r'\bmagicdefenda\b': 'opf.getEffectRole().getAttrById(141)',
            r'\bspeeda\b': 'opf.getEffectRole().getAttrById(150)',
            r'\bmaxhpa\b': 'opf.getEffectRole().getMaxHp()',
            r'\bcurhpa\b': 'opf.getEffectRole().getHp()',
            r'\bmaxmpa\b': 'opf.getEffectRole().getMaxMp()',
            r'\bcurmpa\b': 'opf.getEffectRole().getMp()',
            
            # 目标属性 (b结尾)
            r'\bphyattackb\b': 'aimf.getEffectRole().getAttrById(130)',
            r'\bmagicattackb\b': 'aimf.getEffectRole().getAttrById(131)',
            r'\bmedicalb\b': 'aimf.getEffectRole().getAttrById(132)',
            r'\bdefendb\b': 'aimf.getEffectRole().getAttrById(140)',
            r'\bmagicdefb\b': 'aimf.getEffectRole().getAttrById(141)',
            r'\bspeedb\b': 'aimf.getEffectRole().getAttrById(150)',
            r'\bmaxhpb\b': 'aimf.getEffectRole().getMaxHp()',
            r'\bcurhpb\b': 'aimf.getEffectRole().getHp()',
            r'\bmaxmpb\b': 'aimf.getEffectRole().getMaxMp()',
            r'\bcurmpb\b': 'aimf.getEffectRole().getMp()',
            
            # 技能和等级相关
            r'\bskilllevela\b': 'engine.getDouble("skilllevela").intValue()',
            r'\bgradea\b': 'engine.getDouble("gradea").intValue()',
            r'\bgradeb\b': 'aimf.getEffectRole().getLevel()',
            r'\bmaindamage\b': 'engine.getDouble("maindamage").intValue()',
            
            # 其他游戏变量
            r'\bpreaimcount\b': 'engine.getDouble("preaimcount").intValue()',
            r'\bhealrevisea\b': 'opf.getEffectRole().getAttrById(133)',
            r'\bmedicaljiashena\b': 'opf.getEffectRole().getAttrById(134)',
            r'\benhanceseala\b': 'opf.getEffectRole().getAttrById(135)',
            r'\bresistsealb\b': 'aimf.getEffectRole().getAttrById(136)',
        }
        
        for pattern, replacement in replacements.items():
            formula = re.sub(pattern, replacement, formula)
        
        return formula
    
    def generate_fix_report(self):
        """生成修复报告"""
        print(f"\n📋 生成技能公式修复报告...")
        
        report_path = self.project_root / "skill_formula_fix_report.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 技能公式修复报告\n\n")
            f.write(f"## 分析结果\n\n")
            f.write(f"- JS脚本注册错误: {self.js_script_errors} 个\n")
            f.write(f"- 变量未定义错误: {self.reference_errors} 个\n")
            f.write(f"- 缺失的技能公式: {len(self.missing_formulas)} 种\n")
            f.write(f"- 缺失的变量: {len(self.missing_variables)} 种\n\n")
            
            f.write(f"## 最常见的缺失变量\n\n")
            for var, count in self.missing_variables.most_common(20):
                f.write(f"- `{var}`: {count} 次\n")
            
            f.write(f"\n## 最常见的缺失公式\n\n")
            for formula, count in self.missing_formulas.most_common(20):
                f.write(f"- `{formula}`: {count} 次\n")
            
            # 生成修复代码
            mapping_code, handler_code = self.generate_formula_mapping()
            
            f.write(f"\n## 修复代码\n\n")
            f.write(f"### 在InitFunMap()方法中添加:\n\n")
            f.write("```java\n")
            for line in mapping_code[:20]:  # 只显示前20个
                f.write(line + "\n")
            f.write("```\n\n")
            
            f.write(f"### 在JsFunbyID()方法中添加:\n\n")
            f.write("```java\n")
            for line in handler_code[:40]:  # 只显示前40行
                f.write(line + "\n")
            f.write("```\n")
        
        print(f"✅ 修复报告已生成: {report_path}")
    
    def run(self):
        """运行修复流程"""
        print("🚀 开始技能公式修复流程...")
        
        if not self.analyze_gs_log():
            return False
        
        self.generate_fix_report()
        
        print(f"\n✅ 技能公式修复流程完成!")
        print(f"   已分析 {self.js_script_errors + self.reference_errors} 个错误")
        print(f"   识别出 {len(self.missing_formulas)} 种缺失公式")
        print(f"   识别出 {len(self.missing_variables)} 种缺失变量")
        
        return True

def main():
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = os.getcwd()
    
    fixer = SkillFormulaFixer(project_root)
    success = fixer.run()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
