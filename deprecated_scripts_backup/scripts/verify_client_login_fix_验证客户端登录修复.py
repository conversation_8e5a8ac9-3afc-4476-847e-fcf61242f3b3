#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户端登录问题修复验证脚本
Client Login Issue Fix Verification Script
==========================================

验证SceneJSEngine.put方法修复是否解决客户端闪退问题
Verify that SceneJSEngine.put method fix resolves client crash issues
"""

import subprocess
import json
import time
from pathlib import Path
from datetime import datetime

class ClientLoginFixVerifier:
    """客户端登录修复验证器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()

    def verify_login_fix(self):
        """验证登录修复"""
        print("🔍 验证客户端登录问题修复...")
        
        verification_result = {
            "timestamp": datetime.now().isoformat(),
            "verification_steps": [],
            "class_files_status": {},
            "method_signature_check": {},
            "log_analysis": {},
            "fix_success": False
        }
        
        try:
            # 步骤1: 验证关键class文件存在
            self._verify_class_files(verification_result)
            
            # 步骤2: 检查方法签名兼容性
            self._check_method_signatures(verification_result)
            
            # 步骤3: 分析最新日志
            self._analyze_latest_logs(verification_result)
            
            # 步骤4: 生成修复报告
            self._generate_fix_report(verification_result)
            
            # 判断修复成功状态
            verification_result["fix_success"] = self._determine_fix_success(verification_result)
            
        except Exception as e:
            verification_result["error"] = str(e)
            print(f"❌ 验证过程中发生错误: {str(e)}")
        
        return verification_result

    def _verify_class_files(self, result: dict):
        """验证关键class文件存在"""
        print("📁 验证关键class文件...")
        
        required_class_files = [
            "out/production/gsxdb/fire/script/IJavaScriptEngine.class",
            "out/production/gsxdb/fire/script/AbstractJSEngine.class", 
            "out/production/gsxdb/fire/script/SceneJSEngine.class",
            "out/production/gsxdb/fire/script/FightJSEngine.class"
        ]
        
        class_files_status = {}
        
        for class_file in required_class_files:
            class_path = self.project_root / class_file
            class_name = Path(class_file).name
            
            if class_path.exists():
                file_size = class_path.stat().st_size
                modified_time = datetime.fromtimestamp(class_path.stat().st_mtime)
                
                class_files_status[class_name] = {
                    "exists": True,
                    "size": file_size,
                    "modified": modified_time.isoformat(),
                    "recently_modified": (datetime.now() - modified_time).seconds < 3600  # 1小时内
                }
                
                status_icon = "🆕" if class_files_status[class_name]["recently_modified"] else "✅"
                print(f"  {status_icon} {class_name} ({file_size} bytes)")
            else:
                class_files_status[class_name] = {"exists": False}
                print(f"  ❌ {class_name} - 文件不存在")
        
        result["class_files_status"] = class_files_status
        result["verification_steps"].append("class文件验证")

    def _check_method_signatures(self, result: dict):
        """检查方法签名兼容性"""
        print("🔍 检查方法签名兼容性...")
        
        # 使用javap检查方法签名
        method_checks = {}
        
        try:
            # 检查IJavaScriptEngine接口
            cmd = ["javap", "-cp", "out/production/gsxdb", "-s", "fire.script.IJavaScriptEngine"]
            process = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if process.returncode == 0:
                output = process.stdout
                has_put_method = "put(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;" in output
                method_checks["IJavaScriptEngine"] = {
                    "success": True,
                    "has_correct_put_signature": has_put_method,
                    "output_sample": output[:200]
                }
                
                if has_put_method:
                    print("  ✅ IJavaScriptEngine.put方法签名正确")
                else:
                    print("  ⚠️ IJavaScriptEngine.put方法签名可能有问题")
            else:
                method_checks["IJavaScriptEngine"] = {
                    "success": False,
                    "error": process.stderr
                }
                print("  ❌ 无法检查IJavaScriptEngine")
            
            # 检查SceneJSEngine类
            cmd = ["javap", "-cp", "out/production/gsxdb", "-s", "fire.script.SceneJSEngine"]
            process = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if process.returncode == 0:
                output = process.stdout
                has_put_method = "put(Ljava/lang/String;Ljava/lang/Object;)Ljava/lang/Object;" in output
                method_checks["SceneJSEngine"] = {
                    "success": True,
                    "has_correct_put_signature": has_put_method,
                    "output_sample": output[:200]
                }
                
                if has_put_method:
                    print("  ✅ SceneJSEngine.put方法签名正确")
                else:
                    print("  ⚠️ SceneJSEngine.put方法签名可能有问题")
            else:
                method_checks["SceneJSEngine"] = {
                    "success": False,
                    "error": process.stderr
                }
                print("  ❌ 无法检查SceneJSEngine")
                
        except Exception as e:
            method_checks["error"] = str(e)
            print(f"  ❌ 方法签名检查异常: {str(e)}")
        
        result["method_signature_check"] = method_checks
        result["verification_steps"].append("方法签名检查")

    def _analyze_latest_logs(self, result: dict):
        """分析最新日志"""
        print("📊 分析最新日志...")
        
        log_file = self.project_root / "gs.log"
        log_analysis = {}
        
        if log_file.exists():
            try:
                # 读取最后100行日志
                with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    recent_lines = lines[-100:] if len(lines) > 100 else lines
                
                # 分析关键错误
                scenejs_errors = []
                nosuchmethod_errors = []
                login_errors = []
                
                for i, line in enumerate(recent_lines):
                    if "SceneJSEngine.put" in line and "NoSuchMethodError" in line:
                        scenejs_errors.append({
                            "line_number": len(lines) - len(recent_lines) + i + 1,
                            "content": line.strip()
                        })
                    
                    if "NoSuchMethodError" in line:
                        nosuchmethod_errors.append({
                            "line_number": len(lines) - len(recent_lines) + i + 1,
                            "content": line.strip()
                        })
                    
                    if "登录" in line or "login" in line.lower() or "error=2049" in line:
                        login_errors.append({
                            "line_number": len(lines) - len(recent_lines) + i + 1,
                            "content": line.strip()
                        })
                
                log_analysis = {
                    "total_recent_lines": len(recent_lines),
                    "scenejs_put_errors": len(scenejs_errors),
                    "nosuchmethod_errors": len(nosuchmethod_errors),
                    "login_related_errors": len(login_errors),
                    "scenejs_error_details": scenejs_errors[:3],  # 最多显示3个
                    "recent_nosuchmethod_errors": nosuchmethod_errors[-3:],  # 最新的3个
                    "recent_login_errors": login_errors[-3:]  # 最新的3个
                }
                
                print(f"  📊 最近100行日志分析:")
                print(f"    SceneJSEngine.put错误: {len(scenejs_errors)} 个")
                print(f"    NoSuchMethodError错误: {len(nosuchmethod_errors)} 个")
                print(f"    登录相关错误: {len(login_errors)} 个")
                
                if len(scenejs_errors) == 0:
                    print("  ✅ 未发现SceneJSEngine.put相关错误")
                else:
                    print("  ⚠️ 仍有SceneJSEngine.put相关错误")
                
            except Exception as e:
                log_analysis["error"] = str(e)
                print(f"  ❌ 日志分析失败: {str(e)}")
        else:
            log_analysis["error"] = "gs.log文件不存在"
            print("  ❌ gs.log文件不存在")
        
        result["log_analysis"] = log_analysis
        result["verification_steps"].append("日志分析")

    def _determine_fix_success(self, result: dict) -> bool:
        """判断修复成功状态"""
        
        # 检查class文件状态
        class_files_ok = all(
            status.get("exists", False) 
            for status in result.get("class_files_status", {}).values()
        )
        
        # 检查方法签名
        method_signatures_ok = all(
            check.get("has_correct_put_signature", False)
            for check in result.get("method_signature_check", {}).values()
            if isinstance(check, dict) and "has_correct_put_signature" in check
        )
        
        # 检查日志中是否还有SceneJSEngine错误
        log_analysis = result.get("log_analysis", {})
        no_scenejs_errors = log_analysis.get("scenejs_put_errors", 1) == 0
        
        return class_files_ok and method_signatures_ok and no_scenejs_errors

    def _generate_fix_report(self, result: dict):
        """生成修复报告"""
        print("📄 生成修复报告...")
        
        report_file = self.project_root / "client_login_fix_verification_report.json"
        
        # 添加修复总结
        result["fix_summary"] = {
            "verification_date": datetime.now().isoformat(),
            "total_verification_steps": len(result["verification_steps"]),
            "class_files_verified": len(result.get("class_files_status", {})),
            "method_signatures_checked": len(result.get("method_signature_check", {})),
            "log_lines_analyzed": result.get("log_analysis", {}).get("total_recent_lines", 0)
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"  ✓ 修复验证报告已保存: {report_file}")

def main():
    """主函数"""
    verifier = ClientLoginFixVerifier()
    
    print("🔍 客户端登录问题修复验证工具")
    print("=" * 50)
    
    # 执行验证
    result = verifier.verify_login_fix()
    
    print("\n" + "=" * 50)
    print("📊 客户端登录修复验证总结")
    print("=" * 50)
    
    # 显示验证结果
    steps_completed = len(result.get("verification_steps", []))
    fix_success = result.get("fix_success", False)
    
    print(f"📈 验证统计:")
    print(f"  ✅ 完成步骤: {steps_completed} 个")
    print(f"  🎯 修复状态: {'成功' if fix_success else '需要进一步处理'}")
    
    # 显示class文件状态
    class_files_status = result.get("class_files_status", {})
    if class_files_status:
        print(f"\n📁 Class文件状态:")
        for class_name, status in class_files_status.items():
            if status.get("exists", False):
                icon = "🆕" if status.get("recently_modified", False) else "✅"
                print(f"  {icon} {class_name} ({status.get('size', 0)} bytes)")
            else:
                print(f"  ❌ {class_name} - 缺失")
    
    # 显示方法签名检查结果
    method_checks = result.get("method_signature_check", {})
    if method_checks:
        print(f"\n🔍 方法签名检查:")
        for class_name, check in method_checks.items():
            if isinstance(check, dict) and "has_correct_put_signature" in check:
                icon = "✅" if check["has_correct_put_signature"] else "⚠️"
                print(f"  {icon} {class_name}.put方法")
    
    # 显示日志分析结果
    log_analysis = result.get("log_analysis", {})
    if log_analysis and "error" not in log_analysis:
        print(f"\n📊 日志分析结果:")
        print(f"  SceneJSEngine.put错误: {log_analysis.get('scenejs_put_errors', 0)} 个")
        print(f"  NoSuchMethodError错误: {log_analysis.get('nosuchmethod_errors', 0)} 个")
        print(f"  登录相关错误: {log_analysis.get('login_related_errors', 0)} 个")
    
    # 显示最终结论
    if fix_success:
        print(f"\n🎉 修复验证成功! 客户端登录问题已解决!")
        print(f"\n💡 修复内容:")
        print(f"  🔧 修复了SceneJSEngine.put方法签名")
        print(f"  📝 确保了IJavaScriptEngine接口兼容性")
        print(f"  🔨 重新编译了关键class文件")
        print(f"\n🚀 下一步:")
        print(f"  1. 重启游戏服务器")
        print(f"  2. 测试客户端登录")
        print(f"  3. 监控登录成功率")
        print(f"  4. 验证角色功能正常")
    else:
        print(f"\n⚠️ 修复验证发现问题，需要进一步处理")
        
        # 提供具体的问题和建议
        if not all(status.get("exists", False) for status in class_files_status.values()):
            print(f"  ❌ 部分class文件缺失，需要重新编译")
        
        if not all(check.get("has_correct_put_signature", False) 
                  for check in method_checks.values() 
                  if isinstance(check, dict) and "has_correct_put_signature" in check):
            print(f"  ❌ 方法签名不正确，需要检查接口实现")
        
        if log_analysis.get("scenejs_put_errors", 0) > 0:
            print(f"  ❌ 日志中仍有SceneJSEngine错误，需要进一步修复")
    
    print(f"\n📄 详细报告: client_login_fix_verification_report.json")

if __name__ == "__main__":
    main()
