#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JavaScript引擎修复工具 | JavaScript Engine Fix Tool
=================================================

修复GSXDB项目中的JavaScript引擎编译问题
Fix JavaScript engine compilation issues in GSXDB project

功能特性 | Features:
- 分析JavaScript脚本错误 | Analyze JavaScript script errors
- 修复脚本语法兼容性 | Fix script syntax compatibility
- 优化引擎配置 | Optimize engine configuration
- 生成修复报告 | Generate fix report
"""

import os
import re
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Set

class JavaScriptEngineFixer:
    """JavaScript引擎修复器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.src_dir = self.project_root / "src"
        
        # JavaScript脚本错误模式
        self.error_patterns = {
            "script_register_error": r"(\d+):JS脚本注册JavaScript表达式，脚本:(.+)",
            "script_compile_error": r"(\d+):JS脚本需要使用js引擎编译，脚本:(.+)",
            "with_math_pattern": r"with\(Math\)\{\s*(.+?)\s*\}",
            "function_pattern": r"function\s+(\w+)\(\)\{\s*with\(Math\)\{\s*return\s+(.+?);\s*\};\s*\}"
        }

    def analyze_javascript_errors(self):
        """分析JavaScript错误"""
        print("🔍 分析JavaScript引擎错误...")
        
        result = {
            "timestamp": datetime.now().isoformat(),
            "error_analysis": {},
            "script_issues": [],
            "fix_recommendations": []
        }
        
        try:
            # 分析日志文件中的JavaScript错误
            log_file = self.project_root / "gs.log"
            if log_file.exists():
                self._analyze_log_errors(log_file, result)
            
            # 分析源代码中的JavaScript相关问题
            self._analyze_source_code(result)
            
            # 生成修复建议
            self._generate_fix_recommendations(result)
            
            return result
            
        except Exception as e:
            result["error"] = f"分析过程中发生错误: {str(e)}"
            return result

    def _analyze_log_errors(self, log_file: Path, result: Dict):
        """分析日志文件中的错误"""
        print("  📄 分析日志文件...")
        
        script_register_errors = []
        script_compile_errors = []
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    # 检查脚本注册错误
                    register_match = re.search(self.error_patterns["script_register_error"], line)
                    if register_match:
                        error_code = register_match.group(1)
                        script_content = register_match.group(2)
                        script_register_errors.append({
                            "line": line_num,
                            "error_code": error_code,
                            "script": script_content,
                            "type": "register_error"
                        })
                    
                    # 检查脚本编译错误
                    compile_match = re.search(self.error_patterns["script_compile_error"], line)
                    if compile_match:
                        error_code = compile_match.group(1)
                        script_content = compile_match.group(2)
                        script_compile_errors.append({
                            "line": line_num,
                            "error_code": error_code,
                            "script": script_content,
                            "type": "compile_error"
                        })
        
        except Exception as e:
            print(f"  ❌ 读取日志文件失败: {str(e)}")
        
        result["error_analysis"] = {
            "script_register_errors": len(script_register_errors),
            "script_compile_errors": len(script_compile_errors),
            "total_errors": len(script_register_errors) + len(script_compile_errors)
        }
        
        result["script_issues"] = script_register_errors + script_compile_errors
        
        print(f"    ✓ 发现脚本注册错误: {len(script_register_errors)} 个")
        print(f"    ✓ 发现脚本编译错误: {len(script_compile_errors)} 个")

    def _analyze_source_code(self, result: Dict):
        """分析源代码中的JavaScript相关问题"""
        print("  📁 分析源代码...")
        
        js_related_files = []
        
        # 查找JavaScript相关的Java文件
        for java_file in self.src_dir.rglob("*.java"):
            try:
                with open(java_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                    if any(keyword in content for keyword in [
                        "ScriptEngine", "JavaScript", "eval", "with(Math)", 
                        "JsFunManager", "FightJSEngine"
                    ]):
                        js_related_files.append({
                            "file": str(java_file.relative_to(self.project_root)),
                            "issues": self._check_file_issues(content)
                        })
            except Exception as e:
                print(f"    ⚠️ 无法读取文件 {java_file}: {str(e)}")
        
        result["source_analysis"] = {
            "js_related_files": len(js_related_files),
            "files": js_related_files
        }
        
        print(f"    ✓ 发现JavaScript相关文件: {len(js_related_files)} 个")

    def _check_file_issues(self, content: str) -> List[str]:
        """检查文件中的问题"""
        issues = []
        
        # 检查with(Math)语法
        if "with(Math)" in content:
            issues.append("使用了with(Math)语法，可能不兼容新版JavaScript引擎")
        
        # 检查ScriptEngine初始化
        if "ScriptEngine" in content and "ScriptEngineManager" not in content:
            issues.append("使用ScriptEngine但缺少ScriptEngineManager初始化")
        
        # 检查异常处理
        if "eval(" in content and "ScriptException" not in content:
            issues.append("使用eval但缺少ScriptException异常处理")
        
        return issues

    def _generate_fix_recommendations(self, result: Dict):
        """生成修复建议"""
        print("  💡 生成修复建议...")
        
        recommendations = []
        
        # 基于错误分析生成建议
        total_errors = result["error_analysis"]["total_errors"]
        
        if total_errors > 0:
            recommendations.append({
                "priority": "高",
                "category": "JavaScript引擎配置",
                "issue": f"发现{total_errors}个JavaScript脚本错误",
                "solution": "升级或重新配置JavaScript引擎，确保兼容性",
                "implementation": [
                    "检查当前JavaScript引擎版本",
                    "更新JsFunManager和FightJSEngine类",
                    "添加脚本语法兼容性处理",
                    "实现错误恢复机制"
                ]
            })
        
        # 基于源代码分析生成建议
        source_analysis = result.get("source_analysis", {})
        js_files = source_analysis.get("files", [])
        
        for file_info in js_files:
            if file_info["issues"]:
                recommendations.append({
                    "priority": "中",
                    "category": "代码优化",
                    "issue": f"文件{file_info['file']}存在兼容性问题",
                    "solution": "修复代码中的JavaScript兼容性问题",
                    "implementation": file_info["issues"]
                })
        
        result["fix_recommendations"] = recommendations
        print(f"    ✓ 生成修复建议: {len(recommendations)} 条")

    def create_javascript_engine_fix(self):
        """创建JavaScript引擎修复类"""
        print("🔧 创建JavaScript引擎修复类...")
        
        # 创建修复后的JsFunManager
        jsfun_fix_content = '''package fire.script;

import javax.script.*;
import java.util.HashMap;
import java.util.Map;

/**
 * 修复版JavaScript函数管理器
 * Fixed JavaScript Function Manager
 */
public class JsFunManagerFixed {
    
    private static ScriptEngine engine;
    private static final Map<String, CompiledScript> compiledScripts = new HashMap<>();
    
    static {
        initializeJavaScriptEngine();
    }
    
    /**
     * 初始化JavaScript引擎
     */
    private static void initializeJavaScriptEngine() {
        try {
            ScriptEngineManager manager = new ScriptEngineManager();
            
            // 尝试使用Nashorn引擎
            engine = manager.getEngineByName("nashorn");
            if (engine == null) {
                // 回退到Rhino引擎
                engine = manager.getEngineByName("rhino");
            }
            if (engine == null) {
                // 使用默认JavaScript引擎
                engine = manager.getEngineByName("javascript");
            }
            
            if (engine == null) {
                throw new RuntimeException("无法找到可用的JavaScript引擎");
            }
            
            System.out.println("JavaScript引擎初始化成功: " + engine.getClass().getName());
            
        } catch (Exception e) {
            System.err.println("JavaScript引擎初始化失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 修复脚本语法
     */
    public static String fixScriptSyntax(String script) {
        if (script == null || script.trim().isEmpty()) {
            return script;
        }
        
        // 移除with(Math)包装
        script = script.replaceAll("with\\s*\\(\\s*Math\\s*\\)\\s*\\{\\s*", "");
        script = script.replaceAll("\\s*\\}\\s*$", "");
        
        // 添加Math.前缀到数学函数
        script = script.replaceAll("\\b(abs|max|min|floor|ceil|round|sqrt|pow)\\s*\\(", "Math.$1(");
        
        // 处理return语句
        if (!script.trim().startsWith("return")) {
            script = "return " + script;
        }
        
        // 确保以分号结尾
        if (!script.trim().endsWith(";")) {
            script = script + ";";
        }
        
        return script;
    }
    
    /**
     * 编译并执行脚本
     */
    public static Object executeScript(String script) {
        if (engine == null) {
            System.err.println("JavaScript引擎未初始化");
            return null;
        }
        
        try {
            // 修复脚本语法
            String fixedScript = fixScriptSyntax(script);
            
            // 尝试从缓存获取编译后的脚本
            CompiledScript compiled = compiledScripts.get(fixedScript);
            if (compiled == null && engine instanceof Compilable) {
                compiled = ((Compilable) engine).compile(fixedScript);
                compiledScripts.put(fixedScript, compiled);
            }
            
            // 执行脚本
            if (compiled != null) {
                return compiled.eval();
            } else {
                return engine.eval(fixedScript);
            }
            
        } catch (ScriptException e) {
            System.err.println("脚本执行失败: " + script);
            System.err.println("错误信息: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 批量修复脚本
     */
    public static void fixBattleScripts() {
        System.out.println("开始修复战斗脚本...");
        
        // 这里可以添加批量修复逻辑
        String[] testScripts = {
            "with(Math){ return -(phyattacka*1.3-defendb+1*skilllevela);}",
            "with(Math){ return 0.15*maxhpb;}",
            "with(Math){ return gradea*0.875;}"
        };
        
        for (String script : testScripts) {
            Object result = executeScript(script);
            System.out.println("修复脚本: " + script + " -> " + result);
        }
    }
}'''
        
        # 保存修复类
        fix_file = self.src_dir / "fire" / "script" / "JsFunManagerFixed.java"
        fix_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(fix_file, 'w', encoding='utf-8') as f:
            f.write(jsfun_fix_content)
        
        print(f"  ✓ 创建修复类: {fix_file}")
        
        return str(fix_file)

def main():
    """主函数"""
    fixer = JavaScriptEngineFixer()
    
    print("🔧 JavaScript引擎修复工具")
    print("=" * 50)
    
    # 分析JavaScript错误
    analysis_result = fixer.analyze_javascript_errors()
    
    # 创建修复类
    fix_file = fixer.create_javascript_engine_fix()
    
    # 保存分析报告
    report_file = Path("javascript_engine_analysis_report.json")
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, indent=2, ensure_ascii=False)
    
    print("\n" + "=" * 50)
    print("📊 JavaScript引擎错误分析总结")
    print("=" * 50)
    
    # 显示分析结果
    error_analysis = analysis_result.get("error_analysis", {})
    total_errors = error_analysis.get("total_errors", 0)
    
    print(f"📈 错误统计:")
    print(f"  🔴 脚本注册错误: {error_analysis.get('script_register_errors', 0)} 个")
    print(f"  🔴 脚本编译错误: {error_analysis.get('script_compile_errors', 0)} 个")
    print(f"  📊 总错误数: {total_errors} 个")
    
    # 显示修复建议
    recommendations = analysis_result.get("fix_recommendations", [])
    if recommendations:
        print(f"\n💡 修复建议 ({len(recommendations)} 条):")
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. [{rec['priority']}] {rec['category']}: {rec['issue']}")
            print(f"     解决方案: {rec['solution']}")
    
    print(f"\n📄 详细报告: {report_file}")
    print(f"🔧 修复类文件: {fix_file}")
    
    if total_errors > 0:
        print(f"\n⚠️  发现 {total_errors} 个JavaScript错误，建议立即修复！")
    else:
        print(f"\n✅ 未发现JavaScript错误，系统运行正常！")

if __name__ == "__main__":
    main()
