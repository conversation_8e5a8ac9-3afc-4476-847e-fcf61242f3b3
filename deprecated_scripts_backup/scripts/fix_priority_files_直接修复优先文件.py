#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接修复优先文件
Direct Fix Priority Files
========================

直接修复优先的反编译文件，无需交互
Directly fix priority decompiled files without interaction
"""

import os
import shutil
from pathlib import Path
from datetime import datetime
import json
import re

class PriorityFileFixer:
    """优先文件修复器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root).resolve()
        self.src_dir = self.project_root / "src"
        self.original_dir = self.project_root / "solutions" / "gsxdb原始java代码参考"
        self.backup_dir = self.project_root / "backup" / "priority_fix"
    
    def fix_priority_files(self):
        """修复优先文件"""
        print("🎯 修复优先反编译文件...")
        
        fix_result = {
            "timestamp": datetime.now().isoformat(),
            "priority_files": [],
            "files_replaced": [],
            "files_skipped": [],
            "errors": [],
            "success": False
        }
        
        try:
            # 创建备份目录
            self._create_backup_dir()
            
            # 获取优先文件列表
            priority_files = self._get_priority_files()
            
            for relative_path in priority_files:
                try:
                    original_file = self.original_dir / relative_path
                    target_file = self.src_dir / relative_path
                    
                    if original_file.exists() and target_file.exists():
                        # 检查是否需要修复
                        if self._contains_decompiled_vars(target_file):
                            print(f"  🔧 修复: {relative_path}")
                            
                            # 备份原文件
                            backup_file = self.backup_dir / relative_path
                            backup_file.parent.mkdir(parents=True, exist_ok=True)
                            shutil.copy2(target_file, backup_file)
                            
                            # 替换为原始文件
                            shutil.copy2(original_file, target_file)
                            
                            fix_result["files_replaced"].append(relative_path)
                            print(f"    ✓ 已替换")
                        else:
                            fix_result["files_skipped"].append(f"{relative_path} (无需修复)")
                            print(f"    ℹ️ 跳过: {relative_path} (无需修复)")
                    else:
                        if not original_file.exists():
                            fix_result["files_skipped"].append(f"{relative_path} (原始文件不存在)")
                            print(f"    ⚠️ 跳过: {relative_path} (原始文件不存在)")
                        if not target_file.exists():
                            fix_result["files_skipped"].append(f"{relative_path} (目标文件不存在)")
                            print(f"    ⚠️ 跳过: {relative_path} (目标文件不存在)")
                        
                except Exception as e:
                    error_msg = f"修复文件失败 {relative_path}: {str(e)}"
                    fix_result["errors"].append(error_msg)
                    print(f"    ❌ {error_msg}")
            
            fix_result["priority_files"] = priority_files
            fix_result["success"] = True
            
            # 生成修复报告
            self._generate_fix_report(fix_result)
            
            print("✅ 优先文件修复完成")
            
        except Exception as e:
            fix_result["errors"].append(str(e))
            print(f"❌ 优先文件修复失败: {str(e)}")
        
        return fix_result
    
    def _create_backup_dir(self):
        """创建备份目录"""
        self.backup_dir.mkdir(parents=True, exist_ok=True)
    
    def _contains_decompiled_vars(self, file_path: Path) -> bool:
        """检查文件是否包含反编译变量名"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查是否包含var1, var2, var3等模式
            var_pattern = r'\bvar\d+\b'
            return bool(re.search(var_pattern, content))
            
        except Exception:
            return False
    
    def _get_priority_files(self):
        """获取优先修复的文件列表"""
        priority_files = [
            # 日志系统 - 已知包含反编译变量
            "fire/log/YYLogger.java",
            
            # 技能系统 - 已知包含反编译变量
            "fire/pb/skill/particleskill/CRequestLearnParticleSkill.java",
            
            # 宠物系统 - 已知包含大量反编译变量
            "fire/pb/pet/Pet.java",
            
            # 战斗系统
            "fire/pb/battle/Monster.java",
            "fire/pb/battle/BattleField.java", 
            "fire/pb/battle/PNewBattle.java",
            
            # 物品系统
            "fire/pb/item/ItemBase.java",
            
            # 角色系统
            "fire/pb/role/RoleImpl.java",
            
            # 活动系统
            "fire/pb/activity/ActivityClanFightManager.java",
            
            # 帮派系统
            "fire/pb/clan/ClanManage.java",
            
            # 好友系统
            "fire/pb/friends/PSendFriendMsgProc.java",
            
            # GM系统
            "fire/pb/gm/GM_addlevel.java",
            "fire/pb/gm/GM_mail.java",
            
            # 商店系统
            "fire/pb/shop/MarketUtils.java",
            
            # 队伍系统
            "fire/pb/team/CRespondInvite.java",
            
            # 任务系统
            "fire/pb/mission/RoleMission.java",
            
            # 地图系统
            "fire/pb/map/PCreateTimerNpcByData.java",
            
            # 技能系统
            "fire/pb/skill/FightSkill.java",
            "fire/pb/skill/liveskill/LiveSkillManager.java",
            
            # Buff系统
            "fire/pb/buff/continual/ConstantlyBuff.java",
            "fire/pb/buff/continual/PhysicalInjure.java",
            
            # 循环任务
            "fire/pb/circletask/CircleTaskManager.java",
            "fire/pb/circletask/RoleAnYeTask.java"
        ]
        
        return priority_files
    
    def _generate_fix_report(self, result):
        """生成修复报告"""
        report_file = self.project_root / "priority_files_fix_report.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"  📊 修复报告: {report_file}")

def main():
    """主函数"""
    fixer = PriorityFileFixer()
    
    print("🎯 优先反编译文件修复器")
    print("=" * 50)
    
    # 直接修复优先文件
    result = fixer.fix_priority_files()
    
    print("\n" + "=" * 50)
    print("📊 修复结果总结")
    print("=" * 50)
    
    if result["success"]:
        print("✅ 修复成功!")
        print(f"📄 已替换文件: {len(result['files_replaced'])} 个")
        print(f"⏭️ 跳过文件: {len(result['files_skipped'])} 个")
        
        if result["files_replaced"]:
            print(f"\n🔧 已替换的文件:")
            for file_path in result["files_replaced"]:
                print(f"  ✓ {file_path}")
        
        if result["errors"]:
            print(f"\n⚠️ 错误信息:")
            for error in result["errors"]:
                print(f"  ❌ {error}")
        
        print(f"\n🎯 下一步:")
        print(f"  1. 在IDEA中重新编译修复后的文件")
        print(f"  2. 更新jar包")
        print(f"  3. 重启服务器测试")
        
        # 生成编译命令
        if result["files_replaced"]:
            print(f"\n📋 需要在IDEA中手动编译的文件:")
            for i, file_path in enumerate(result["files_replaced"], 1):
                src_path = f"src/{file_path}"
                print(f"  {i:2d}. {src_path}")
        
    else:
        print("❌ 修复失败")
        if result["errors"]:
            for error in result["errors"]:
                print(f"  错误: {error}")

if __name__ == "__main__":
    main()
