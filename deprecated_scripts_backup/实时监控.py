#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时监控游戏服务器状态
"""

import subprocess
import time
import os
from datetime import datetime

def log(message):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_java_process():
    """检查Java进程"""
    try:
        result = subprocess.run(['powershell', '-Command', 
            'Get-Process java -ErrorAction SilentlyContinue | Select-Object Id,ProcessName,CPU'], 
            capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0 and result.stdout.strip():
            lines = result.stdout.strip().split('\n')
            if len(lines) > 2:
                return True, lines[2:]  # 跳过标题行
        return False, []
    except:
        return False, []

def get_latest_log_lines(log_file, num_lines=5):
    """获取最新的日志行"""
    try:
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                return lines[-num_lines:] if lines else []
        return []
    except:
        return []

def analyze_errors(lines):
    """分析错误"""
    errors = []
    for line in lines:
        line = line.strip()
        if 'ERROR' in line or 'FATAL' in line or 'Exception' in line:
            errors.append(line)
    return errors

def main():
    log_file = "game/server1/game_server/gs.log"
    
    log("🔍 开始实时监控游戏服务器...")
    
    for i in range(60):  # 监控5分钟
        log(f"=== 第 {i+1} 次检查 ===")
        
        # 检查Java进程
        java_running, java_info = check_java_process()
        if java_running:
            log("✅ Java进程运行中")
            for info in java_info:
                if info.strip():
                    log(f"   📊 {info.strip()}")
        else:
            log("❌ Java进程未运行")
        
        # 检查最新日志
        latest_lines = get_latest_log_lines(log_file, 3)
        if latest_lines:
            log("📝 最新日志:")
            for line in latest_lines:
                line = line.strip()
                if line:
                    if 'ERROR' in line or 'FATAL' in line:
                        log(f"   ❌ {line}")
                    elif 'WARN' in line:
                        log(f"   ⚠️  {line}")
                    else:
                        log(f"   📄 {line}")
        
        # 分析错误
        errors = analyze_errors(latest_lines)
        if errors:
            log(f"⚠️  发现 {len(errors)} 个错误:")
            for error in errors:
                log(f"   🚨 {error}")
        
        print("-" * 80)
        time.sleep(5)  # 每5秒检查一次
    
    log("✅ 监控完成")

if __name__ == "__main__":
    main()
