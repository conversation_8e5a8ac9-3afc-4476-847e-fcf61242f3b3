#!/bin/bash
#==============================================================================
# 快速语法测试脚本
# 用于验证qd脚本的语法正确性
#==============================================================================

echo "=== QD脚本语法检查 ==="
echo "检查时间: $(date)"
echo

# 检查脚本文件是否存在
if [ ! -f "centos7.6_game_server/bin/qd" ]; then
    echo "❌ 错误: qd脚本文件不存在"
    exit 1
fi

echo "✅ qd脚本文件存在"

# 检查脚本权限
if [ -x "centos7.6_game_server/bin/qd" ]; then
    echo "✅ qd脚本具有执行权限"
else
    echo "⚠️  警告: qd脚本缺少执行权限"
    echo "   修复命令: chmod +x centos7.6_game_server/bin/qd"
fi

# 检查基本语法结构
echo
echo "=== 语法结构检查 ==="

# 检查if-fi匹配
if_count=$(grep -c "^[[:space:]]*if\|^[[:space:]]*elif" centos7.6_game_server/bin/qd)
fi_count=$(grep -c "^[[:space:]]*fi" centos7.6_game_server/bin/qd)

echo "if/elif 语句数量: $if_count"
echo "fi 语句数量: $fi_count"

if [ "$if_count" -eq "$fi_count" ]; then
    echo "✅ if-fi 语句匹配"
else
    echo "❌ if-fi 语句不匹配"
fi

# 检查case-esac匹配
case_count=$(grep -c "^[[:space:]]*case" centos7.6_game_server/bin/qd)
esac_count=$(grep -c "^[[:space:]]*esac" centos7.6_game_server/bin/qd)

echo "case 语句数量: $case_count"
echo "esac 语句数量: $esac_count"

if [ "$case_count" -eq "$esac_count" ]; then
    echo "✅ case-esac 语句匹配"
else
    echo "❌ case-esac 语句不匹配"
fi

# 检查函数定义
func_start=$(grep -c "^[[:space:]]*[a-zA-Z_][a-zA-Z0-9_]*[[:space:]]*(" centos7.6_game_server/bin/qd)
func_end=$(grep -c "^[[:space:]]*}[[:space:]]*$" centos7.6_game_server/bin/qd)

echo "函数定义数量: $func_start"
echo "函数结束数量: $func_end"

# 检查常见语法问题
echo
echo "=== 常见问题检查 ==="

# 检查未闭合的引号
if grep -n "[^\\]\"[^\"]*$" centos7.6_game_server/bin/qd >/dev/null; then
    echo "⚠️  警告: 可能存在未闭合的双引号"
else
    echo "✅ 双引号检查通过"
fi

if grep -n "[^\\]'[^']*$" centos7.6_game_server/bin/qd >/dev/null; then
    echo "⚠️  警告: 可能存在未闭合的单引号"
else
    echo "✅ 单引号检查通过"
fi

# 检查变量引用
if grep -n '\$[a-zA-Z_][a-zA-Z0-9_]*[^a-zA-Z0-9_"}\]]' centos7.6_game_server/bin/qd >/dev/null; then
    echo "⚠️  警告: 可能存在不安全的变量引用"
else
    echo "✅ 变量引用检查通过"
fi

echo
echo "=== 建议的测试命令 ==="
echo "1. 语法检查: bash -n centos7.6_game_server/bin/qd"
echo "2. 帮助测试: ./centos7.6_game_server/bin/qd help"
echo "3. 端口检查: ./centos7.6_game_server/bin/qd dk"
echo "4. 菜单测试: ./centos7.6_game_server/bin/qd"

echo
echo "=== 修复建议 ==="
echo "如果在CentOS环境中仍有语法错误，请："
echo "1. 检查行尾是否有Windows格式的换行符"
echo "2. 确保所有if语句都有对应的fi"
echo "3. 确保所有case语句都有对应的esac"
echo "4. 检查函数定义的括号匹配"

echo
echo "语法检查完成！"
