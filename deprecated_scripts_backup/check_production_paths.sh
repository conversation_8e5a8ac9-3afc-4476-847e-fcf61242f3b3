#!/bin/bash
#==============================================================================
# 生产环境路径检测脚本
# 版本: 1.0
# 创建时间: 2025-01-29
# 描述: 检测生产环境中的游戏服务器目录结构
#==============================================================================

set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${CYAN}[DEBUG]${NC} $1"
}

# 检查当前环境
check_environment() {
    log_info "=== 环境信息 ==="
    echo "当前用户: $(whoami)"
    echo "当前目录: $(pwd)"
    echo "主目录: $HOME"
    echo "脚本位置: ${BASH_SOURCE[0]}"
    echo "脚本目录: $(dirname "${BASH_SOURCE[0]}")"
    echo
}

# 检查可能的游戏目录
check_game_directories() {
    log_info "=== 检查游戏目录 ==="
    
    local possible_game_dirs=(
        "/home/<USER>"
        "$HOME/game"
        "$(pwd)/game"
        "$(dirname "${BASH_SOURCE[0]}")/../game"
        "/opt/game"
        "/usr/local/game"
        "./centos7.6_game_server/game"
    )
    
    local found_dirs=()
    
    for dir in "${possible_game_dirs[@]}"; do
        if [ -d "$dir" ]; then
            log_info "找到游戏目录: $dir"
            found_dirs+=("$dir")
            
            # 检查子目录
            if [ -d "$dir" ]; then
                echo "  子目录:"
                ls -la "$dir" 2>/dev/null | grep "^d" | awk '{print "    " $9}' || true
            fi
        else
            log_debug "目录不存在: $dir"
        fi
    done
    
    if [ ${#found_dirs[@]} -eq 0 ]; then
        log_error "未找到任何游戏目录"
        return 1
    fi
    
    echo
    return 0
}

# 检查大区目录
check_server_directories() {
    log_info "=== 检查大区目录 ==="
    
    local possible_base_dirs=(
        "/home/<USER>"
        "$HOME/game"
        "$(pwd)/game"
        "$(dirname "${BASH_SOURCE[0]}")/../game"
        "/opt/game"
        "/usr/local/game"
        "./centos7.6_game_server/game"
    )
    
    local found_servers=()
    
    for base_dir in "${possible_base_dirs[@]}"; do
        if [ -d "$base_dir" ]; then
            log_debug "检查基础目录: $base_dir"
            
            # 查找server*目录
            for server_dir in "$base_dir"/server*; do
                if [ -d "$server_dir" ]; then
                    local server_name=$(basename "$server_dir")
                    local server_num=${server_name#server}
                    
                    log_info "找到大区: $server_name (编号: $server_num)"
                    echo "  路径: $server_dir"
                    
                    # 检查大区内的服务
                    local services=("game_server" "gate_server" "proxy_server")
                    for service in "${services[@]}"; do
                        if [ -d "$server_dir/$service" ]; then
                            echo "    ✓ $service"
                        else
                            echo "    ✗ $service (缺失)"
                        fi
                    done
                    
                    found_servers+=("$server_dir")
                    echo
                fi
            done
        fi
    done
    
    if [ ${#found_servers[@]} -eq 0 ]; then
        log_error "未找到任何大区目录"
        return 1
    fi
    
    return 0
}

# 检查qd脚本位置
check_qd_script() {
    log_info "=== 检查qd脚本 ==="
    
    local possible_qd_paths=(
        "./qd"
        "./bin/qd"
        "/home/<USER>/bin/qd"
        "$HOME/bin/qd"
        "$(pwd)/bin/qd"
        "$(dirname "${BASH_SOURCE[0]}")/qd"
        "./centos7.6_game_server/bin/qd"
    )
    
    for qd_path in "${possible_qd_paths[@]}"; do
        if [ -f "$qd_path" ]; then
            log_info "找到qd脚本: $qd_path"
            
            if [ -x "$qd_path" ]; then
                echo "  ✓ 可执行"
            else
                echo "  ✗ 不可执行 (需要: chmod +x $qd_path)"
            fi
            
            # 检查脚本中的路径配置
            if grep -q "GAME_HOME" "$qd_path"; then
                local game_home=$(grep "GAME_HOME=" "$qd_path" | head -n1 | cut -d'"' -f2)
                echo "  配置的GAME_HOME: $game_home"
                
                if [ -d "$game_home" ]; then
                    echo "    ✓ 目录存在"
                else
                    echo "    ✗ 目录不存在"
                fi
            fi
            echo
        else
            log_debug "qd脚本不存在: $qd_path"
        fi
    done
}

# 检查Java环境
check_java() {
    log_info "=== 检查Java环境 ==="
    
    if command -v java &> /dev/null; then
        log_info "Java可用"
        java -version 2>&1 | head -n3
        echo "Java路径: $(which java)"
    else
        log_error "Java不可用"
    fi
    echo
}

# 检查网络工具
check_network_tools() {
    log_info "=== 检查网络工具 ==="
    
    local tools=("netstat" "ss" "lsof" "nc")
    
    for tool in "${tools[@]}"; do
        if command -v "$tool" &> /dev/null; then
            log_info "$tool 可用: $(which $tool)"
        else
            log_warn "$tool 不可用"
        fi
    done
    echo
}

# 生成修复建议
generate_fix_suggestions() {
    log_info "=== 修复建议 ==="
    
    echo "1. 如果qd脚本报告'大区不存在'，请检查以下内容："
    echo
    
    echo "2. 确认游戏目录结构："
    echo "   - 检查 /home/<USER>"
    echo "   - 检查大区目录命名是否正确 (server1, server2, etc.)"
    echo "   - 确保目录权限正确"
    echo
    
    echo "3. 修复qd脚本中的路径配置："
    echo "   - 编辑qd脚本中的GAME_HOME变量"
    echo "   - 确保路径指向正确的游戏目录"
    echo
    
    echo "4. 设置正确的权限："
    echo "   chmod +x /path/to/qd"
    echo "   chmod -R 755 /home/<USER>"
    echo
    
    echo "5. 如果目录结构不同，可以创建软链接："
    echo "   ln -s /actual/game/path /home/<USER>"
    echo
    
    echo "6. 测试命令："
    echo "   ./qd dk     # 检查端口"
    echo "   ./qd help   # 显示帮助"
    echo "   ./qd 1      # 启动大区1"
    echo
}

# 主函数
main() {
    echo -e "${BLUE}=== 生产环境路径检测工具 ===${NC}"
    echo -e "${BLUE}检测时间: $(date)${NC}"
    echo
    
    check_environment
    check_java
    check_network_tools
    check_game_directories
    check_server_directories
    check_qd_script
    generate_fix_suggestions
    
    echo -e "${GREEN}检测完成！${NC}"
    echo
    echo -e "${YELLOW}如果仍有问题，请将此输出发送给技术支持。${NC}"
}

# 执行主函数
main "$@"
