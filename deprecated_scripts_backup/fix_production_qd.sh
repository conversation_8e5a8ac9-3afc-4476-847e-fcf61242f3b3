#!/bin/bash
#==============================================================================
# 生产环境QD脚本修复工具
# 专门针对 /home/<USER>/ 结构的服务器
# 版本: 1.0
# 创建时间: 2025-01-29
#==============================================================================

set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

# 常量定义
readonly GAME_HOME="/home/<USER>"
readonly EXPECTED_STRUCTURE=(
    "$GAME_HOME"
    "$GAME_HOME/server1"
    "$GAME_HOME/server1/game_server"
    "$GAME_HOME/server1/gate_server"
    "$GAME_HOME/server1/proxy_server"
)

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查目录结构
check_directory_structure() {
    log_info "=== 检查目录结构 ==="
    
    local all_good=true
    
    for dir in "${EXPECTED_STRUCTURE[@]}"; do
        if [ -d "$dir" ]; then
            log_info "✓ $dir 存在"
        else
            log_error "✗ $dir 不存在"
            all_good=false
        fi
    done
    
    if [ "$all_good" = true ]; then
        log_info "目录结构检查通过"
        return 0
    else
        log_error "目录结构检查失败"
        return 1
    fi
}

# 查找qd脚本
find_qd_scripts() {
    log_info "=== 查找QD脚本 ==="
    
    local qd_scripts=()
    
    # 查找主qd脚本
    local main_qd_locations=(
        "$GAME_HOME/qd"
        "$GAME_HOME/bin/qd"
        "/usr/local/bin/qd"
        "$(pwd)/qd"
    )
    
    for location in "${main_qd_locations[@]}"; do
        if [ -f "$location" ]; then
            log_info "找到主QD脚本: $location"
            qd_scripts+=("$location")
        fi
    done
    
    # 查找大区qd脚本
    for server_dir in "$GAME_HOME"/server*; do
        if [ -d "$server_dir" ]; then
            local qd_sh="$server_dir/qd.sh"
            if [ -f "$qd_sh" ]; then
                log_info "找到大区QD脚本: $qd_sh"
                qd_scripts+=("$qd_sh")
            fi
        fi
    done
    
    if [ ${#qd_scripts[@]} -eq 0 ]; then
        log_error "未找到任何QD脚本"
        return 1
    fi
    
    # 返回找到的脚本
    printf '%s\n' "${qd_scripts[@]}"
    return 0
}

# 修复主qd脚本配置
fix_main_qd_script() {
    local qd_script="$1"
    
    log_info "修复主QD脚本: $qd_script"
    
    # 备份原文件
    local backup_file="${qd_script}.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$qd_script" "$backup_file"
    log_info "已备份到: $backup_file"
    
    # 修复GAME_HOME路径
    if grep -q "readonly GAME_HOME=" "$qd_script"; then
        sed -i "s|readonly GAME_HOME=.*|readonly GAME_HOME=\"$GAME_HOME\"|g" "$qd_script"
        log_info "已更新GAME_HOME路径"
    else
        log_warn "未找到GAME_HOME配置，可能需要手动添加"
    fi
    
    # 修复LOG_FILE路径
    if grep -q "readonly LOG_FILE=" "$qd_script"; then
        sed -i "s|readonly LOG_FILE=.*|readonly LOG_FILE=\"$GAME_HOME/script.log\"|g" "$qd_script"
        log_info "已更新LOG_FILE路径"
    fi
    
    # 设置执行权限
    chmod +x "$qd_script"
    log_info "已设置执行权限"
}

# 修复大区qd脚本
fix_zone_qd_script() {
    local qd_script="$1"
    
    log_info "修复大区QD脚本: $qd_script"
    
    # 设置执行权限
    chmod +x "$qd_script"
    
    # 检查脚本内容
    if [ -s "$qd_script" ]; then
        log_info "大区脚本存在且非空"
    else
        log_warn "大区脚本为空或不存在"
    fi
}

# 设置目录权限
set_directory_permissions() {
    log_info "=== 设置目录权限 ==="
    
    # 设置游戏目录权限
    if [ -d "$GAME_HOME" ]; then
        chmod -R 755 "$GAME_HOME" 2>/dev/null || true
        log_info "已设置游戏目录权限"
    fi
    
    # 设置特定脚本权限
    for server_dir in "$GAME_HOME"/server*; do
        if [ -d "$server_dir" ]; then
            # 设置大区脚本权限
            [ -f "$server_dir/qd.sh" ] && chmod +x "$server_dir/qd.sh"
            
            # 设置游戏服务器脚本权限
            [ -f "$server_dir/game_server/start.sh" ] && chmod +x "$server_dir/game_server/start.sh"
            
            # 设置服务器可执行文件权限
            [ -f "$server_dir/gate_server/gateserver" ] && chmod +x "$server_dir/gate_server/gateserver"
            [ -f "$server_dir/proxy_server/proxyserver" ] && chmod +x "$server_dir/proxy_server/proxyserver"
        fi
    done
    
    log_info "权限设置完成"
}

# 测试修复结果
test_qd_functionality() {
    log_info "=== 测试QD功能 ==="
    
    # 查找主qd脚本进行测试
    local main_qd=""
    for location in "$GAME_HOME/qd" "$GAME_HOME/bin/qd"; do
        if [ -f "$location" ] && [ -x "$location" ]; then
            main_qd="$location"
            break
        fi
    done
    
    if [ -z "$main_qd" ]; then
        log_warn "未找到可执行的主QD脚本"
        return 1
    fi
    
    log_info "测试脚本: $main_qd"
    
    # 切换到游戏目录
    cd "$GAME_HOME" || {
        log_error "无法切换到游戏目录"
        return 1
    }
    
    # 测试语法
    if bash -n "$main_qd" 2>/dev/null; then
        log_info "✓ 脚本语法正确"
    else
        log_error "✗ 脚本语法错误"
        return 1
    fi
    
    # 测试帮助功能
    if timeout 10 "$main_qd" help &>/dev/null; then
        log_info "✓ 帮助功能正常"
    else
        log_warn "⚠ 帮助功能可能有问题"
    fi
    
    # 测试端口检查功能
    if timeout 10 "$main_qd" dk &>/dev/null; then
        log_info "✓ 端口检查功能正常"
    else
        log_warn "⚠ 端口检查功能可能有问题"
    fi
    
    return 0
}

# 显示使用说明
show_usage_guide() {
    log_info "=== 使用指南 ==="
    echo
    echo "修复完成后，请按以下方式使用："
    echo
    echo "1. 切换到游戏目录:"
    echo "   cd $GAME_HOME"
    echo
    echo "2. 检查可用命令:"
    echo "   ./bin/qd help"
    echo
    echo "3. 检查端口状态:"
    echo "   ./bin/qd dk"
    echo
    echo "4. 启动大区1:"
    echo "   ./bin/qd 1"
    echo
    echo "5. 关闭大区1:"
    echo "   ./bin/qd 01"
    echo
    echo "6. 或者使用大区脚本:"
    echo "   cd $GAME_HOME/server1"
    echo "   ./qd.sh start"
    echo "   ./qd.sh status"
    echo
}

# 主函数
main() {
    echo -e "${BLUE}=== 生产环境QD脚本修复工具 ===${NC}"
    echo -e "${BLUE}修复时间: $(date)${NC}"
    echo -e "${BLUE}目标环境: $GAME_HOME${NC}"
    echo
    
    # 检查是否为root用户
    if [ "$(id -u)" -eq 0 ]; then
        log_warn "检测到root用户，请确保这是预期的"
    fi
    
    # 检查目录结构
    if ! check_directory_structure; then
        log_error "目录结构检查失败，请确保游戏服务器已正确部署"
        exit 1
    fi
    
    # 查找qd脚本
    local qd_scripts
    if ! qd_scripts=$(find_qd_scripts); then
        log_error "未找到QD脚本，请检查部署"
        exit 1
    fi
    
    # 修复找到的脚本
    while IFS= read -r script; do
        if [[ "$script" == *"/qd" ]]; then
            fix_main_qd_script "$script"
        elif [[ "$script" == *"/qd.sh" ]]; then
            fix_zone_qd_script "$script"
        fi
    done <<< "$qd_scripts"
    
    # 设置权限
    set_directory_permissions
    
    # 测试功能
    if test_qd_functionality; then
        log_info "✓ 功能测试通过"
    else
        log_warn "⚠ 功能测试有问题，但基本修复已完成"
    fi
    
    # 显示使用指南
    show_usage_guide
    
    echo -e "${GREEN}=== 修复完成 ===${NC}"
    echo
    echo -e "${YELLOW}现在您可以尝试启动大区1:${NC}"
    echo -e "${YELLOW}cd $GAME_HOME && ./bin/qd 1${NC}"
}

# 执行主函数
main "$@"
