#!/bin/bash
# QD脚本菜单优化验证工具

echo "=========================================="
echo "QD脚本菜单优化验证工具"
echo "=========================================="

QD_SCRIPT="centos7.6_game_server/bin/qd/qd"

# 检查qd脚本是否存在
if [ ! -f "$QD_SCRIPT" ]; then
    echo "❌ 错误：qd脚本文件不存在: $QD_SCRIPT"
    exit 1
fi

echo "✅ qd脚本文件存在"

# 检查脚本语法
echo "🔍 检查脚本语法..."
if bash -n "$QD_SCRIPT"; then
    echo "✅ 脚本语法检查通过"
else
    echo "❌ 脚本语法错误"
    exit 1
fi

# 检查关键函数是否存在
echo "🔍 检查关键函数..."

functions_to_check=(
    "print_menu_item_columns"
    "show_menu"
    "print_title"
)

for func in "${functions_to_check[@]}"; do
    if grep -q "^$func()" "$QD_SCRIPT"; then
        echo "✅ 函数 $func 存在"
    else
        echo "❌ 函数 $func 不存在"
        exit 1
    fi
done

# 检查菜单项是否完整
echo "🔍 检查菜单项完整性..."

menu_items=(
    "区服管理"
    "Robot测试系统"
    "数据管理"
    "系统工具"
    "print_menu_item_columns.*0.*启动公共服务"
    "print_menu_item_columns.*robot.*启动Robot测试"
    "print_menu_item_columns.*SD.*单区删档"
    "print_menu_item_columns.*dk.*查看端口状态"
)

for item in "${menu_items[@]}"; do
    if grep -q "$item" "$QD_SCRIPT"; then
        echo "✅ 菜单项检查通过: $item"
    else
        echo "❌ 菜单项缺失: $item"
        exit 1
    fi
done

# 检查是否移除了重复函数定义
echo "🔍 检查重复函数定义..."
func_count=$(grep -c "^print_menu_item_columns()" "$QD_SCRIPT")
if [ "$func_count" -eq 1 ]; then
    echo "✅ 重复函数定义已清理"
else
    echo "❌ 仍存在重复函数定义 (发现 $func_count 个)"
    exit 1
fi

# 检查Unicode字符使用
echo "🔍 检查Unicode框线字符..."
if grep -q "╔.*╗" "$QD_SCRIPT" && grep -q "┌.*┐" "$QD_SCRIPT"; then
    echo "✅ Unicode框线字符使用正确"
else
    echo "❌ Unicode框线字符缺失"
    exit 1
fi

# 检查颜色定义
echo "🔍 检查颜色定义..."
colors=(
    "GREEN"
    "PURPLE"
    "RED"
    "YELLOW"
    "CYAN"
    "BOLD"
    "NC"
)

for color in "${colors[@]}"; do
    if grep -q "readonly $color=" "$QD_SCRIPT"; then
        echo "✅ 颜色定义存在: $color"
    else
        echo "❌ 颜色定义缺失: $color"
        exit 1
    fi
done

# 统计菜单行数（估算）
echo "📊 菜单优化效果分析..."
menu_start=$(grep -n "show_menu()" "$QD_SCRIPT" | cut -d: -f1)
menu_end=$(grep -n "printf.*请输入指令" "$QD_SCRIPT" | cut -d: -f1)

if [ -n "$menu_start" ] && [ -n "$menu_end" ]; then
    menu_lines=$((menu_end - menu_start))
    echo "✅ 菜单函数长度: $menu_lines 行"
    
    if [ "$menu_lines" -lt 50 ]; then
        echo "✅ 菜单代码简洁，优化效果良好"
    else
        echo "⚠️  菜单代码较长，可能需要进一步优化"
    fi
else
    echo "⚠️  无法计算菜单长度"
fi

# 检查左右分栏实现
echo "🔍 检查左右分栏实现..."
if grep -q "print_menu_item_columns.*启动公共服务.*关闭公共服务" "$QD_SCRIPT"; then
    echo "✅ 左右分栏布局实现正确"
else
    echo "❌ 左右分栏布局实现有问题"
    exit 1
fi

echo ""
echo "=========================================="
echo "✅ QD脚本菜单优化验证完成！"
echo "=========================================="
echo ""
echo "优化总结："
echo "1. ✅ 脚本语法正确"
echo "2. ✅ 关键函数完整"
echo "3. ✅ 菜单项完整"
echo "4. ✅ 重复函数已清理"
echo "5. ✅ Unicode字符正确"
echo "6. ✅ 颜色定义完整"
echo "7. ✅ 左右分栏实现"
echo ""
echo "🎉 菜单优化成功！可以投入使用。"
echo ""
echo "使用方法："
echo "cd centos7.6_game_server/bin/qd"
echo "./qd"
echo ""
echo "注意事项："
echo "1. 确保在支持Unicode的终端中运行"
echo "2. 建议终端宽度至少80字符"
echo "3. 所有原有功能保持不变"
