#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的游戏服务器测试
"""

import subprocess
import time
import os
from datetime import datetime

def log(message):
    """输出带时间戳的日志"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")

def test_game_server():
    """测试游戏服务器"""
    log("🎮 开始游戏服务器测试...")
    
    # 1. 检查Java进程
    try:
        result = subprocess.run(['powershell', '-Command', 'Get-Process java -ErrorAction SilentlyContinue'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and result.stdout.strip():
            log("✅ Java进程正在运行")
            print(result.stdout)
        else:
            log("❌ 没有发现Java进程")
    except Exception as e:
        log(f"⚠️  检查Java进程失败: {e}")
    
    # 2. 检查Python进程
    try:
        result = subprocess.run(['powershell', '-Command', 'Get-Process python -ErrorAction SilentlyContinue'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and result.stdout.strip():
            log("✅ Python进程正在运行")
            print(result.stdout)
        else:
            log("❌ 没有发现Python进程")
    except Exception as e:
        log(f"⚠️  检查Python进程失败: {e}")
    
    # 3. 检查端口状态
    try:
        result = subprocess.run(['powershell', '-Command', 'netstat -an | findstr "LISTEN" | findstr "4200\\|4300\\|4100"'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and result.stdout.strip():
            log("✅ 发现监听端口:")
            print(result.stdout)
        else:
            log("❌ 没有发现相关端口监听")
    except Exception as e:
        log(f"⚠️  检查端口失败: {e}")
    
    # 4. 检查游戏日志
    log_file = "game/server1/game_server/gs.log"
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                if lines:
                    log(f"✅ 游戏日志文件存在，共{len(lines)}行")
                    log("📝 最新日志内容:")
                    for line in lines[-5:]:
                        if line.strip():
                            print(f"   {line.strip()}")
                else:
                    log("⚠️  游戏日志文件为空")
        except Exception as e:
            log(f"⚠️  读取游戏日志失败: {e}")
    else:
        log("❌ 游戏日志文件不存在")
    
    # 5. 尝试启动游戏服务器
    log("🚀 尝试启动游戏服务器...")
    try:
        game_dir = "game/server1/game_server"
        if os.path.exists(game_dir):
            os.chdir(game_dir)
            log(f"📁 切换到目录: {os.getcwd()}")
            
            # 启动命令
            cmd = [
                'java', 
                '-server',
                '-Dlog4j.configurationFile=log4j2.xml',
                '-Xms512m', '-Xmx1024m', '-Xmn256m',
                '-jar', 'gsxdb.jar',
                '-rmiport', '41001'
            ]
            
            log(f"🎯 启动命令: {' '.join(cmd)}")
            
            # 启动进程
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
            log(f"✅ 游戏服务器已启动 (PID: {process.pid})")
            
            # 监控启动过程
            start_time = time.time()
            while time.time() - start_time < 30:  # 最多等待30秒
                if process.poll() is not None:
                    log(f"❌ 进程已退出，退出码: {process.returncode}")
                    break
                
                # 读取输出
                try:
                    line = process.stdout.readline()
                    if line:
                        print(f"[GAME] {line.strip()}")
                        if "SERVER STARTING" in line:
                            log("🎉 服务器开始启动!")
                        elif "初始化完成" in line or "启动完成" in line:
                            log("🎉 服务器启动完成!")
                            break
                except:
                    pass
                
                time.sleep(0.1)
            
            if process.poll() is None:
                log("✅ 游戏服务器正在运行中...")
                return True
            else:
                log("❌ 游戏服务器启动失败")
                return False
                
        else:
            log(f"❌ 游戏目录不存在: {game_dir}")
            return False
            
    except Exception as e:
        log(f"❌ 启动游戏服务器失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🎮 游戏服务器简单测试工具")
    print("=" * 60)
    
    success = test_game_server()
    
    if success:
        print("\n🎉 测试完成: 游戏服务器运行正常!")
    else:
        print("\n❌ 测试完成: 发现问题")
    
    print("=" * 60)
