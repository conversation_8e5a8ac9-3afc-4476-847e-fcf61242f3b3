#!/bin/bash
# 测试菜单显示效果

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly WHITE='\033[1;37m'
readonly BOLD='\033[1m'
readonly NC='\033[0m'

# 打印左右分栏菜单项
print_menu_item_columns() {
    local left_key=$1
    local left_desc=$2
    local left_color=$3
    local right_key=$4
    local right_desc=$5
    local right_color=$6

    # 计算左侧菜单项格式 (去掉emoji长度影响)
    local left_key_len=${#left_key}
    local left_desc_clean=$(echo "$left_desc" | sed 's/[🔴🟢▶️⏹️🧹🤖🚀⛔📊📋⚙️📦💾🗑️🧽🔧🔍🎮🆕🔄🛠️❓💡👉]//g')
    local left_desc_len=${#left_desc_clean}
    local left_padding=$(( 10 - left_key_len ))
    local left_text=$(printf "${left_color}[%s]%${left_padding}s${NC} %s" "$left_key" "" "$left_desc")
    
    # 计算左侧实际显示长度（不包含颜色代码）
    local left_display_len=$((left_key_len + left_desc_len + 13))
    local left_spaces=$(( 38 - left_display_len ))
    if [ $left_spaces -lt 1 ]; then
        left_spaces=1
    fi

    # 如果有右侧菜单项，则显示两列
    if [ -n "$right_key" ]; then
        local right_key_len=${#right_key}
        local right_padding=$(( 10 - right_key_len ))
        local right_text=$(printf "${right_color}[%s]%${right_padding}s${NC} %s" "$right_key" "" "$right_desc")
        printf "│ %s%${left_spaces}s %s │\n" "$left_text" "" "$right_text"
    else
        # 只有左侧菜单项时，居中显示
        printf "│ %s%${left_spaces}s                                    │\n" "$left_text" ""
    fi
}

# 主菜单显示
show_menu() {
    clear
    
    # 主标题
    printf "${BOLD}${PURPLE}"
    printf "╔════════════════════════════════════════════════════════════════════════╗\n"
    printf "║                    梦乐园多区管理脚本 v2.0.0                          ║\n"
    printf "║                 可视化动态指令菜单 - 威少专用独家制作                  ║\n"
    printf "╚════════════════════════════════════════════════════════════════════════╝\n"
    printf "${NC}\n"
    
    # 区服管理 - 使用左右分栏布局
    printf "${GREEN}┌─ 🖥️  区服管理 ─────────────────────────────────────────────────────────┐${NC}\n"
    print_menu_item_columns "0" "🟢 启动公共服务" "${GREEN}" "00" "🔴 关闭公共服务" "${GREEN}"
    print_menu_item_columns "1-99" "▶️  启动对应大区" "${GREEN}" "01-99" "⏹️  关闭对应大区" "${GREEN}"
    print_menu_item_columns "gbA" "⏹️  关闭所有大区" "${GREEN}" "gbAF" "🧹 强制关闭+清理" "${GREEN}"
    printf "${GREEN}└────────────────────────────────────────────────────────────────────────┘${NC}\n\n"
    
    # Robot测试系统 - 使用左右分栏布局
    printf "${PURPLE}┌─ 🤖 Robot测试系统 ─────────────────────────────────────────────────────┐${NC}\n"
    print_menu_item_columns "robot" "🚀 启动Robot测试" "${PURPLE}" "robotstop" "⛔ 停止Robot测试" "${PURPLE}"
    print_menu_item_columns "robotstatus" "📊 查看Robot状态" "${PURPLE}" "robotlog" "📋 查看Robot日志" "${PURPLE}"
    print_menu_item_columns "robotconfig" "⚙️  Robot配置管理" "${PURPLE}" "robotbatch" "📦 Robot批量管理" "${PURPLE}"
    printf "${PURPLE}└────────────────────────────────────────────────────────────────────────┘${NC}\n\n"
    
    # 数据管理 - 使用左右分栏布局
    printf "${RED}┌─ 💾 数据管理 ──────────────────────────────────────────────────────────┐${NC}\n"
    print_menu_item_columns "SD" "🗑️  单区删档" "${RED}" "SDA" "🗑️  全区删档" "${RED}"
    print_menu_item_columns "cleanLog" "🧽 清理日志" "${RED}" "rmdblog" "🧽 清理数据日志" "${RED}"
    print_menu_item_columns "packdb" "📦 备份数据" "${RED}" "" "" ""
    printf "${RED}└────────────────────────────────────────────────────────────────────────┘${NC}\n\n"
    
    # 系统工具 - 使用左右分栏布局
    printf "${YELLOW}┌─ 🔧 系统工具 ──────────────────────────────────────────────────────────┐${NC}\n"
    print_menu_item_columns "dk" "🔍 查看端口状态" "${YELLOW}" "gm" "🎮 GM命令调试" "${YELLOW}"
    print_menu_item_columns "kxq" "🆕 开新区" "${YELLOW}" "gx" "🔄 更新所有大区" "${YELLOW}"
    print_menu_item_columns "bt" "🛠️  安装宝塔" "${YELLOW}" "jc" "⚙️  安装基础环境" "${YELLOW}"
    print_menu_item_columns "help" "❓ 显示帮助" "${CYAN}" "" "" ""
    printf "${YELLOW}└────────────────────────────────────────────────────────────────────────┘${NC}\n\n"
    
    printf "${BOLD}${CYAN}💡 提示：输入对应指令即可执行相应功能${NC}\n"
    printf "${BOLD}${YELLOW}👉 请输入指令：${NC} "
}

# 显示菜单
show_menu
