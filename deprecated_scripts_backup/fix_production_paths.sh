#!/bin/bash
#==============================================================================
# 生产环境路径修复脚本
# 版本: 1.0
# 创建时间: 2025-01-29
# 描述: 自动修复生产环境中的路径问题
#==============================================================================

set -euo pipefail

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 查找游戏目录
find_game_directory() {
    local possible_dirs=(
        "/home/<USER>"
        "$HOME/game"
        "$(pwd)/game"
        "$(pwd)/centos7.6_game_server/game"
        "/opt/game"
        "/usr/local/game"
    )
    
    for dir in "${possible_dirs[@]}"; do
        if [ -d "$dir" ] && [ -d "$dir/server1" ]; then
            echo "$dir"
            return 0
        fi
    done
    
    return 1
}

# 查找qd脚本
find_qd_script() {
    local possible_paths=(
        "./qd"
        "./bin/qd"
        "/home/<USER>/bin/qd"
        "$HOME/bin/qd"
        "$(pwd)/bin/qd"
        "$(pwd)/centos7.6_game_server/bin/qd"
    )
    
    for path in "${possible_paths[@]}"; do
        if [ -f "$path" ]; then
            echo "$path"
            return 0
        fi
    done
    
    return 1
}

# 修复qd脚本中的路径
fix_qd_script_paths() {
    local qd_script="$1"
    local game_dir="$2"
    
    log_info "修复qd脚本中的路径配置..."
    
    # 备份原文件
    cp "$qd_script" "${qd_script}.backup.$(date +%Y%m%d_%H%M%S)"
    log_info "已备份原文件: ${qd_script}.backup.$(date +%Y%m%d_%H%M%S)"
    
    # 修复GAME_HOME路径
    sed -i "s|readonly GAME_HOME=.*|readonly GAME_HOME=\"$game_dir\"|g" "$qd_script"
    
    # 修复LOG_FILE路径
    local log_dir="$(dirname "$game_dir")"
    sed -i "s|readonly LOG_FILE=.*|readonly LOG_FILE=\"$log_dir/script.log\"|g" "$qd_script"
    
    log_info "已更新qd脚本中的路径配置"
    log_info "GAME_HOME: $game_dir"
    log_info "LOG_FILE: $log_dir/script.log"
}

# 创建必要的目录结构
create_directory_structure() {
    local game_dir="$1"
    
    log_info "检查并创建必要的目录结构..."
    
    # 创建基础目录
    mkdir -p "$game_dir" 2>/dev/null || true
    
    # 检查是否至少有一个大区
    local has_server=false
    for i in {1..9}; do
        if [ -d "$game_dir/server$i" ]; then
            has_server=true
            break
        fi
    done
    
    if [ "$has_server" = false ]; then
        log_warn "未找到任何大区目录"
        log_info "如果您的大区目录在其他位置，请创建软链接："
        echo "  例如: ln -s /actual/server/path $game_dir/server1"
    fi
}

# 设置权限
set_permissions() {
    local qd_script="$1"
    local game_dir="$2"
    
    log_info "设置正确的权限..."
    
    # 设置qd脚本权限
    chmod +x "$qd_script" 2>/dev/null || true
    
    # 设置游戏目录权限
    if [ -d "$game_dir" ]; then
        chmod -R 755 "$game_dir" 2>/dev/null || true
    fi
    
    # 设置其他脚本权限
    local script_dir=$(dirname "$qd_script")
    for script in "$script_dir"/*.sh; do
        if [ -f "$script" ]; then
            chmod +x "$script" 2>/dev/null || true
        fi
    done
}

# 测试修复结果
test_fix() {
    local qd_script="$1"
    
    log_info "测试修复结果..."
    
    # 测试语法
    if bash -n "$qd_script" 2>/dev/null; then
        log_info "✓ qd脚本语法正确"
    else
        log_error "✗ qd脚本语法错误"
        return 1
    fi
    
    # 测试基本功能
    local script_dir=$(dirname "$qd_script")
    cd "$script_dir" || return 1
    
    if timeout 5 ./$(basename "$qd_script") help &>/dev/null; then
        log_info "✓ qd脚本帮助功能正常"
    else
        log_warn "⚠ qd脚本帮助功能可能有问题"
    fi
    
    if timeout 5 ./$(basename "$qd_script") dk &>/dev/null; then
        log_info "✓ qd脚本端口检查功能正常"
    else
        log_warn "⚠ qd脚本端口检查功能可能有问题"
    fi
}

# 显示使用说明
show_usage_instructions() {
    log_info "=== 使用说明 ==="
    echo
    echo "修复完成后，您可以使用以下命令："
    echo
    echo "1. 检查端口状态:"
    echo "   ./qd dk"
    echo
    echo "2. 显示帮助信息:"
    echo "   ./qd help"
    echo
    echo "3. 启动大区1:"
    echo "   ./qd 1"
    echo
    echo "4. 关闭大区1:"
    echo "   ./qd 01"
    echo
    echo "5. 查看所有可用命令:"
    echo "   ./qd"
    echo
}

# 主函数
main() {
    echo -e "${BLUE}=== 生产环境路径修复工具 ===${NC}"
    echo -e "${BLUE}修复时间: $(date)${NC}"
    echo
    
    # 查找游戏目录
    log_info "查找游戏目录..."
    local game_dir
    if game_dir=$(find_game_directory); then
        log_info "找到游戏目录: $game_dir"
    else
        log_error "未找到游戏目录"
        echo
        echo "请确保以下目录之一存在并包含server1子目录："
        echo "  - /home/<USER>"
        echo "  - $HOME/game"
        echo "  - $(pwd)/game"
        echo "  - $(pwd)/centos7.6_game_server/game"
        echo
        echo "或者手动创建软链接："
        echo "  ln -s /your/actual/game/path /home/<USER>"
        exit 1
    fi
    
    # 查找qd脚本
    log_info "查找qd脚本..."
    local qd_script
    if qd_script=$(find_qd_script); then
        log_info "找到qd脚本: $qd_script"
    else
        log_error "未找到qd脚本"
        echo
        echo "请确保qd脚本存在于以下位置之一："
        echo "  - ./qd"
        echo "  - ./bin/qd"
        echo "  - /home/<USER>/bin/qd"
        echo "  - $(pwd)/bin/qd"
        echo "  - $(pwd)/centos7.6_game_server/bin/qd"
        exit 1
    fi
    
    # 修复路径
    fix_qd_script_paths "$qd_script" "$game_dir"
    
    # 创建目录结构
    create_directory_structure "$game_dir"
    
    # 设置权限
    set_permissions "$qd_script" "$game_dir"
    
    # 测试修复结果
    test_fix "$qd_script"
    
    # 显示使用说明
    show_usage_instructions
    
    echo -e "${GREEN}修复完成！${NC}"
    echo
    echo -e "${YELLOW}现在您可以尝试运行: $(dirname "$qd_script")/$(basename "$qd_script") 1${NC}"
}

# 执行主函数
main "$@"
