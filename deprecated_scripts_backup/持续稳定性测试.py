#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏服务器持续稳定性测试
监控运行状态、错误日志、性能指标
"""

import subprocess
import time
import os
import re
import threading
from datetime import datetime
from collections import defaultdict

class GameServerMonitor:
    def __init__(self):
        self.game_dir = "game/server1/game_server"
        self.log_file = os.path.join(self.game_dir, "gs.log")
        self.running = True
        self.error_count = defaultdict(int)
        self.last_log_position = 0
        self.start_time = time.time()
        
    def log(self, message, level="INFO"):
        """输出带时间戳的日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        prefix = {
            "INFO": "📊",
            "WARN": "⚠️ ",
            "ERROR": "❌",
            "SUCCESS": "✅"
        }.get(level, "📝")
        print(f"[{timestamp}] {prefix} {message}")
    
    def check_java_process(self):
        """检查Java进程状态"""
        try:
            result = subprocess.run(['powershell', '-Command', 
                'Get-Process java -ErrorAction SilentlyContinue | Select-Object Id,CPU,WorkingSet'], 
                capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                if len(lines) > 2:  # 有数据行
                    for line in lines[2:]:  # 跳过标题行
                        if line.strip():
                            parts = line.split()
                            if len(parts) >= 3:
                                pid = parts[0]
                                cpu = parts[1] if parts[1] != '' else '0'
                                memory = parts[2] if parts[2] != '' else '0'
                                return {
                                    'status': 'running',
                                    'pid': pid,
                                    'cpu': cpu,
                                    'memory': memory
                                }
            return {'status': 'stopped'}
        except Exception as e:
            self.log(f"检查Java进程失败: {e}", "ERROR")
            return {'status': 'error'}
    
    def check_port_status(self):
        """检查端口状态"""
        try:
            result = subprocess.run(['powershell', '-Command', 
                'netstat -an | findstr "LISTEN" | findstr "4100\\|4200\\|4300"'], 
                capture_output=True, text=True, timeout=10)
            
            ports = []
            if result.returncode == 0 and result.stdout.strip():
                for line in result.stdout.strip().split('\n'):
                    if ':41001' in line:
                        ports.append('41001 (RMI)')
                    elif ':42001' in line:
                        ports.append('42001 (Gate)')
                    elif ':43001' in line:
                        ports.append('43001 (Provider)')
            
            return ports
        except Exception as e:
            self.log(f"检查端口状态失败: {e}", "ERROR")
            return []
    
    def analyze_log_errors(self, content):
        """分析日志中的错误"""
        error_patterns = [
            (r'ERROR.*?(\w+Exception)', '异常错误'),
            (r'WARN.*?Connect Abort.*?(\d+\.\d+\.\d+\.\d+:\d+)', '连接中断'),
            (r'ERROR.*?载入.*?失败', '配置加载失败'),
            (r'Exception.*?at.*?(\w+\.\w+\.\w+)', 'Java异常'),
            (r'FATAL.*?(\w+)', '致命错误'),
            (r'OutOfMemoryError', '内存溢出'),
            (r'StackOverflowError', '栈溢出'),
            (r'ClassNotFoundException.*?(\w+\.\w+\.\w+)', '类未找到'),
        ]
        
        errors = []
        for pattern, error_type in error_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                error_key = f"{error_type}: {match}"
                self.error_count[error_key] += 1
                errors.append(error_key)
        
        return errors
    
    def monitor_log_file(self):
        """监控日志文件"""
        if not os.path.exists(self.log_file):
            self.log("日志文件不存在", "ERROR")
            return []
        
        try:
            with open(self.log_file, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(self.last_log_position)
                new_content = f.read()
                self.last_log_position = f.tell()
                
                if new_content.strip():
                    # 分析新的日志内容
                    errors = self.analyze_log_errors(new_content)
                    
                    # 显示最新的几行日志
                    lines = new_content.strip().split('\n')
                    recent_lines = [line for line in lines[-5:] if line.strip()]
                    
                    return {
                        'new_lines': recent_lines,
                        'errors': errors,
                        'content_length': len(new_content)
                    }
                
                return {'new_lines': [], 'errors': [], 'content_length': 0}
                
        except Exception as e:
            self.log(f"读取日志文件失败: {e}", "ERROR")
            return {'new_lines': [], 'errors': [], 'content_length': 0}
    
    def get_system_stats(self):
        """获取系统统计信息"""
        uptime = time.time() - self.start_time
        hours = int(uptime // 3600)
        minutes = int((uptime % 3600) // 60)
        seconds = int(uptime % 60)
        
        return {
            'uptime': f"{hours:02d}:{minutes:02d}:{seconds:02d}",
            'total_errors': sum(self.error_count.values()),
            'error_types': len(self.error_count)
        }
    
    def print_status_report(self, java_status, ports, log_info, stats):
        """打印状态报告"""
        print("\n" + "="*80)
        print(f"🎮 游戏服务器稳定性监控报告 - 运行时间: {stats['uptime']}")
        print("="*80)
        
        # Java进程状态
        if java_status['status'] == 'running':
            self.log(f"Java进程运行正常 (PID: {java_status['pid']}, CPU: {java_status['cpu']}, 内存: {java_status['memory']})", "SUCCESS")
        else:
            self.log(f"Java进程状态异常: {java_status['status']}", "ERROR")
        
        # 端口状态
        if ports:
            self.log(f"监听端口: {', '.join(ports)}", "SUCCESS")
        else:
            self.log("没有发现监听端口", "WARN")
        
        # 日志状态
        if log_info['content_length'] > 0:
            self.log(f"日志活跃 (新增 {log_info['content_length']} 字符)", "SUCCESS")
        else:
            self.log("日志无新内容", "INFO")
        
        # 错误统计
        if log_info['errors']:
            self.log(f"发现 {len(log_info['errors'])} 个新错误", "WARN")
            for error in log_info['errors'][:3]:  # 只显示前3个
                print(f"   ⚠️  {error}")
        
        self.log(f"累计错误: {stats['total_errors']} 个 ({stats['error_types']} 种类型)", "INFO")
        
        # 最新日志
        if log_info['new_lines']:
            print("\n📝 最新日志内容:")
            for line in log_info['new_lines']:
                if 'ERROR' in line:
                    print(f"   ❌ {line}")
                elif 'WARN' in line:
                    print(f"   ⚠️  {line}")
                else:
                    print(f"   📄 {line}")
        
        print("="*80)
    
    def print_error_summary(self):
        """打印错误汇总"""
        if self.error_count:
            print("\n📊 错误统计汇总:")
            print("-" * 60)
            for error, count in sorted(self.error_count.items(), key=lambda x: x[1], reverse=True):
                print(f"   {count:3d}x {error}")
            print("-" * 60)
    
    def run_continuous_test(self, duration_minutes=30, check_interval=30):
        """运行持续测试"""
        self.log(f"开始持续稳定性测试 (持续 {duration_minutes} 分钟，每 {check_interval} 秒检查一次)")
        
        end_time = time.time() + (duration_minutes * 60)
        check_count = 0
        
        try:
            while time.time() < end_time and self.running:
                check_count += 1
                
                # 检查各项状态
                java_status = self.check_java_process()
                ports = self.check_port_status()
                log_info = self.monitor_log_file()
                stats = self.get_system_stats()
                
                # 打印状态报告
                self.print_status_report(java_status, ports, log_info, stats)
                
                # 如果Java进程停止，立即报告
                if java_status['status'] != 'running':
                    self.log("⚠️  检测到Java进程异常，建议检查！", "ERROR")
                    break
                
                # 等待下次检查
                if time.time() < end_time:
                    self.log(f"等待 {check_interval} 秒后进行下次检查... (第 {check_count} 次检查)")
                    time.sleep(check_interval)
            
            # 测试完成总结
            self.log(f"持续测试完成！共进行了 {check_count} 次检查", "SUCCESS")
            self.print_error_summary()
            
        except KeyboardInterrupt:
            self.log("测试被用户中断", "WARN")
        except Exception as e:
            self.log(f"测试过程中发生错误: {e}", "ERROR")

def main():
    print("🎮 游戏服务器持续稳定性测试工具")
    print("=" * 60)
    
    monitor = GameServerMonitor()
    
    # 运行30分钟的持续测试，每30秒检查一次
    monitor.run_continuous_test(duration_minutes=30, check_interval=30)

if __name__ == "__main__":
    main()
