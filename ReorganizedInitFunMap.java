// 重新整理的InitFunMap方法 - 按技能公式内容规律分类
public class ReorganizedInitFunMap {

    public static void InitFunMap() {

        // ========================================
        // 1. 基础属性计算公式 (Basic Attribute Formulas)
        // ========================================

        // 1.1 品质相关 (Quality Related)
        funMap.put("with(Math){ return quality;}", 5);
        funMap.put("with(Math){ return quality*0.6+10;}", 3);
        funMap.put("with(Math){ return quality*3*(havebuffa(508042)?(1.2):(1));}", 10);
        funMap.put("with(Math){ return quality*5+50;}", 20);
        funMap.put("with(Math){ return quality*12+150;}", 19);
        funMap.put("with(Math){ return quality*60+2000;}", 2);
        funMap.put("with(Math){ return -quality*3;}", 13);
        funMap.put("with(Math){ return (quality*5+100)*(havebuffa(508042)?(1.2):(1));}", 11);

        // 1.2 等级相关 (Level Related)
        funMap.put("with(Math){ return gradea*0.00105;}", 197);
        funMap.put("with(Math){ return gradea*0.001225;}", 198);
        funMap.put("with(Math){ return gradea*0.0014;}", 199);
        funMap.put("with(Math){ return gradea*0.05;}", 212);
        funMap.put("with(Math){ return gradea*0.075;}", 216);
        funMap.put("with(Math){ return gradea*0.0875;}", 218);
        funMap.put("with(Math){ return gradea*0.1;}", 182);
        funMap.put("with(Math){ return gradea*0.12;}", 202);
        funMap.put("with(Math){ return gradea*0.14;}", 204);
        funMap.put("with(Math){ return gradea*0.16;}", 181);
        funMap.put("with(Math){ return gradea*0.175;}", 188);
        funMap.put("with(Math){ return gradea*0.189;}", 205);
        funMap.put("with(Math){ return gradea*0.2362;}", 206);
        funMap.put("with(Math){ return gradea*0.3;}", 221);
        funMap.put("with(Math){ return gradea*0.3375;}", 208);
        funMap.put("with(Math){ return gradea*0.375;}", 222);
        funMap.put("with(Math){ return gradea*0.5;}", 213);
        funMap.put("with(Math){ return gradea*0.525;}", 224);
        funMap.put("with(Math){ return gradea*0.625;}", 215);
        funMap.put("with(Math){ return gradea*0.65;}", 219);
        funMap.put("with(Math){ return gradea*0.7;}", 190);
        funMap.put("with(Math){ return gradea*0.75;}", 217);
        funMap.put("with(Math){ return gradea*0.85;}", 220);
        funMap.put("with(Math){ return gradea*0.9;}", 158);
        funMap.put("with(Math){ return gradea*1;}", 201);
        funMap.put("with(Math){ return gradea*1.05;}", 192);
        funMap.put("with(Math){ return gradea*1.1;}", 348);
        funMap.put("with(Math){ return gradea*1.22;}", 193);
        funMap.put("with(Math){ return gradea*1.225;}", 211);
        funMap.put("with(Math){ return gradea*1.3;}", 295);
        funMap.put("with(Math){ return gradea*1.5;}", 251);
        funMap.put("with(Math){ return gradea*2.3;}", 349);
        funMap.put("with(Math){ return gradea*3;}", 161);
        funMap.put("with(Math){ return gradea*3.25;}", 227);
        funMap.put("with(Math){ return gradea*3.5;}", 228);
        funMap.put("with(Math){ return gradea*4;}", 230);
        funMap.put("with(Math){ return gradea*20;}", 159);
        funMap.put("with(Math){ return 3*gradea;}", 161);

        // 1.3 技能等级相关 (Skill Level Related)
        funMap.put("with(Math){ return 0.5*skilllevela;}", 45);
        funMap.put("with(Math){ return 0.6*skilllevela;}", 256);
        funMap.put("with(Math){ return 1*skilllevela;}", 38);
        funMap.put("with(Math){ return 1.4*skilllevela;}", 86);
        funMap.put("with(Math){ return 2*skilllevela;}", 23);
        funMap.put("with(Math){ return 2.1*skilllevela;}", 94);
        funMap.put("with(Math){ return 3*skilllevela;}", 50);
        funMap.put("with(Math){ return 5*skilllevela;}", 261);
        funMap.put("with(Math){ return 6*skilllevela;}", 258);
        funMap.put("with(Math){ return 10*skilllevela;}", 46);
        funMap.put("with(Math){ return 14*skilllevela;}", 101);
        funMap.put("with(Math){ return -2*skilllevela;}", 59);
        funMap.put("with(Math){ return -0.3*skilllevela;}", 323);
        funMap.put("with(Math){ return -0.7*skilllevela;}", 334);
        funMap.put("with(Math){ return -8*skilllevela;}", 327);
        funMap.put("with(Math){ return skilllevela*1;}", 115);

        // 1.4 技能等级条件判断 (Skill Level Conditions)
        funMap.put("with(Math){ return skilllevela>=3;}", 268);
        funMap.put("with(Math){ return skilllevela>=40;}", 27);
        funMap.put("with(Math){ return skilllevela>=50;}", 63);
        funMap.put("with(Math){ return skilllevela>=60;}", 54);
        funMap.put("with(Math){ return skilllevela>=70;}", 29);
        funMap.put("with(Math){ return skilllevela>=90;}", 69);

        // ========================================
        // 2. 伤害计算公式 (Damage Calculation Formulas)
        // ========================================

        // 2.1 物理攻击伤害 (Physical Attack Damage)
        funMap.put("with(Math){ return -(max(phyattacka*1-defendb+1*gradea,phyattacka*0.1)+(havebuffa(509300)?(min(max(defendb-phyattackb,((pve)?(2*gradea):(10))),4*gradea)):(0)));}", 0);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea);}", 1);
        funMap.put("with(Math){ return -(phyattacka*0.4+defenda*0.5-defendb+2*gradea);}", 107);
        funMap.put("with(Math){ return -(phyattacka*0.45-defendb+1*gradea);}", 167);
        funMap.put("with(Math){ return -(phyattacka*0.45-defendb+1*skilllevela);}", 70);
        funMap.put("with(Math){ return -(phyattacka*0.5-defendb+1*gradea);}", 250);
        funMap.put("with(Math){ return -(phyattacka*0.55-defendb+1*gradea);}", 179);
        funMap.put("with(Math){ return -(phyattacka*0.65-defendb+1*skilllevela);}", 30);
        funMap.put("with(Math){ return -(phyattacka*0.7-defendb+1*skilllevela);}", 110);
        funMap.put("with(Math){ return -(phyattacka*0.8-defendb+1*gradea);}", 249);
        funMap.put("with(Math){ return -(phyattacka*0.85-defendb+1*skilllevela);}", 48);
        funMap.put("with(Math){ return -(phyattacka*0.9-defendb+1*skilllevela);}", 26);
        funMap.put("with(Math){ return -(phyattacka*0.95-defendb+1*skilllevela+max(0,speeda-speedb)*0.5);}", 319);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea)*0.5;}", 296);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea)*0.9;}", 297);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea)*(0.45+0.05*skilllevela);}", 39);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*skilllevela);}", 301);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+3*gradea);}", 165);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea+max((phyattacka-phyattackb)*0.05,0));}", 169);
        funMap.put("with(Math){ return -(phyattacka*1-defendb+1*gradea+max((phyattacka-phyattackb)*0.25,0));}", 169);
        funMap.put("with(Math){ return -(phyattacka*1.05-defendb+1*gradea);}", 178);
        funMap.put("with(Math){ return -(phyattacka*1.05-defendb+1*skilllevela+max(0,speeda-speedb)*0.5);}", 318);
        funMap.put("with(Math){ return -(phyattacka*1.05-min(defendb,magicdefb)+1*skilllevela);}", 305);
        funMap.put("with(Math){ return -(phyattacka*1.1-defendb*0.9+2*skilllevela);}", 266);
        funMap.put("with(Math){ return -(phyattacka*1.15-defendb+1*skilllevela);}", 32);
        funMap.put("with(Math){ return -(phyattacka*1.25-defendb+1*skilllevela);}", 33);
        funMap.put("with(Math){ return -(phyattacka*1.25-min(defendb,magicdefb)+1*skilllevela);}", 307);
        funMap.put("with(Math){ return -(phyattacka*1.40-defendb+1*skilllevela)*2.5;}", 146);
        funMap.put("with(Math){ return -(phyattacka*1.45-defendb+1*skilllevela);}", 47);
        funMap.put("with(Math){ return -(phyattacka*1.45-defendb+1*skilllevela)*2.5;}", 147);
        funMap.put("with(Math){ return -(phyattacka*1.50-defendb+1*skilllevela)*2.5;}", 148);
        funMap.put("with(Math){ return -(phyattacka*1.6-defendb+1*gradea);}", 232);
        funMap.put("with(Math){ return -(phyattacka*1.6-defendb+1*skilllevela);}", 253);
        funMap.put("with(Math){ return -(phyattacka*1.6-min(defendb,magicdefb)+1*skilllevela);}", 255);
        funMap.put("with(Math){ return -(phyattacka*2.5-defendb+1*gradea);}", 278);
        funMap.put("with(Math){ return -(phyattacka*10-defendb+1*gradea+max((phyattacka-phyattackb)*0.05,0));}", 143);
        funMap.put("with(Math){ return -(phyattacka*1-min(defendb,magicdefb)+1*skilllevela);}", 308);

        // 2.2 魔法攻击伤害 (Magic Attack Damage)
        funMap.put("with(Math){ return -(magicattacka*0.9-magicdefb+3*skilllevela);}", 111);
        funMap.put("with(Math){ return -(magicattacka*1-magicdefb+2*skilllevela)*0.5;}", 91);
        funMap.put("with(Math){ return -(magicattacka*1-magicdefb+3*skilllevela);}", 113);
        funMap.put("with(Math){ return -(magicattacka*1.1-magicdefb+3*skilllevela);}", 118);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+1.5*gradea)*1.2;}", 299);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela);}", 339);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*0.1;}", 329);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*0.25;}", 328);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*(0.5+0.05*(3-preaimcount));}", 129);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*(0.5+0.05*(4-preaimcount));}", 51);
        funMap.put("with(Math){ return -(magicattacka*1.5-magicdefb+2*skilllevela)*(0.5+0.05*(3-preaimcount))*(havebuffa(506109)?((random()*(1.20-1.1))+1.1):((random()*(1.20-0.9))+0.9));}", 89);
        funMap.put("with(Math){ return -(magicattacka*2-magicdefb+2*skilllevela)*0.5;}", 322);
        funMap.put("with(Math){ return -(magicattacka*2.2-magicdefb+2*gradea)*0.5;}", 162);
        funMap.put("with(Math){ return -(magicattacka*2.5-magicdefb+2*skilllevela)*0.5;}", 79);
        funMap.put("with(Math){ return -(magicattacka*2.5-magicdefb+2*skilllevela)*0.5*(havebuffa(506109)?((random()*(1.20-1.1))+1.1):((random()*(1.20-0.9))+0.9));}", 90);
        funMap.put("with(Math){ return max((magicattacka*2.5-magicdefb+2*skilllevela)*0.5*(havebuffa(506109)?((random()*(1.20-1.1))+1.1):((random()*(1.20-0.9))+0.9)),3*skilllevela);}", 333);

        // 2.3 其他攻击属性 (Other Attack Attributes)
        funMap.put("with(Math){ return magicattacka*0.1;}", 347);
        funMap.put("with(Math){ return magicattacka*10;}", 289);
        funMap.put("with(Math){ return -magicattacka*0.7;}", 262);
        funMap.put("with(Math){ return -magicattacka*1.7;}", 287);
        funMap.put("with(Math){ return -magicattacka*2;}", 152);
        funMap.put("with(Math){ return phyattacka*0.1;}", 254);
        funMap.put("with(Math){ return phyattacka*2-defendb+1*gradea;}", 279);
        funMap.put("with(Math){ return -phyattacka;}", 286);
        funMap.put("with(Math){ return speeda*0.5;}", 263);

        // ========================================
        // 3. 生命值和魔法值相关公式 (HP/MP Related Formulas)
        // ========================================

        // 3.1 生命值相关 (HP Related)
        funMap.put("with(Math){ return maxhpb;}", 128);
        funMap.put("with(Math){ return maxhpb*0.5;}", 131);
        funMap.put("with(Math){ return maxhpb*1;}", 114);
        funMap.put("with(Math){ return 0.3*maxhpb;}", 237);
        funMap.put("with(Math){ return 0.35*maxhpb;}", 236);
        funMap.put("with(Math){ return maxhpa*0.22;}", 284);
        funMap.put("with(Math){ return (maxhpa*0.2)*0.4;}", 282);
        funMap.put("with(Math){ return -maxhpb*0.2;}", 138);
        funMap.put("with(Math){ return -maxhpb*0.4;}", 140);
        funMap.put("with(Math){ return -maxhpb*0.7;}", 142);
        funMap.put("with(Math){ return -maxhpb*2.5;}", 126);
        funMap.put("with(Math){ return -curhpa*1;}", 119);
        funMap.put("with(Math){ return -curhpb;}", 257);
        funMap.put("with(Math){ return -curhpb*0.05;}", 120);
        funMap.put("with(Math){ return -curhpb*0.1;}", 121);
        funMap.put("with(Math){ return -curhpb*0.5;}", 123);
        funMap.put("with(Math){ return -curhpb*0.7;}", 125);
        funMap.put("with(Math){ return -curhpb*0.8;}", 135);
        funMap.put("with(Math){ return min(maxhpb*0.15,gradeb*12);}", 247);
        funMap.put("with(Math){ return min(maxhpb*0.25,gradeb*12);}", 246);
        funMap.put("with(Math){ return min(maxhpb*0.50,gradeb*30);}", 244);
        funMap.put("with(Math){ return -min(0.1*curhpb,20*skilllevela);}", 336);
        funMap.put("with(Math){ return (curhpa/maxhpa)>=0.5;}", 31);
        funMap.put("with(Math){ return survivala<survivalb;}", 270);

        // 3.2 魔法值相关 (MP Related)
        funMap.put("with(Math){ return maxmpb*0.15+250;}", 242);
        funMap.put("with(Math){ return -min(0.05*curmpb,10*skilllevela);}", 337);

        // ========================================
        // 4. 治疗和医疗公式 (Healing and Medical Formulas)
        // ========================================

        funMap.put("with(Math){ return medicala+1*skilllevela;}", 330);
        funMap.put("with(Math){ return medicala+1.2*skilllevela;}", 95);
        funMap.put("with(Math){ return medicala+3*skilllevela;}", 84);
        funMap.put("with(Math){ return medicala+3*skilllevela*2;}", 87);
        funMap.put("with(Math){ return medicala+skilllevela*10;}", 105);
        funMap.put("with(Math){ return medicala*2+skilllevela*10;}", 103);
        funMap.put("with(Math){ return (medicala+1.2*skilllevela)*0.4;}", 96);
        funMap.put("with(Math){ return ((medicala+3*skilllevela)*0.5+abs(maindamage)*0.5)*(1+healrevisea)*(1+medicaljiashena/1000);}", 314);
        funMap.put("with(Math){ return ((medicala*0.2+skilllevela*1.2)+abs(maindamage)*0.08)*(1+healrevisea)*(1+medicaljiashena/1000);}", 76);
        funMap.put("with(Math){ return ((medicala*0.5+skilllevela*1.4)+abs(maindamage)*0.1)*(1+healrevisea)*(1+medicaljiashena/1000);}", 74);

        // ========================================
        // 5. 伤害相关公式 (Damage Related Formulas)
        // ========================================

        funMap.put("with(Math){ return maindamage;}", 75);
        funMap.put("with(Math){ return -maindamage;}", 175);
        funMap.put("with(Math){ return abs(maindamage)*0.20;}", 180);
        funMap.put("with(Math){ return abs(maindamage)*0.35;}", 80);
        funMap.put("with(Math){ return abs(maindamage)*1;}", 81);
        funMap.put("with(Math){ return abs(maindamage*0.2);}", 291);
        funMap.put("with(Math){ return maindamage*0.33*((random()*(1.05-0.95))+0.95);}", 168);

        // ========================================
        // 6. 概率和随机数公式 (Probability and Random Formulas)
        // ========================================

        funMap.put("with(Math){ return ((random()*(3-2))+2);}", 172);
        funMap.put("with(Math){ return randint(1,2);}", 98);
        funMap.put("with(Math){ return 4000+6000*random();}", 402);
        funMap.put("with(Math){ return (15+random()*5)*RoleLv;}", 415);
        funMap.put("with(Math){ return (105+random()*30)*RoleLv;}", 541);
        funMap.put("with(Math){ return (175+random()*50)*RoleLv;}", 475);
        funMap.put("with(Math){ return 0*TeamNum+2;}", 949);
        funMap.put("with(Math){ return 0*TeamNum+4;}", 944);
        funMap.put("with(Math){ return 0*TeamNum+6+2*random();}", 947);
        funMap.put("with(Math){ return 0*TeamNum+8+2*random();}", 948);
        funMap.put("with(Math){ return 1*TeamNum+3;}", 941);
        funMap.put("with(Math){ return 1*TeamNum+4;}", 945);
        funMap.put("with(Math){ return 1*TeamNum+5;}", 943);
        funMap.put("with(Math){ return TeamLv;}", 942);

        // ========================================
        // 7. 复杂计算公式 (Complex Calculation Formulas)
        // ========================================

        // 7.1 技能等级阶段计算 (Skill Level Stage Calculations)
        funMap.put("with(Math){ return 2+min(floor(skilllevela/60),1);}", 145);
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1);}", 124);
        funMap.put("with(Math){ return 4+min(floor(skilllevela/60),1);}", 302);
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),2);}", 272);
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),3);}", 273);
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),4);}", 274);
        funMap.put("with(Math){ return 3+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),5);}", 275);
        funMap.put("with(Math){ return 4+min(floor(skilllevela/60),1)+min(floor(skilllevela/90),1);}", 106);
        funMap.put("with(Math){ return round((1-pow(0.98,skilllevela))*1000);}", 265);

        // 7.2 基础数值计算 (Basic Value Calculations)
        funMap.put("with(Math){ return 10+1.2*gradea;}", 252);
        funMap.put("with(Math){ return 10+1.2*skilllevela;}", 24);
        funMap.put("with(Math){ return 10+2*skilllevela;}", 72);
        funMap.put("with(Math){ return 10+2.4*skilllevela;}", 78);
        funMap.put("with(Math){ return 100+1.2*skilllevela;}", 100);
        funMap.put("with(Math){ return min(-1*gradea+0.5*skilllevela,0);}", 57);
        funMap.put("with(Math){ return min(-2.4*gradea+1.2*skilllevela,0);}", 35);

        // 7.3 Buff相关计算 (Buff Related Calculations)
        funMap.put("with(Math){ return 100*(havebuffa(508042)?(1.2):(1));}", 12);
        funMap.put("with(Math){ return 150*(havebuffa(508042)?(1.2):(1));}", 16);
        funMap.put("with(Math){ return 200*(havebuffa(508042)?(1.2):(1));}", 15);
        funMap.put("with(Math){ return 250*(havebuffa(508042)?(1.2):(1));}", 18);
        funMap.put("with(Math){ return 300*(havebuffa(508042)?(1.2):(1));}", 17);
        funMap.put("with(Math){ return 400*(havebuffa(508042)?(1.2):(1));}", 7);

        // ========================================
        // 8. 条件判断公式 (Conditional Formulas)
        // ========================================

        // 8.1 效果点数判断 (Effect Point Conditions)
        funMap.put("with(Math){ return effectpointa>=0;}", 639);
        funMap.put("with(Math){ return effectpointa<1;}", 659);
        funMap.put("with(Math){ return effectpointa>=2;}", 67);
        funMap.put("with(Math){ return effectpointa>=4;}", 641);

        // 8.2 PVE判断 (PVE Conditions)
        funMap.put("with(Math){ return pve;}", 14);

        // 8.3 概率计算 (Probability Calculations)
        funMap.put("with(Math){ return 0.5+0.001*(skilllevela-gradeb);}", 300);
        funMap.put("with(Math){ return 0.5+0.001*(skilllevela-gradeb)+(enhanceseala-resistsealb);}", 326);
        funMap.put("with(Math){ return 0.6+0.001*(skilllevela-gradeb);}", 310);
        funMap.put("with(Math){ return 0.6+0.002*(skilllevela-gradeb)+(enhanceseala-resistsealb);}", 144);

        // ========================================
        // 9. 封印相关公式 (Seal Related Formulas)
        // ========================================

        funMap.put("with(Math){ return ((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))*0.3;}", 133);
        funMap.put("with(Math){ return ((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))*0.4;}", 132);
        funMap.put("with(Math){ return ((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))+0.05;}", 55);
        funMap.put("with(Math){ return ((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))+0.05+(1-curhpa/maxhpa)*0.1;}", 324);
        funMap.put("with(Math){ return ((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))+(1-curhpa/maxhpa)*0.1;}", 325);
        funMap.put("with(Math){ return ((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16;}", 341);
        funMap.put("with(Math){ return (((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16)*0.2;}", 346);
        funMap.put("with(Math){ return (((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16)*0.4;}", 344);
        funMap.put("with(Math){ return (((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16)*0.5;}", 343);
        funMap.put("with(Math){ return (((sealhita>=unsealb)?(0.98-0.32*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)):(0.66*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)))-0.16)*0.6;}", 342);
        funMap.put("with(Math){ return (sealhita>=unsealb)?(0.98-0.38*pow(0.95,(sealhita/10-unsealb/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)*(0.3+0.05*skilllevela)):(0.6*pow(0.9,(unsealb/10-sealhita/10))+(enhanceseala-resistsealb)+(kongzhijiachenga/1000-kongzhimianyib/1000)*(0.3+0.05*skilllevela));}", 109);

        // ========================================
        // 10. 防御相关公式 (Defense Related Formulas)
        // ========================================

        funMap.put("with(Math){ return -((0.15+0.001*skilllevela)*defendb+1*skilllevela);}", 304);
        funMap.put("with(Math){ return -((0.15+0.001*skilllevela)*magicdefb+1*skilllevela);}", 303);


        // ========================================
        // 11. 经验和金钱计算公式 (Experience and Money Formulas)
        // ========================================

        // 11.1 角色等级相关经验 (Role Level Experience)
        funMap.put("with(Math){ return 100*RoleLv;}", 405);
        funMap.put("with(Math){ return 200*RoleLv;}", 385);
        funMap.put("with(Math){ return 250*RoleLv*(random()*(1.02-0.98)+0.98);}", 401);
        funMap.put("with(Math){ return 500*RoleLv;}", 411);
        funMap.put("with(Math){ return RoleLv*10;}", 388);
        funMap.put("with(Math){ return RoleLv*100;}", 519);
        funMap.put("with(Math){ return RoleLv*100*30/5;}", 586);
        funMap.put("with(Math){ return RoleLv*200;}", 498);
        funMap.put("with(Math){ return RoleLv*1.71+40;}", 456);
        funMap.put("with(Math){ return RoleLv*1.7+20;}", 458);
        funMap.put("with(Math){ return 9.5*RoleLv;}", 369);
        funMap.put("with(Math){ return 1.38*RoleLv*(random()*(1.02-0.98)+0.98);}", 462);
        funMap.put("with(Math){ return 2.22*RoleLv*(random()*(1.02-0.98)+0.98);}", 460);
        funMap.put("with(Math){ return 2.5*RoleLv*(random()*(1.02-0.98)+0.98);}", 461);
        funMap.put("with(Math){ return 3.4*RoleLv*(random()*(1.02-0.98)+0.98);}", 459);

        // 11.2 复杂经验计算 (Complex Experience Calculations)
        funMap.put("with(Math){ return (1*RoleLv*0.94)*(random()*(1.02-0.98)+0.98);}", 423);
        funMap.put("with(Math){ return (1*RoleLv*0.775*2)*(random()*(1.02-0.98)+0.98);}", 421);
        funMap.put("with(Math){ return (1*RoleLv*0.775*2*2)*(random()*(1.02-0.98)+0.98);}", 448);
        funMap.put("with(Math){ return (1*RoleLv*1)*(random()*(1.02-0.98)+0.98);}", 444);
        funMap.put("with(Math){ return (1*RoleLv*1.256*2*2)*(random()*(1.02-0.98)+0.98);}", 446);
        funMap.put("with(Math){ return (1*RoleLv*1.3)*(random()*(1.02-0.98)+0.98);}", 453);
        funMap.put("with(Math){ return (1*RoleLv*2)*(random()*(1.02-0.98)+0.98);}", 449);
        funMap.put("with(Math){ return (1*RoleLv*4)*(random()*(1.02-0.98)+0.98);}", 431);
        funMap.put("with(Math){ return (1*RoleLv*5)*(random()*(1.02-0.98)+0.98);}", 424);
        funMap.put("with(Math){ return (1*RoleLv*6.66)*(random()*(1.02-0.98)+0.98);}", 425);
        funMap.put("with(Math){ return (1*RoleLv*8)*(random()*(1.02-0.98)+0.98);}", 451);

        // 11.3 标准经验计算 (Standard Experience Calculations)
        funMap.put("with(Math){ return StdExp*5/10*(random()*(1.2-0.8)+0.8);}", 511);
        funMap.put("with(Math){ return StdExp*7*2.86/168*8;}", 536);
        funMap.put("with(Math){ return (1+IsSerMul)*(StdExp*5);}", 543);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*6.7);}", 548);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*6.7/10)*((TeamNum-1)*0.05+1);}", 542);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*6.7/15);}", 470);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*6.6667/8*((Ring-1)*0.3+1));}", 504);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+7*IsDbPoint+IsSerMul)*(StdExp*10/63*((Ring-1)*0.05+1));}", 550);
        funMap.put("with(Math){ return (1+IsSerMul)*(StdExp*13.333/28.867*((Ring-1)*0.08+1)+RoleLv*50-1000);}", 481);
        funMap.put("with(Math){ return (1+0.05*IsTL)*(1+IsSerMul)*(StdExp*2.14*7/8*((Ring-1)*0.3+1));}", 487);

        // 11.4 金钱计算 (Money Calculations)
        funMap.put("with(Math){ return (StdMoney*3.15/8*((Ring-1)*0.3+1))*(random()*(1.05-0.95)+0.95);}", 389);
        funMap.put("with(Math){ return (StdMoney*4.2/8*((Ring-1)*0.3+1))*(random()*(1.05-0.95)+0.95);}", 357);
        funMap.put("with(Math){ return (StdMoney*6/28.1*((Ring-1)*0.09+1))*(random()*(1.05-0.95)+0.95);}", 400);
        funMap.put("with(Math){ return (1+14*IsDbPoint+IsSerMul)*(StdMoney*1.5/74*(14*0.09+1))*(random()*(1.2-0.8)+0.8);}", 407);

        // ========================================
        // 12. 战斗相关复杂公式 (Complex Battle Formulas)
        // ========================================

        // 12.1 怪物等级相关 (Monster Level Related)
        funMap.put("with(Math){ return (MonsterLv-30)*0.2+4;}", 437);
        funMap.put("with(Math){ return (1000*MonsterLv*0.1)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 353);
        funMap.put("with(Math){ return (5000*MonsterLv*0.05)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 577);
        funMap.put("with(Math){ return (400*MonsterLv*0.017*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*(1-IsDbPoint)+400*MonsterLv*0.13*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*IsDbPoint)*(random()*(1.02-0.98)+0.98);}", 515);
        funMap.put("with(Math){ return (400*MonsterLv*0.011*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*(1-IsDbPoint)+400*MonsterLv*0.13*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*IsDbPoint)*(random()*(1.02-0.98)+0.98);}", 528);
        funMap.put("with(Math){ return (1000*MonsterLv*0.019*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*IsDbPoint)*(random()*(1.02-0.98)+0.98);}", 363);
        funMap.put("with(Math){ return (5000*MonsterLv*0.011*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*(1-IsDbPoint)+5000*MonsterLv*0.02*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*IsDbPoint)*(random()*(1.02-0.98)+0.98);}", 588);
        funMap.put("with(Math){ return (5000*MonsterLv*0.017*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*(1-IsDbPoint)+5000*MonsterLv*0.02*(MonsterNum*0.083+MasterNum*0.1245)*min(max(1-0.2*floor(abs(MonsterLv-RoleLv)/5),0.1),1)*IsDbPoint)*(random()*(1.02-0.98)+0.98);}", 583);

        // 12.2 团队相关 (Team Related)
        funMap.put("with(Math){ return (1000*TeamLv*0.075*(1-IsDbPoint)+1000*TeamLv*0.12*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 386);
        funMap.put("with(Math){ return (1000*TeamLv*0.038*(0.78+0.04*Ring)*(1-IsDbPoint)+1000*TeamLv*0.098*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 396);
        funMap.put("with(Math){ return (400*TeamLv*0.15*(0.78+0.04*Ring)*(1-IsDbPoint)+400*TeamLv*1.02*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 467);
        funMap.put("with(Math){ return (400*TeamLv*0.3*(1-IsDbPoint)+400*TeamLv*1.244*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 526);
        funMap.put("with(Math){ return (1000*TeamLv*0.15*(0.78+0.04*Ring)*(1-IsDbPoint)+400*TeamLv*1.02*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 514);
        funMap.put("with(Math){ return (500*TeamLv*0.15*(0.78+0.04*Ring)*(1-IsDbPoint)+400*TeamLv*1.02*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 552);
        funMap.put("with(Math){ return (5000*TeamLv*0.025*(0.78+0.04*Ring)*(1-IsDbPoint)+5000*TeamLv*0.102*(0.78+0.04*Ring)*IsDbPoint)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 575);

        // 12.3 PVP相关 (PVP Related)
        funMap.put("with(Math){ return (1*RoleLv*1.256*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 435);
        funMap.put("with(Math){ return (1*RoleLv*0.775*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 427);
        funMap.put("with(Math){ return (1*RoleLv*0.775*2*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 429);
        funMap.put("with(Math){ return (1*RoleLv*0.717*2*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 434);
        funMap.put("with(Math){ return (400*RoleLv*0.628*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 509);
        funMap.put("with(Math){ return (400*RoleLv*0.628*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1))*0.5)*(random()*(1.02-0.98)+0.98);}", 493);
        funMap.put("with(Math){ return (400*RoleLv*0.678*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 505);
        funMap.put("with(Math){ return (400*RoleLv*0.678*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1))*0.5)*(random()*(1.02-0.98)+0.98);}", 468);
        funMap.put("with(Math){ return (400*RoleLv*1.099*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 476);
        funMap.put("with(Math){ return (400*RoleLv*1.099*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1))*0.5)*(random()*(1.02-0.98)+0.98);}", 472);
        funMap.put("with(Math){ return (1000*RoleLv*0.215*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 355);
        funMap.put("with(Math){ return (1000*RoleLv*0.232*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1)))*(random()*(1.02-0.98)+0.98);}", 351);
        funMap.put("with(Math){ return (1000*RoleLv*0.377*(0.95+0.05*(PVPCnt+1))*(0.95+0.05*(PVPTargetCnt+1))*0.5)*(random()*(1.02-0.98)+0.98);}", 390);

        // ========================================
        // 13. 环数和时间相关公式 (Ring and Time Related Formulas)
        // ========================================

        // 13.1 环数相关 (Ring Related)
        funMap.put("with(Math){ return Ring+1;}", 554);
        funMap.put("with(Math){ return Ring+3;}", 553);
        funMap.put("with(Math){ return 400*RoleLv*0.656*(0.82+0.04*((Ring-1)%8+1))*(0.58+0.04*(floor((Ring-1)/8)+1));}", 524);
        funMap.put("with(Math){ return 400*RoleLv*0.875*(0.91+0.02*((Ring-1)%8+1))*(0.58+0.04*(floor((Ring-1)/8)+1));}", 469);
        funMap.put("with(Math){ return (400*RoleLv*0.694*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 495);
        funMap.put("with(Math){ return (2000*RoleLv*0.694*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 507);
        funMap.put("with(Math){ return (400*RoleLv*0.278*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 464);
        funMap.put("with(Math){ return (1000*RoleLv*0.1*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 380);
        funMap.put("with(Math){ return (5000*RoleLv*0.05*(0.78+0.04*Ring))*(random()*(1.02-0.98)+0.98);}", 569);

        // 13.2 时间相关 (Time Related)
        funMap.put("with(Math){ return 1*RoleLv*0.667*2*(3.5+0.5*(floor((Time-1)/5)+1))*floor(1-(Time%5)*0.2)*(random()*(1.02-0.98)+0.98);}", 430);
        funMap.put("with(Math){ return 400*RoleLv*1.215*(0.85+0.03*((Time-1)%9+1))*(0.9+0.1*(floor((Time-1)/9)+1))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 530);
        funMap.put("with(Math){ return 800*RoleLv*1.215*(0.85+0.03*((Time-1)%9+1))*(0.9+0.1*(floor((Time-1)/9)+1))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 484);
        funMap.put("with(Math){ return 400*RoleLv*1*(0.74+0.02*Time)*(random()*(1.02-0.98)+0.98);}", 499);
        funMap.put("with(Math){ return 1600*RoleLv*1*(0.74+0.02*Time)*(random()*(1.02-0.98)+0.98);}", 547);
        funMap.put("with(Math){ return 3200*RoleLv*1*(0.74+0.02*Time)*(random()*(1.02-0.98)+0.98);}", 537);
        funMap.put("with(Math){ return 1000*RoleLv*0.067*(0.74+0.02*Time)*(random()*(1.02-0.98)+0.98);}", 368);
        funMap.put("with(Math){ return 1600*RoleLv*0.067*(0.74+0.02*Time)*(random()*(1.02-0.98)+0.98);}", 395);


        // ========================================
        // 14. 状态检查公式 (Status Check Formulas)
        // ========================================

        // 14.1 数值状态检查 (Numeric Status Checks)
        funMap.put("with(Math){ return _94005_>=1;}", 623);
        funMap.put("with(Math){ return _94006_<3;}", 624);
        funMap.put("with(Math){ return _94007_>=2;}", 626);
        funMap.put("with(Math){ return _94007_>=2&&_94011_>=1;}", 625);
        funMap.put("with(Math){ return _94008_>=1;}", 627);
        funMap.put("with(Math){ return _94009_<1;}", 628);
        funMap.put("with(Math){ return _94014_>=7;}", 858);
        funMap.put("with(Math){ return _94015_>=1;}", 633);
        funMap.put("with(Math){ return _94016_>=1;}", 631);
        funMap.put("with(Math){ return _94017_<1;}", 634);
        funMap.put("with(Math){ return _94023_>=1;}", 601);
        funMap.put("with(Math){ return _94024_>=1;}", 602);
        funMap.put("with(Math){ return _94025_>=1&&_94029_>=1;}", 646);
        funMap.put("with(Math){ return _94025_>=1&&_94030_==2;}", 648);
        funMap.put("with(Math){ return _94025_>=1&&_94030_>=3;}", 647);
        funMap.put("with(Math){ return _94027_>=1&&_94053_<1;}", 609);
        funMap.put("with(Math){ return _94028_>=1;}", 658);
        funMap.put("with(Math){ return _94029_>=1;}", 603);
        funMap.put("with(Math){ return _94030_==1;}", 653);
        funMap.put("with(Math){ return _94030_==2;}", 652);
        funMap.put("with(Math){ return _94030_==2&&_94029_>=1;}", 651);
        funMap.put("with(Math){ return _94031_>=3;}", 607);
        funMap.put("with(Math){ return _94036_>=2;}", 610);
        funMap.put("with(Math){ return _94037_>=1;}", 656);
        funMap.put("with(Math){ return _94038_>=2;}", 657);
        funMap.put("with(Math){ return _94040_<1;}", 611);
        funMap.put("with(Math){ return _94041_>=1;}", 888);
        funMap.put("with(Math){ return _94044_>=1;}", 661);
        funMap.put("with(Math){ return _94044_>=1&&_94057_<1;}", 654);
        funMap.put("with(Math){ return _94056_>=1;}", 822);
        funMap.put("with(Math){ return _94056_>=2;}", 614);
        funMap.put("with(Math){ return _94056_>=4;}", 827);

        // 14.2 95系列状态检查 (95 Series Status Checks)
        funMap.put("with(Math){ return _95001_>=1;}", 662);
        funMap.put("with(Math){ return _95002_==1;}", 663);
        funMap.put("with(Math){ return _95003_<=2;}", 828);
        funMap.put("with(Math){ return _95003_<=4;}", 830);
        funMap.put("with(Math){ return _95003_<=6;}", 832);
        funMap.put("with(Math){ return _95003_<=8;}", 834);
        funMap.put("with(Math){ return _95003_<=9;}", 835);
        funMap.put("with(Math){ return _95003_==4;}", 842);
        funMap.put("with(Math){ return _95003_==6;}", 844);
        funMap.put("with(Math){ return _95003_==7;}", 845);
        funMap.put("with(Math){ return _95003_==9;}", 847);
        funMap.put("with(Math){ return _95010_>=1;}", 666);
        funMap.put("with(Math){ return _95013_==1;}", 665);
        funMap.put("with(Math){ return _95017_==1;}", 668);
        funMap.put("with(Math){ return _95018_==1;}", 700);
        funMap.put("with(Math){ return _95018_<=1;}", 669);
        funMap.put("with(Math){ return _95020_==1;}", 670);
        funMap.put("with(Math){ return _95024_<=0;}", 672);
        funMap.put("with(Math){ return _95030_>=1;}", 675);
        funMap.put("with(Math){ return _95035_>=1;}", 676);
        funMap.put("with(Math){ return _95036_>=1;}", 677);
        funMap.put("with(Math){ return _95037_>=2||_95040_>=1;}", 680);
        funMap.put("with(Math){ return _95038_>=1;}", 679);
        funMap.put("with(Math){ return _95041_>=2;}", 681);
        funMap.put("with(Math){ return _95043_>=3;}", 683);
        funMap.put("with(Math){ return _95046_<=2;}", 686);
        funMap.put("with(Math){ return _95047_>=2;}", 687);
        funMap.put("with(Math){ return _95048_>=1;}", 688);
        funMap.put("with(Math){ return _95049_>=2||_95054_>=1;}", 689);
        funMap.put("with(Math){ return _95050_>=1;}", 690);
        funMap.put("with(Math){ return _95051_>=2;}", 691);
        funMap.put("with(Math){ return _95052_>=1;}", 698);
        funMap.put("with(Math){ return _95053_==1;}", 693);
        funMap.put("with(Math){ return _95055_>=1;}", 695);
        funMap.put("with(Math){ return _95057_==1;}", 701);
        funMap.put("with(Math){ return _95057_>=1;}", 724);
        funMap.put("with(Math){ return _95060_>=1;}", 699);
        funMap.put("with(Math){ return _95065_>=3;}", 707);
        funMap.put("with(Math){ return _95073_==1;}", 703);
        funMap.put("with(Math){ return _95078_<1;}", 713);
        funMap.put("with(Math){ return _95080_==1;}", 714);
        funMap.put("with(Math){ return _95081_<1;}", 716);
        funMap.put("with(Math){ return _95082_>=3;}", 723);
        funMap.put("with(Math){ return _95083_==1;}", 719);
        funMap.put("with(Math){ return _95084_<1;}", 720);
        funMap.put("with(Math){ return _95085_>=2&&_95089_<1;}", 721);
        funMap.put("with(Math){ return _95086_>=1;}", 718);

        // 14.3 96系列状态检查 (96 Series Status Checks)
        funMap.put("with(Math){ return _96001_/_96002_<0.1;}", 877);
        funMap.put("with(Math){ return _96001_/_96002_<0.2;}", 727);
        funMap.put("with(Math){ return _96001_/_96002_>=0.2;}", 726);
        funMap.put("with(Math){ return _96003_>=1;}", 729);
        funMap.put("with(Math){ return _96015_==1;}", 731);
        funMap.put("with(Math){ return _96016_>=1||_96018_>=1;}", 732);
        funMap.put("with(Math){ return _96016_<1&&_96018_<1;}", 733);
        funMap.put("with(Math){ return _96017_<1;}", 734);
        funMap.put("with(Math){ return _96101_==1;}", 744);
        funMap.put("with(Math){ return _96103_>=1;}", 736);
        funMap.put("with(Math){ return _96105_>=1;}", 740);
        funMap.put("with(Math){ return _96106_>=1;}", 738);
        funMap.put("with(Math){ return _96110_>=1;}", 742);
        funMap.put("with(Math){ return _96125_>1;}", 803);
        funMap.put("with(Math){ return _96200_>=4;}", 785);
        funMap.put("with(Math){ return _96201_>=4;}", 786);
        funMap.put("with(Math){ return _96202_>=4;}", 787);
        funMap.put("with(Math){ return _96203_>=4;}", 788);
        funMap.put("with(Math){ return _96207_>=4;}", 792);
        funMap.put("with(Math){ return _96210_<3;}", 795);
        funMap.put("with(Math){ return _96211_>=1;}", 796);
        funMap.put("with(Math){ return _96216_<=3;}", 800);
        funMap.put("with(Math){ return _96217_<3;}", 801);
        funMap.put("with(Math){ return _96301_>=1;}", 752);
        funMap.put("with(Math){ return _96301_>=1&&_96309_<1;}", 756);
        funMap.put("with(Math){ return _96301_>=1&&_96362_<1;}", 759);
        funMap.put("with(Math){ return _96301_>=1&&_96364_<1;}", 761);
        funMap.put("with(Math){ return _96301_>=1&&_96365_<1;}", 762);
        funMap.put("with(Math){ return _96302_>=1;}", 753);
        funMap.put("with(Math){ return _96304_<1;}", 764);
        funMap.put("with(Math){ return _96351_>=1;}", 765);
        funMap.put("with(Math){ return _96351_>=1&&_96359_<1;}", 769);
        funMap.put("with(Math){ return _96351_>=1&&_96360_<1;}", 770);
        funMap.put("with(Math){ return _96351_>=1&&_96362_<1;}", 772);
        funMap.put("with(Math){ return _96351_>=1&&_96366_<1;}", 776);
        funMap.put("with(Math){ return _96352_>=1;}", 766);
        funMap.put("with(Math){ return _96354_<1;}", 777);
        funMap.put("with(Math){ return _96401_==1;}", 778);
        funMap.put("with(Math){ return _96402_>=1||_96403_>=1;}", 779);

        // 14.4 97系列状态检查 (97 Series Status Checks)
        funMap.put("with(Math){ return _97001_/_97002_<0.2;}", 781);
        funMap.put("with(Math){ return _97001_>=4;}", 840);
        funMap.put("with(Math){ return _97004_>=4;}", 783);
        funMap.put("with(Math){ return _97005_==1;}", 784);

        // 14.5 99系列状态检查 (99 Series Status Checks)
        funMap.put("with(Math){ return _99001_<1&&_99004_==1;}", 806);
        funMap.put("with(Math){ return _99001_<1&&_99010_>=1;}", 812);
        funMap.put("with(Math){ return _99001_<1&&_99012_>=1;}", 814);
        funMap.put("with(Math){ return _99003_>=1&&_96124_<=0.01;}", 807);
        funMap.put("with(Math){ return _99032_>=1;}", 809);
        funMap.put("with(Math){ return _99033_>=1;}", 810);
        funMap.put("with(Math){ return _99036_>=1;}", 823);
        funMap.put("with(Math){ return _99037_>=1;}", 824);
        funMap.put("with(Math){ return _99038_>=1;}", 825);
        funMap.put("with(Math){ return _99041_>=3;}", 848);
        funMap.put("with(Math){ return _99043_>=1;}", 850);
        funMap.put("with(Math){ return _99049_>=5;}", 883);
        funMap.put("with(Math){ return _99052_>=1;}", 857);
        funMap.put("with(Math){ return _99053_>=1;}", 859);
        funMap.put("with(Math){ return _99053_<1;}", 868);
        funMap.put("with(Math){ return _99054_>=1;}", 860);
        funMap.put("with(Math){ return _99055_>=1;}", 861);
        funMap.put("with(Math){ return _99055_<1;}", 870);
        funMap.put("with(Math){ return _99056_>=1;}", 862);
        funMap.put("with(Math){ return _99056_<1;}", 871);
        funMap.put("with(Math){ return _99057_>=1;}", 863);
        funMap.put("with(Math){ return _99058_>=1;}", 864);
        funMap.put("with(Math){ return _99059_>=1;}", 865);
        funMap.put("with(Math){ return _99059_<1;}", 874);
        funMap.put("with(Math){ return _99060_<1;}", 875);
        funMap.put("with(Math){ return _99062_>=2;}", 879);
        funMap.put("with(Math){ return _99065_>=4;}", 660);
        funMap.put("with(Math){ return _99070_==1;}", 886);
        funMap.put("with(Math){ return _99071_>=1;}", 885);
        funMap.put("with(Math){ return _99072_<1;}", 887);
        funMap.put("with(Math){ return _99074_>=1;}", 890);

        // ========================================
        // 15. Buff状态检查公式 (Buff Status Check Formulas)
        // ========================================

        funMap.put("with(Math){ return !_13_&&!_501008_;}", 910);
        funMap.put("with(Math){ return !_110_&&!_120_;}", 928);
        funMap.put("with(Math){ return !_501004_;}", 906);
        funMap.put("with(Math){ return !_501010_&&!_13_;}", 905);
        funMap.put("with(Math){ return !_502002_;}", 927);
        funMap.put("with(Math){ return !_503001_;}", 933);
        funMap.put("with(Math){ return !_506003_;}", 901);
        funMap.put("with(Math){ return !_506109_;}", 915);
        funMap.put("with(Math){ return !_506201_;}", 923);
        funMap.put("with(Math){ return !_506306_;}", 922);
        funMap.put("with(Math){ return !_508006_;}", 936);
        funMap.put("with(Math){ return !_508008_;}", 940);
        funMap.put("with(Math){ return !_508014_;}", 939);
        funMap.put("with(Math){ return !_508237_;}", 931);
        funMap.put("with(Math){ return _500033_;}", 899);
        funMap.put("with(Math){ return _501004_;}", 925);
        funMap.put("with(Math){ return _501010_;}", 924);
        funMap.put("with(Math){ return _501402_;}", 909);
        funMap.put("with(Math){ return _501901_;}", 902);
        funMap.put("with(Math){ return _502002_;}", 891);
        funMap.put("with(Math){ return _502003_||_506002_;}", 903);
        funMap.put("with(Math){ return _503002_;}", 913);
        funMap.put("with(Math){ return _504002_;}", 895);
        funMap.put("with(Math){ return _506002_;}", 908);
        funMap.put("with(Math){ return _506109_;}", 919);
        funMap.put("with(Math){ return _506306_;}", 912);
        funMap.put("with(Math){ return _509081_;}", 937);
        funMap.put("with(Math){ return _509082_||_509083_;}", 894);
        funMap.put("with(Math){ return _509082_||_509083_||_506201_||_509068_||_509031_;}", 926);
        funMap.put("with(Math){ return _509201_;}", 929);

        // ========================================
        // 16. 复本和服务器相关公式 (Instance and Server Related Formulas)
        // ========================================

        // 16.1 复本相关 (Instance Related)
        funMap.put("with(Math){ return 400*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.35*(0.5+0.1*Saveid)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 477);
        funMap.put("with(Math){ return 400*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.35*(0.75+0.05*Saveid)*(random()*(1.02-0.98)+0.98);}", 488);
        funMap.put("with(Math){ return 400*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.35*(0.75+0.05*Saveid)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 479);
        funMap.put("with(Math){ return 1000*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.35*(0.5+0.1*Saveid)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 416);
        funMap.put("with(Math){ return 1000*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.35*(0.75+0.05*Saveid)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 379);
        funMap.put("with(Math){ return 5000*min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)*0.058*(0.5+0.1*Saveid)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 571);
        funMap.put("with(Math){ return (1*min(max(RoleLv,FuBenLv),FuBenLv+9)*5)*(random()*(1.02-0.98)+0.98);}", 426);
        funMap.put("with(Math){ return (400*min(max(RoleLv,FuBenLv),FuBenLv+9)*1*(0.7+0.1*Ring))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 551);
        funMap.put("with(Math){ return (400*min(max(RoleLv,FuBenLv),FuBenLv+9)*2*(0.7+0.1*Ring))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15)*(1+0.05*IsTL);}", 539);
        funMap.put("with(Math){ return (1000*min(max(RoleLv,FuBenLv),FuBenLv+9)*0.1*(0.7+0.1*Ring))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 354);
        funMap.put("with(Math){ return (5000*min(max(RoleLv,FuBenLv),FuBenLv+9)*0.2*(0.7+0.1*Ring))*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 578);

        // 16.2 服务器相关 (Server Related)
        funMap.put("with(Math){ return floor((min(max(floor((ServerLv-50)*0.2),0),3)*2+3)*min(max(rolenum*0.0005,1),2));}", 599);
        funMap.put("with(Math){ return min(max(rolenum*0.001,2),4)+(ServerLv-50)*0.2;}", 597);

        // 16.3 特殊计算 (Special Calculations)
        funMap.put("with(Math){ return SwXs*1;}", 568);
        funMap.put("with(Math){ return 2200+floor(RoleLv/10)*440+(200+floor(RoleLv/10)*40*(Ring-1));}", 559);
        funMap.put("with(Math){ return 2200+floor(min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)/10)*440+(200+floor(min(RoleLv,FuBenId*10-1051-floor(FuBenId/113)*5)/10)*40*Saveid);}", 560);
        funMap.put("with(Math){ return 5800+floor(RoleLv/10)*440+(200+floor(RoleLv/10)*40*(Ring-1));}", 563);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,49)/10)*440+(200+floor(min(RoleLv,49)/10)*40*10))*2;}", 562);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,59)/10)*440+(200+floor(min(RoleLv,59)/10)*40*10))*2;}", 564);
        funMap.put("with(Math){ return (2200+floor(min(RoleLv,94)/10)*440+(200+floor(min(RoleLv,94)/10)*40*10))*2;}", 558);

        // 16.4 团队和答题相关 (Team and Answer Related)
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*1;}", 445);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*3;}", 455);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*5;}", 439);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*100;}", 370);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*120;}", 382);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*140;}", 359);
        funMap.put("with(Math){ return (15)+(TeamNum-1)*15+RoleLv*150;}", 365);
        funMap.put("with(Math){ return (50)+(TeamNum-1)*50+RoleLv*5;}", 440);
        funMap.put("with(Math){ return (400*RoleLv*0.25*(0.79+0.02*AnswerCnt))*(random()*(1.02-0.98)+0.98);}", 522);
        funMap.put("with(Math){ return (400*RoleLv*0.25*(0.79+0.02*AnswerCnt))*0.5*(random()*(1.02-0.98)+0.98);}", 494);
        funMap.put("with(Math){ return (400*RoleLv*0.25*(0.89+0.02*AnswerCnt))*(random()*(1.02-0.98)+0.98);}", 497);
        funMap.put("with(Math){ return (400*RoleLv*0.5*(0.79+0.02*AnswerCnt))*0.5*(random()*(1.02-0.98)+0.98);}", 485);
        funMap.put("with(Math){ return (1000*RoleLv*0.05*(0.79+0.02*AnswerCnt))*(random()*(1.02-0.98)+0.98);}", 414);
        funMap.put("with(Math){ return (1000*RoleLv*0.1*(0.79+0.02*AnswerCnt))*0.5*(random()*(1.02-0.98)+0.98);}", 406);
        funMap.put("with(Math){ return (1000*RoleLv*0.1*(0.89+0.02*AnswerCnt))*(random()*(1.02-0.98)+0.98);}", 384);
        funMap.put("with(Math){ return (1000*RoleLv*0.1*(0.89+0.02*AnswerCnt))*0.5*(random()*(1.02-0.98)+0.98);}", 419);

        // ========================================
        // 17. 简单数值计算公式 (Simple Numeric Formulas)
        // ========================================

        funMap.put("with(Math){ return 400*min(RoleLv,49)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 520);
        funMap.put("with(Math){ return 400*min(RoleLv,74)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 500);
        funMap.put("with(Math){ return 400*min(RoleLv,79)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 513);
        funMap.put("with(Math){ return 400*min(RoleLv,89)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 474);
        funMap.put("with(Math){ return 400*min(RoleLv,94)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 508);
        funMap.put("with(Math){ return 400*min(RoleLv,99)*0.35*(0.75+0.05*10)*(random()*(1.02-0.98)+0.98);}", 532);
        funMap.put("with(Math){ return 400*RoleLv*1.215*(0.88+0.03);}", 527);
        funMap.put("with(Math){ return (400*RoleLv*0.017)*(random()*(1.02-0.98)+0.98);}", 535);
        funMap.put("with(Math){ return (400*RoleLv*0.667)*(random()*(1.02-0.98)+0.98)*0.5;}", 512);
        funMap.put("with(Math){ return (400*RoleLv*0.7)*(random()*(1.02-0.98)+0.98);}", 473);
        funMap.put("with(Math){ return (400*RoleLv*0.833)*(random()*(1.02-0.98)+0.98);}", 521);
        funMap.put("with(Math){ return (400*RoleLv*1)*(random()*(1.02-0.98)+0.98);}", 523);
        funMap.put("with(Math){ return (400*RoleLv*1.099*0.6);}", 533);
        funMap.put("with(Math){ return (400*RoleLv*1.2)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 540);
        funMap.put("with(Math){ return (400*RoleLv*1.25)*(random()*(1.02-0.98)+0.98);}", 525);
        funMap.put("with(Math){ return (400*RoleLv*1.4)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 531);
        funMap.put("with(Math){ return (400*RoleLv*1.6)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 516);
        funMap.put("with(Math){ return (400*RoleLv*2.222)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 549);
        funMap.put("with(Math){ return (400*RoleLv*2.5)*(random()*(1.02-0.98)+0.98);}", 517);
        funMap.put("with(Math){ return (500*RoleLv*0.05)*(random()*(1.02-0.98)+0.98)*0.5;}", 594);
        funMap.put("with(Math){ return (1000*RoleLv*0.1)*(random()*(1.02-0.98)+0.98)*0.5;}", 410);
        funMap.put("with(Math){ return (1000*RoleLv*0.2)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 360);
        funMap.put("with(Math){ return (1000*RoleLv*0.232*2)*(random()*(1.02-0.98)+0.98);}", 367);
        funMap.put("with(Math){ return (1000*RoleLv*0.4)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 403);
        funMap.put("with(Math){ return (5000*RoleLv*0.05)*(random()*(1.02-0.98)+0.98)*0.5;}", 591);
        funMap.put("with(Math){ return (5000*RoleLv*0.05)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 574);
        funMap.put("with(Math){ return (5000*RoleLv*0.1)*(random()*(1.02-0.98)+0.98);}", 584);
        funMap.put("with(Math){ return (5000*RoleLv*0.1)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 572);
        funMap.put("with(Math){ return (5000*RoleLv*0.111)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 573);
        funMap.put("with(Math){ return (5000*RoleLv*0.15)*(random()*(1.02-0.98)+0.98)*(1-min(5-TeamNum,2)*0.15);}", 587);

        System.out.println("InitFunMap reorganized with " + funMap.size() + " formulas categorized by content type");
        System.out.println("Categories:");
        System.out.println("1. Basic Attribute Formulas (Quality, Level, Skill Level)");
        System.out.println("2. Damage Calculation Formulas (Physical, Magic)");
        System.out.println("3. HP/MP Related Formulas");
        System.out.println("4. Healing and Medical Formulas");
        System.out.println("5. Damage Related Formulas");
        System.out.println("6. Probability and Random Formulas");
        System.out.println("7. Complex Calculation Formulas");
        System.out.println("8. Conditional Formulas");
        System.out.println("9. Seal Related Formulas");
        System.out.println("10. Defense Related Formulas");
        System.out.println("11. Experience and Money Formulas");
        System.out.println("12. Complex Battle Formulas");
        System.out.println("13. Ring and Time Related Formulas");
        System.out.println("14. Status Check Formulas (94, 95, 96, 97, 99 series)");
        System.out.println("15. Buff Status Check Formulas");
        System.out.println("16. Instance and Server Related Formulas");
        System.out.println("17. Simple Numeric Formulas");
    }
}