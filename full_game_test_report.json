{"test_time": "2025-07-30T18:42:28.863052", "total_tests": 8, "passed_tests": 3, "success_rate": 37.5, "test_results": {"environment_check": true, "qd_script_check": false, "common_services_start": false, "zone1_start": false, "robot_start": false, "port_verification": false, "log_analysis": true, "cleanup": true}, "test_log": ["[18:42:02] [INFO] 🎮 GSXDB完整游戏启动和机器人测试", "[18:42:02] [INFO] 项目路径: E:\\MT3_Projects\\gsxdb_mt3_mly", "[18:42:02] [INFO] 测试时间: 2025-07-30 18:42:02", "[18:42:02] [INFO] 开始 环境检查...", "[18:42:02] [INFO] === 环境检查 ===", "[18:42:02] [INFO] 执行命令: java -version", "[18:42:02] [SUCCESS] 环境检查通过", "[18:42:02] [SUCCESS] 环境检查 完成", "[18:42:02] [INFO] 开始 QD脚本语法检查...", "[18:42:02] [INFO] === QD脚本语法检查 ===", "[18:42:02] [INFO] 执行命令: bash -n E:\\MT3_Projects\\gsxdb_mt3_mly\\centos7.6_game_server\\bin\\qd", "[18:42:02] [ERROR] 原始QD脚本语法错误: 'bash' 不是内部或外部命令，也不是可运行的程序\n或批处理文件。\n", "[18:42:02] [ERROR] QD脚本语法检查 失败", "[18:42:02] [INFO] 开始 公共服务启动...", "[18:42:02] [INFO] === 公共服务启动测试 ===", "[18:42:02] [INFO] 执行命令: bash E:\\MT3_Projects\\gsxdb_mt3_mly\\centos7.6_game_server\\bin\\qd_fixed 0", "[18:42:02] [ERROR] 公共服务启动失败: 'bash' 不是内部或外部命令，也不是可运行的程序\n或批处理文件。\n", "[18:42:02] [ERROR] 公共服务启动 失败", "[18:42:02] [INFO] 开始 大区1启动...", "[18:42:02] [INFO] === 大区1启动测试 ===", "[18:42:02] [INFO] 执行命令: bash E:\\MT3_Projects\\gsxdb_mt3_mly\\centos7.6_game_server\\bin\\qd_fixed 1", "[18:42:02] [ERROR] 大区1启动失败: 'bash' 不是内部或外部命令，也不是可运行的程序\n或批处理文件。\n", "[18:42:02] [ERROR] 大区1启动 失败", "[18:42:02] [INFO] 开始 机器人启动...", "[18:42:02] [INFO] === 机器人启动测试 ===", "[18:42:02] [INFO] 执行命令: bash E:\\MT3_Projects\\gsxdb_mt3_mly\\centos7.6_game_server\\bin\\qd_fixed robot 1 5 1", "[18:42:02] [ERROR] 机器人启动失败: 'bash' 不是内部或外部命令，也不是可运行的程序\n或批处理文件。\n", "[18:42:02] [ERROR] 机器人启动 失败", "[18:42:02] [INFO] 开始 端口验证...", "[18:42:02] [INFO] === 端口验证测试 ===", "[18:42:04] [ERROR] ❌ 名称服务 端口 22200 异常", "[18:42:06] [ERROR] ❌ SDK服务 端口 29200 异常", "[18:42:08] [ERROR] ❌ 大区1 GM 端口 41001 异常", "[18:42:10] [ERROR] ❌ 大区1游戏 端口 43001 异常", "[18:42:10] [ERROR] 端口验证失败", "[18:42:10] [ERROR] 端口验证 失败", "[18:42:10] [INFO] 开始 日志分析...", "[18:42:10] [INFO] === 日志分析测试 ===", "[18:42:10] [WARNING] 未找到游戏日志文件", "[18:42:10] [WARNING] 未找到机器人日志文件", "[18:42:10] [SUCCESS] 日志分析完成", "[18:42:10] [SUCCESS] 日志分析 完成", "[18:42:10] [INFO] 开始 清理测试...", "[18:42:10] [INFO] === 清理测试 ===", "[18:42:10] [INFO] 执行命令: bash E:\\MT3_Projects\\gsxdb_mt3_mly\\centos7.6_game_server\\bin\\qd_fixed robotkill", "[18:42:10] [WARNING] 机器人停止失败", "[18:42:10] [INFO] 执行命令: bash E:\\MT3_Projects\\gsxdb_mt3_mly\\centos7.6_game_server\\bin\\qd_fixed stop-all", "[18:42:10] [WARNING] 服务停止失败", "[18:42:22] [SUCCESS] 端口 22200 已关闭", "[18:42:24] [SUCCESS] 端口 29200 已关闭", "[18:42:26] [SUCCESS] 端口 41001 已关闭", "[18:42:28] [SUCCESS] 端口 43001 已关闭", "[18:42:28] [SUCCESS] 清理完成", "[18:42:28] [SUCCESS] 清理测试 完成", "[18:42:28] [INFO] === 测试总结 ==="], "summary": {"environment": "✅ 通过", "qd_scripts": "❌ 失败", "common_services": "❌ 失败", "zone1_game": "❌ 失败", "robot_system": "❌ 失败", "port_verification": "❌ 失败", "log_analysis": "✅ 通过", "cleanup": "✅ 通过"}}