<?php

$lang['ut_test_name']		= '测试名称';
$lang['ut_test_datatype']	= '测试数据类型';
$lang['ut_res_datatype']	= '预期数据类型';
$lang['ut_result']			= '结果';
$lang['ut_undefined']		= '未定义测试名称';
$lang['ut_file']			= '文件名称';
$lang['ut_line']			= '行号';
$lang['ut_passed']			= '通过';
$lang['ut_failed']			= '失败';
$lang['ut_boolean']			= '布尔值';
$lang['ut_integer']			= '整数';
$lang['ut_float']			= '浮点数';
$lang['ut_double']			= '双精度浮点数'; // can be the same as float
$lang['ut_string']			= '字符串';
$lang['ut_array']			= '数组';
$lang['ut_object']			= '对象';
$lang['ut_resource']		= '资源';
$lang['ut_null']			= '空';
$lang['ut_notes']			= '便笺';


/* End of file unit_test_lang.php */
/* Location: ./system/language/chinesesimplified/unit_test_lang.php */