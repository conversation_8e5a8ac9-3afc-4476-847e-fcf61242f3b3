<?php

$lang['date_year'] = "年";
$lang['date_years'] = "年";
$lang['date_month'] = "月";
$lang['date_months'] = "月";
$lang['date_week'] = "周";
$lang['date_weeks'] = "周";
$lang['date_day'] = "天";
$lang['date_days'] = "天";
$lang['date_hour'] = "小时";
$lang['date_hours'] = "小时";
$lang['date_minute'] = "分";
$lang['date_minutes'] = "分";
$lang['date_second'] = "秒";
$lang['date_seconds'] = "秒";

$lang['UM12']	= '(UTC -12:00) 贝克/豪兰岛';
$lang['UM11']	= '(UTC -11:00) 萨摩亚时区、纽埃';
$lang['UM10']	= '(UTC -10:00) 夏威夷留申标准时间、库克群岛、塔希提岛';
$lang['UM95']	= '(UTC -9:30) 马克萨斯群岛';
$lang['UM9']	= '(UTC -9:00) 阿拉斯加标准时间、甘比尔群岛';
$lang['UM8']	= '(UTC -8:00) 太平洋标准时间、 克利珀顿岛';
$lang['UM7']	= '(UTC -7:00) 山区标准时间';
$lang['UM6']	= '(UTC -6:00) 中部标准时间';
$lang['UM5']	= '(UTC -5:00) 东部标准时间、西加勒比标准时间';
$lang['UM45']	= '(UTC -4:30) 委内瑞拉标准时间';
$lang['UM4']	= '(UTC -4:00) 大西洋标准时间、东加勒比标准时间';
$lang['UM35']	= '(UTC -3:30) 纽芬兰标准时间';
$lang['UM3']	= '(UTC -3:00) 阿根廷、巴西、法属圭亚那、乌拉圭';
$lang['UM2']	= '(UTC -2:00) 南乔治亚/南桑威奇群岛';
$lang['UM1']	= '(UTC -1:00) 亚速尔群岛、佛得角群岛';
$lang['UTC']	= '(UTC) 格林威治标准时间、西欧';
$lang['UP1']	= '(UTC +1:00) 中欧、西非';
$lang['UP2']	= '(UTC +2:00) 中部非洲、东欧、加里宁格勒';
$lang['UP3']	= '(UTC +3:00) 莫斯科、东非';
$lang['UP35']	= '(UTC +3:30) 伊朗标准时间';
$lang['UP4']	= '(UTC +4:00) 阿塞拜疆标准时间、萨马拉';
$lang['UP45']	= '(UTC +4:30) 阿富汗';
$lang['UP5']	= '(UTC +5:00) 巴基斯坦标准时间、叶卡捷琳堡';
$lang['UP55']	= '(UTC +5:30) 印度标准时间、斯里兰卡';
$lang['UP575']	= '(UTC +5:45) 尼泊尔';
$lang['UP6']	= '(UTC +6:00) 孟加拉国标准时间、不丹、鄂木斯克';
$lang['UP65']	= '(UTC +6:30) 科科斯群岛、缅甸';
$lang['UP7']	= '(UTC +7:00) 克拉斯诺亚尔斯克、柬埔寨、老挝、泰国、越南';
$lang['UP8']	= '(UTC +8:00) 澳大利亚西部标准时间、台北、北京、伊尔库茨克';
$lang['UP875']	= '(UTC +8:45) 澳大利亚中部西部标准时间';
$lang['UP9']	= '(UTC +9:00) 日本标准时间、韩国标准时间、雅库茨克时间';
$lang['UP95']	= '(UTC +9:30) 澳大利亚中部标准时间';
$lang['UP10']	= '(UTC +10:00) 澳大利亚东部标准时间、符拉迪沃斯托克';
$lang['UP105']	= '(UTC +10:30) 豪勳爵岛';
$lang['UP11']	= '(UTC +11:00) 马加丹、所罗门群岛、瓦努阿图';
$lang['UP115']	= '(UTC +11:30) 诺福克岛';
$lang['UP12']	= '(UTC +12:00) 斐济、吉尔伯特群岛、堪察加半岛、新西兰标准时间';
$lang['UP1275']	= '(UTC +12:45) 查塔姆群岛标准时间';
$lang['UP13']	= '(UTC +13:00) 菲尼克斯群岛、汤加';
$lang['UP14']	= '(UTC +14:00) 莱恩群岛';

/* End of file date_lang.php */
/* Location: ./system/language/chinesesimplified/date_lang.php */