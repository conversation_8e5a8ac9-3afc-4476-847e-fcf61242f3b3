server{
    
    location ~ .*\.(php|php5)?$ {
        fastcgi_pass   127.0.0.1:9000;
        fastcgi_index  index.php;
        fastcgi_param  SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include        fastcgi_params;
    }
}


server {
        listen 8801;
        server_name ************:8801;
        index index.html index.php;
	root /opt/www_new/mt3/www;

        location / {
                if (!-e $request_filename) {
                        rewrite ^/(.*)$ /index.php?$1 last;
                        break;
                }
        }

	location ~ .*\.(php|php5)?$ {
        # comment try_files $uri =404; to enable pathinfo
        try_files $uri =404;
        fastcgi_pass  unix:/tmp/php-cgi.sock;
        fastcgi_index index.php;
        include fastcgi.conf;
        #include pathinfo.conf;
    }

        error_log /opt/log/api.mt3_error.log;
        access_log /opt/log/api.mt3_access.log;
}
server {
        listen 8802;
        server_name ************:8802;
        index index.html index.php;
        root /opt/www_new/mt3/admin;

        location / {
                if (!-e $request_filename) {
                        rewrite ^/(.*)$ /index.php?$1 last;
                        break;
                }
        }

        location ~ .*\.(php|php5)?$ {
	# comment try_files $uri =404; to enable pathinfo
	try_files $uri =404;
	fastcgi_pass  unix:/tmp/php-cgi.sock;
	fastcgi_index index.php;
	include fastcgi.conf;
	#include pathinfo.conf;
    }

        error_log /opt/log/admin.mt3_error.log;
        access_log /opt/log/admin.mt3_access.log;
}
