<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<title>Flot Examples: Selection and zooming</title>
	<link href="../examples.css" rel="stylesheet" type="text/css">
	<!--[if lte IE 8]><script language="javascript" type="text/javascript" src="../../excanvas.min.js"></script><![endif]-->
	<script language="javascript" type="text/javascript" src="../../jquery.js"></script>
	<script language="javascript" type="text/javascript" src="../../jquery.flot.js"></script>
	<script language="javascript" type="text/javascript" src="../../jquery.flot.selection.js"></script>
	<script type="text/javascript">

	$(function() {

		// setup plot

		function getData(x1, x2) {

			var d = [];
			for (var i = 0; i <= 100; ++i) {
				var x = x1 + i * (x2 - x1) / 100;
				d.push([x, Math.sin(x * Math.sin(x))]);
			}

			return [
				{ label: "sin(x sin(x))", data: d }
			];
		}

		var options = {
			legend: {
				show: false
			},
			series: {
				lines: {
					show: true
				},
				points: {
					show: true
				}
			},
			yaxis: {
				ticks: 10
			},
			selection: {
				mode: "xy"
			}
		};

		var startData = getData(0, 3 * Math.PI);

		var plot = $.plot("#placeholder", startData, options);

		// Create the overview plot

		var overview = $.plot("#overview", startData, {
			legend: {
				show: false
			},
			series: {
				lines: {
					show: true,
					lineWidth: 1
				},
				shadowSize: 0
			},
			xaxis: {
				ticks: 4
			},
			yaxis: {
				ticks: 3,
				min: -2,
				max: 2
			},
			grid: {
				color: "#999"
			},
			selection: {
				mode: "xy"
			}
		});

		// now connect the two

		$("#placeholder").bind("plotselected", function (event, ranges) {

			// clamp the zooming to prevent eternal zoom

			if (ranges.xaxis.to - ranges.xaxis.from < 0.00001) {
				ranges.xaxis.to = ranges.xaxis.from + 0.00001;
			}

			if (ranges.yaxis.to - ranges.yaxis.from < 0.00001) {
				ranges.yaxis.to = ranges.yaxis.from + 0.00001;
			}

			// do the zooming

			plot = $.plot("#placeholder", getData(ranges.xaxis.from, ranges.xaxis.to),
				$.extend(true, {}, options, {
					xaxis: { min: ranges.xaxis.from, max: ranges.xaxis.to },
					yaxis: { min: ranges.yaxis.from, max: ranges.yaxis.to }
				})
			);

			// don't fire event on the overview to prevent eternal loop

			overview.setSelection(ranges, true);
		});

		$("#overview").bind("plotselected", function (event, ranges) {
			plot.setSelection(ranges);
		});

		// Add the Flot version string to the footer

		$("#footer").prepend("Flot " + $.plot.version + " &ndash; ");
	});

	</script>
</head>
<body>

	<div id="header">
		<h2>Selection and zooming</h2>
	</div>

	<div id="content">

		<div class="demo-container">
			<div id="placeholder" class="demo-placeholder" style="float:left; width:650px;"></div>
			<div id="overview" class="demo-placeholder" style="float:right;width:160px; height:125px;"></div>
		</div>

		<p>Selection support makes it easy to construct flexible zooming schemes. With a few lines of code, the small overview plot to the right has been connected to the large plot. Try selecting a rectangle on either of them.</p>

	</div>

	<div id="footer">
		Copyright &copy; 2007 - 2014 IOLA and Ole Laursen
	</div>

</body>
</html>
