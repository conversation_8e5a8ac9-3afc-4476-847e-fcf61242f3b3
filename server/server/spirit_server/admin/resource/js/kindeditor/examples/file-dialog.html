<!doctype html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>fileDialog Examples</title>
		<link rel="stylesheet" href="../themes/default/default.css" />
		<script src="../kindeditor.js"></script>
		<script src="../lang/zh_CN.js"></script>
		<script>
			KindEditor.ready(function(K) {
				var editor = K.editor({
					allowFileManager : true
				});
				K('#insertfile').click(function() {
					editor.loadPlugin('insertfile', function() {
						editor.plugin.fileDialog({
							fileUrl : K('#url').val(),
							clickFn : function(url, title) {
								K('#url').val(url);
								editor.hideDialog();
							}
						});
					});
				});
			});
		</script>
	</head>
	<body>
		<input type="text" id="url" value="" /> <input type="button" id="insertfile" value="选择文件" />
	</body>
</html>
