---
title: Javascript tree with autoscroll
layout: page
js: examples/autoscroll.js
css: example.css
---

<p id="nav">
    <a href="06_autoescape.html">&laquo; Example 6</a>
    <a href="08_multiple_select.html" class="next">Example 8 &raquo;</a>
</p>

<h1>Example 7 - autoscroll</h1>
    
<div id="scroll-container">
    <div id="tree1"></div>
</div>

<p>
    This is an example of autoscroll. The tree will scroll automatically if you drag an item outside of the tree.
    <br />
    Autoscroll will work automatically. There is no option for it.
</p>
