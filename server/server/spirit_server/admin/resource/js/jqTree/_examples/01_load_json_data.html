---
title: Load json data in javascript tree
layout: page
js: examples/load_json_data.js
css: example.css
---

<p id="nav">
    <a href="../index.html">&laquo; Documentation</a>
    <a href="02_load_json_data_from_server.html" class="next">Example 2 &raquo;</a>
</p>

<h1>Example 1 - load json data</h1>

<div id="tree1"></div>

<p>
    In this example we load the data using the <strong>data</strong> option.
    As you can see, the data is an array of objects.
</p>
<ul>
    <li>The <strong>label</strong> property defines the label of the node.</li>
    <li>The <strong>id</strong> is the unique id of the node.</li>
    <li>The <strong>children</strong> property is an array of nodes.</li>
</ul>

{% highlight js %}
var data = [
    {
        label: 'node1', id: 1,
        children: [
            { label: 'child1', id: 2 },
            { label: 'child2', id: 3 }
        ]
    },
    {
        label: 'node2', id: 4,
        children: [
            { label: 'child3', id: 5 }
        ]
    }
];

$('#tree1').tree({
    data: data
});
{% endhighlight %}
