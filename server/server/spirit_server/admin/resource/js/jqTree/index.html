---
layout: frontpage
js: documentation.js
css: documentation.css
---

<div class="row">
    <div class="col-md-3">
        <div class="bs-sidebar hidden-print" id="menu" role="complementary">
            <a href="#jqtree"><h2>jqTree</h2></a>
            <ul class="nav bs-sidenav">
                {% assign level = 0 %}

                {% for entry in site.entries %}
                    {% if entry.section %}
                        {% if level == 1 %}
                                </ul>
                            </li>
                        {% endif %}
                        <li>
                            <a href="#{{ entry.name }}">{{ entry.title }}</a>
                            <ul class="nav">
                        {% assign level = 1 %}
                    {% else %}
                        <li><a href="#{{ entry.name }}">{{ entry.title }}</a></li>
                    {% endif %}
                {% endfor %}

                {% if level == 1 %}
                        </ul>
                    </li>
                {% endif %}
            </ul>
        </div>
    </div>
    <div class="col-md-9" id="main">
        {% for entry in site.entries %}
            {% if entry.hide_title %}
                <div id="{{ entry.name }}"></div>
            {% elsif entry.section %}
                <h3 id="{{ entry.name }}">{{ entry.title }}</h3>
            {% else %}
                <h4 id="{{ entry.name }}">{{ entry.title }}</h4>
            {% endif %}
            {{ entry.output }}
        {% endfor %}
    </div>
</div>
