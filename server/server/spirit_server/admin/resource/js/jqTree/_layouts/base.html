<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>{% if page.title %}{{ page.title }}{% else %}{{ site.title }}{% endif %}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="JqTree is a jQuery widget for displaying a tree structure in html">
        <link rel="stylesheet" href="{{ site.baseurl }}/static/bower_components/bootstrap/dist/css/bootstrap.min.css">
        <link rel="stylesheet" href="{{ site.baseurl }}/static/bower_components/bootstrap/dist/css/bootstrap-theme.min.css">
        <link rel="stylesheet" href="{{ site.baseurl }}/jqtree.css">
        <link rel="stylesheet" href="{{ site.baseurl }}/static/bower_components/pygments/css/monokai.css">
        <link rel="stylesheet" href="{{ site.baseurl }}/static/bower_components/fontawesome/css/font-awesome.min.css">

        <script>
          (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
          (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
          m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
          })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

          ga('create', 'UA-33742133-1', 'auto');
          ga('require', 'displayfeatures');
          ga('require', 'linkid', 'linkid.js');
          ga('send', 'pageview');
        </script>

        {% if page.css %}
        <link rel="stylesheet" href="{{ site.baseurl }}/static/{{ page.css }}">
        {% endif %}
    </head>
<body>
{{ content }}
</body>

<!--[if lt IE 9]>
    <script src="{{ site.baseurl }}/static/jquery-1.11.2.min.js"></script>
<![endif]-->
<!--[if gte IE 9]><!-->
    <script src="{{ site.baseurl }}/static/bower_components/jquery/dist/jquery.min.js"></script>
<!--<![endif]-->
<script src="{{ site.baseurl }}/tree.jquery.js"></script>
<script src="{{ site.baseurl }}/static/bower_components/bootstrap/dist/js/bootstrap.min.js"></script>
<script src="{{ site.baseurl }}/static/bower_components/jquery-mockjax/jquery.mockjax.js"></script>
<script src="{{ site.baseurl }}/static/example_data.js"></script>

{% if page.js %}
<script src="{{ site.baseurl }}/static/{{ page.js }}"></script>
{% endif %}

</html>
