[![Travis Status](https://secure.travis-ci.org/mbraak/jqTree.svg)](http://travis-ci.org/mbraak/jqTree) [![Coverage Status](https://img.shields.io/coveralls/mbraak/jqTree.svg)](https://coveralls.io/r/mbraak/jqTree)

[![Bower version](https://badge.fury.io/bo/jqtree.svg)](http://badge.fury.io/bo/jqtree) [![NPM version](https://badge.fury.io/js/jqtree.svg)](http://badge.fury.io/js/jqtree)

#jqTree

JqTree is a tree widget. Read more in the [documentation](http://mbraak.github.io/jqTree/).

![screenshot](https://raw.github.com/mbraak/jqTree/master/screenshot.png)

##Features

* Create a tree from JSON data
* Drag and drop
* Works on ie7+, firefox 3.6+, chrome and safari
* Written in Coffeescript

The project is hosted on [github](https://github.com/mbraak/jqTree), has a [test suite](http://mbraak.github.io/jqTree/test/test.html).

See index.html for the full documentation. The documentation is also on [github](http://mbraak.github.io/jqTree/)

##Thanks

The code for the mouse widget is heavily inspired by the mouse widget from jquery ui.
